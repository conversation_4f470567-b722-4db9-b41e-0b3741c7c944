#!/usr/bin/env python3
"""
Launch script for CS2 Betting Analysis Dashboard
Checks dependencies and starts the Streamlit app
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_logs_folder():
    """Check if logs folder exists and contains batch reports"""
    logs_path = "../logs"
    if not os.path.exists(logs_path):
        print(f"⚠️  Warning: Logs folder not found at {logs_path}")
        return False
    
    # Check for batch report files
    import glob
    batch_files = glob.glob(os.path.join(logs_path, "*batch_report*.txt"))
    if not batch_files:
        print(f"⚠️  Warning: No batch report files found in {logs_path}")
        print("   Make sure files contain 'batch_report' in their filename")
        return False
    
    print(f"✅ Found {len(batch_files)} batch report files:")
    for file in batch_files[:3]:  # Show first 3 files
        print(f"   - {os.path.basename(file)}")
    if len(batch_files) > 3:
        print(f"   ... and {len(batch_files) - 3} more files")
    
    return True

def launch_streamlit():
    """Launch the Streamlit app"""
    print("🚀 Starting CS2 Betting Analysis Dashboard...")
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")

def main():
    print("🎯 CS2 Betting Analysis Dashboard Launcher")
    print("=" * 50)
    
    # Check if we can import streamlit
    try:
        import streamlit
        print("✅ Streamlit is available")
    except ImportError:
        print("📦 Streamlit not found, installing dependencies...")
        if not install_requirements():
            print("❌ Failed to install dependencies. Exiting.")
            return
    
    # Check logs folder
    check_logs_folder()
    
    print("\n🌐 The dashboard will open in your web browser")
    print("📱 Use Ctrl+C to stop the server")
    print("-" * 50)
    
    # Launch the app
    launch_streamlit()

if __name__ == "__main__":
    main() 