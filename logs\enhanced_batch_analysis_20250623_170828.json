{"timestamp": "20250623_170828", "total_matches": 1, "successful_scrapes": 1, "failed_urls": [], "success_rate": 100.0, "processing_time": 19.**************, "min_confidence": 70.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "Copenhagen Wolves", "ranking": 102, "ensi_score": 1497, "winrate_10": 70.0, "winrate_30": 53.0, "current_shape": 117.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.15}, {"name": "Bielany", "nationality": "", "kd_ratio": 1.07}, {"name": "n1xen", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "Tapewaare", "nationality": "Norway", "kd_ratio": 0.87}, {"name": "matheos", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "Iberian Soul IS", "ranking": 43, "ensi_score": 1702, "winrate_10": 80.0, "winrate_30": 63.0, "current_shape": 117.0, "avg_kd": 0.94, "players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}]}, "h2h_record": "CPHW: 1 - Draws: 0 - IS: 0 (100% vs 0%)", "prediction": "Iberian Soul IS", "confidence": 66.18716666666666, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Iberian Soul IS -1.5 maps (2-0 win) (77.**************% confidence) | Alternative: MATCH_WINNER (76.**************%)", "key_factors": ["🆚 H2H record: Copenhagen Wolves leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: Copenhagen Wolves (17W-2L vs 2W-14L)", "🏆 Ranking advantage: Iberian Soul IS (#102 vs #43)", "📈 ENSI advantage: Iberian Soul IS (1497 vs 1702)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "CPHW: 1 - Draws: 0 - IS: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": Copenhagen Wolves 2:1 CPHW"], "team1_name": "CPHW", "team2_name": "IS", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 2 European Series #18", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 4}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.15, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.15}, {"name": "Bielany", "nationality": "", "kd_ratio": 1.07}, {"name": "n1xen", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "Tapewaare", "nationality": "Norway", "kd_ratio": 0.87}, {"name": "matheos", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}], "team1_avg_kd": 1.01, "team2_avg_kd": 0.94}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n            All CPHW and IS Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "PARIVISION\nPARIVISION\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Copenhagen Wolves\nCPHW\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "ENCE Academy\nENCE", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "47\n\n\n\n\n\n\nCopenhagen Wolves\nCPHW", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "23\n\n\n\n\n\n\nCopenhagen Wolves\nCPHW", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "55\n\n\n\n\n\n\nCopenhagen Wolves\nCPHW", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "52\n\n\n\n\n\n\nBetclic Apogee Esports\nBetclic", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "06\n\n\n\n\n\n\nCopenhagen Wolves\nCPHW", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 17, "losses": 2}, "team2_recent_form": {"wins": 2, "losses": 14}, "team1_opponents": ["<PERSON><PERSON>", "PAR", "Ast", "<PERSON><PERSON>", "ENC", "<PERSON><PERSON>", "Nex", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "PAR", "Ast", "<PERSON><PERSON>", "<PERSON><PERSON>", "Nex", "<PERSON>"], "team2_opponents": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Bet", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>n"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 27, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Copenhagen Wolves", "CPHW", "Iberian Soul", "Astrum", "Sashi Esport", "<PERSON><PERSON>", "PARIVISION", "HEROIC Academy", "Hero.A", "Dynamo Eclot", "Eclot", "Betclic Apogee Esports", "Betclic", "ENCE Academy", "ENCE.A", "Rebels Gaming", "REBELS", "Nexus Gaming", "Nexus", "BC.Game Esports", "BC.G", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>.", "Leo Team", "<PERSON>", "Ninjas in Pyjamas", "NiP"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Jackinho": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "Bielany": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "n1xen": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "Tapewaare": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "matheos": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"sausol": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "stadodo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "mopoz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "ALEX": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "dav1g": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Tapewaare", "player1_kd": 0.87, "player2": "dav1g", "player2_kd": 0.79, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs dav1g"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Iberian Soul IS to win match", "confidence": 76.**************, "reasoning": ["Team strength difference: 17.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Iberian Soul IS -1.5 maps (2-0 win)", "confidence": 77.**************, "reasoning": ["Based on team strength difference: 17.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 17.8 difference"]}, "CORRECT_SCORE": {"prediction": "Iberian Soul IS 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "<PERSON><PERSON><PERSON> most kills vs sausol", "confidence": 72.99999999999999, "reasoning": ["K/D comparison: 1.15 vs 1.02"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378179-copenhagen-wolves-cphw-vs-iberian-soul-is-gb-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 17:08:28.132618"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 150.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 90, "recommendations": [{"match": "Copenhagen Wolves vs Iberian Soul IS", "bet_type": "Map Handicap", "recommendation": "Iberian Soul IS -1.5 maps (2-0 win)", "confidence": 77.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 17.8"]}, {"match": "Copenhagen Wolves vs Iberian Soul IS", "bet_type": "Moneyline", "recommendation": "Iberian Soul IS to win match", "confidence": 76.**************, "value_rating": 0.***************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 17.8", "Primary betting market"]}, {"match": "Copenhagen Wolves vs Iberian Soul IS", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 17.8 difference"]}]}, "enhancement_stats": {"total_predictions": 1, "average_confidence": 66.18716666666666, "premium_bets": 0, "strong_bets": 0, "good_bets": 0, "lean_bets": 1, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 0.0, "good_pct": 0.0, "lean_pct": 100.0}}}