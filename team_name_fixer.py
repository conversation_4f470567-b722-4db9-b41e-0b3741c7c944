#!/usr/bin/env python3
"""
Team Name Fixer for Ensigame Scraper
Handles proper team name extraction and mapping
"""

import re
from typing import Dict, Optional, List

class TeamNameFixer:
    """Utility class for fixing and mapping team names"""
    
    def __init__(self):
        # Comprehensive team mappings
        self.team_mappings = {
            # Direct mappings
            'b8': 'B8',
            'og': 'OG Gaming', 
            'og-gaming': 'OG Gaming',
            'complexity-gaming': 'Complexity Gaming',
            'col': 'Complexity Gaming',
            'imperial-esports': 'Imperial Esports',
            'imperial': 'Imperial Esports',
            'nemiga-gaming': 'Nemiga Gaming', 
            'nemiga': 'Nemiga Gaming',
            'chinggis-warriors': 'Chinggis Warriors',
            'cw': 'Chinggis Warriors',
            'lynn-vision-gaming': 'Lynn Vision Gaming',
            'lynn-vision': 'Lynn Vision',
            'lvg': 'Lynn Vision',
            'wildcard-gaming': 'Wildcard Gaming',
            'wildcard': 'Wildcard Gaming',
            'nrg-esports': 'NRG Esports',
            'nrg': 'NRG Esports',
            'tyloo': 'TyLoo',
            'metizport': 'Metizport',
            'metz': 'Metizport',
            'fluxo': 'Fluxo',
            'heroic': 'Heroic',
            'legacy': 'Legacy',
            'betboom': 'BetBoom',
            'flyquest': 'FlyQuest',
            # NEW: Add all the team mappings from enhanced_automated_pipeline.py
            'team falcons falcons': 'Team Falcons',
            'made in brazil mibr': 'MIBR',
            'faze clan faze': 'FaZe Clan',
            '3dmax 3dmax': '3DMAX',
            'pain gaming pain': 'paiN Gaming',
            'lynn vision gaming lvg': 'Lynn Vision',
            'furia esports furia': 'FURIA Esports',
            'b8 b8': 'B8',
            'nemiga gaming nemiga': 'Nemiga Gaming',
            'm80 m80': 'M80',
            'tyloo tyloo': 'TyLoo',
            'heroic heroic': 'HEROIC',
            'heroic academy heroa': 'HEROIC Academy',
            'qmistry qmistry': 'Qmistry',
            'copenhagen wolves cphw': 'Copenhagen Wolves',
            'amkal amkal': 'Amkal',
            'inner circle esports ice': 'Inner Circle Esports',
            'akimbo esports akimbo': 'Akimbo Esports',
            'take flyte tf': 'Take Flyte',
            'vitaplur vplur': 'Vitaplur',
            'outfit 49 o49': 'Outfit 49',
            'monte monte': 'Monte',
            'leo team leo': 'LEO Team',
            'lag gaming lag': 'LAG Gaming',
            'wanted goons wg': 'Wanted Goons',
            'parivision parivision': 'Parivision',
            'partizan esports partizan': 'Partizan Esports',
            'boss boss': 'Boss',
            'subtick subtick': 'Subtick',
            'super evil gang seg': 'Super Evil Gang',
            'marca registrada marca': 'Marca Registrada',
            'gun5 esports gun5': 'Gun5 Esports',
            'marius marius': 'Marius',
            'party astronauts pa': 'Party Astronauts',
            'girl kissers kissers': 'Girl Kissers',
            'cybershoke esports cs': 'CYBERSHOKE',
            '9ine 9ine': '9ine',
            'rebels gaming rebels': 'Rebels Gaming',
            'konoecf kono': 'Konoecf',
            # Current match teams
            'ex sabre esports ex sabre': 'Ex Sabre Esports',
            'ex-sabre-esports-ex-sabre': 'Ex Sabre Esports',
            'copenhagen wolves cphw': 'Copenhagen Wolves',
            'copenhagen-wolves-cphw': 'Copenhagen Wolves',
            'energyultra eny': 'EnergyUltra',
            'energyultra-eny': 'EnergyUltra',
            'kubix esports kubix': 'Kubix Esports',
            'kubix-esports-kubix': 'Kubix Esports',
            'sinners esports sinners': 'Sinners Esports',
            'sinners-esports-sinners': 'Sinners Esports',
            'zero tenacity z10': 'Zero Tenacity',
            'zero-tenacity-z10': 'Zero Tenacity',
            'astrum astrum': 'Astrum',
            'astrum-astrum': 'Astrum',
            'roler coaster rc': 'Roler Coaster',
            'roler-coaster-rc': 'Roler Coaster',
            'northern lights nl': 'Northern Lights',
            'northern-lights-nl': 'Northern Lights',
            'energyultra eny epl s28 12 06 25': 'EnergyUltra',
            'energyultra eny': 'EnergyUltra',
            'energyultra-eny': 'EnergyUltra'
        }
        
        # URL pattern mappings for specific problematic URLs
        self.url_pattern_fixes = {
            r'1325638-b8-b8-vs-og-gaming': ('B8', 'OG Gaming'),
            r'b8-b8-vs-og-gaming-og': ('B8', 'OG Gaming'),
            r'imperial-esports-imperial-vs-nemiga-gaming-nemiga': ('Imperial Esports', 'Nemiga Gaming'),
            r'complexity-gaming-col-vs-fluxo-fluxo': ('Complexity Gaming', 'Fluxo'),
            r'lynn-vision-gaming-lvg-vs-wildcard-gaming-wildcard': ('Lynn Vision', 'Wildcard Gaming'),
            r'betboom-betboom-vs-flyquest-flyquest': ('BetBoom', 'FlyQuest'),
            r'heroic-heroic-vs-nrg-esports-nrg': ('Heroic', 'NRG Esports'),
            r'tyloo-tyloo-vs-metizport-metz': ('TyLoo', 'Metizport'),
            r'chinggis-warriors-cw-vs-legacy-legacy': ('Chinggis Warriors', 'Legacy'),
            # NEW: Add the problematic URLs we're seeing in the current logs
            r'team-falcons-falcons-vs-made-in-brazil-mibr': ('Team Falcons', 'MIBR'),
            r'heroic-academy-heroa-vs-qmistry-qmistry': ('HEROIC Academy', 'Qmistry'),
            r'amkal-amkal-vs-inner-circle-esports-ice': ('Amkal', 'Inner Circle Esports'),
            r'amkal-amkal-vs-copenhagen-wolves-cphw': ('Amkal', 'Copenhagen Wolves'),
            # CURRENT URL FIXES
            r'1374989-ex-sabre-esports-ex-sabre-vs-copenhagen-wolves-cphw': ('Ex Sabre Esports', 'Copenhagen Wolves'),
            r'1373565-energyultra-eny-vs-kubix-esports-kubix-untd21': ('EnergyUltra', 'Kubix Esports'),
            r'1375255-northern-lights-nl-vs-energyultra-eny-epl-s28': ('Northern Lights', 'EnergyUltra'),
            r'1354663-kappa-bar-kb-vs-northern-lights-nl-se-11-06-25': ('Kappa BAR', 'Northern Lights'),
            r'kappa-bar-kb-vs-northern-lights-nl': ('Kappa BAR', 'Northern Lights')
        }
    
    def fix_teams_from_url(self, url: str) -> Optional[Dict[str, str]]:
        """Fix team names extracted from URL - Enhanced with same logic as enhanced_automated_pipeline"""
        try:
            # First check direct pattern matches
            url_lower = url.lower()
            for pattern, (team1, team2) in self.url_pattern_fixes.items():
                if re.search(pattern, url_lower):
                    return {'team1_name': team1, 'team2_name': team2}
            
            # Remove the base URL part to work with the match path only
            url_path = url.split('/')[-1] if '/' in url else url
            
            # FIXED: Handle the specific URL pattern from the current error
            # URL: 1374989-ex-sabre-esports-ex-sabre-vs-copenhagen-wolves-cphw-gb-12-06-25
            
            # Pattern 1: NUMBER-TEAM1-PARTS-vs-TEAM2-PARTS-EXTRA
            # ENHANCED: Better regex to handle all tournament codes and dates
            match = re.search(r'^\d+-(.+?)-vs-(.+?)(?:-(?:untd21|blast|esea|cct|epg|d2us|gb)-\d{2}-\d{2}-\d{2}|$)', url_path, re.IGNORECASE)
            if match:
                team1_raw = match.group(1)
                team2_raw = match.group(2)
                
                # ENHANCED: Remove tournament codes and dates from team names
                team1_raw = re.sub(r'-(?:untd21|blast|esea|cct|epg|d2us|gb|epl|se)-?(?:s\d+)?-?\d*(?:-\d{2}-\d{2}-\d{2})?$', '', team1_raw, flags=re.IGNORECASE)
                team2_raw = re.sub(r'-(?:untd21|blast|esea|cct|epg|d2us|gb|epl|se)-?(?:s\d+)?-?\d*(?:-\d{2}-\d{2}-\d{2})?$', '', team2_raw, flags=re.IGNORECASE)
                
                # Clean team names
                team1 = self.clean_team_name(team1_raw)
                team2 = self.clean_team_name(team2_raw)
                
                if team1 != "Team" and team2 != "Team":
                    print(f"✅ Teams extracted from URL pattern 1: {team1} vs {team2}")
                    return {'team1_name': team1, 'team2_name': team2}
            
            # Pattern 2: Handle complex ensigame URL patterns with tournaments
            match = re.search(r'(\d+)-(.+)-blast-|(\d+)-(.+)-esea-|(\d+)-(.+)-cct-|(\d+)-(.+)-epg-|(\d+)-(.+)-d2us-|(\d+)-(.+)-untd21-', url_path, re.IGNORECASE)
            if match:
                # Get the teams part (everything between number and tournament)
                teams_part = match.group(2) or match.group(4) or match.group(6) or match.group(8) or match.group(10) or match.group(12)
                
                if teams_part and '-vs-' in teams_part:
                    team_parts = teams_part.split('-vs-')
                    if len(team_parts) == 2:
                        team1_raw = team_parts[0]
                        team2_raw = team_parts[1]
                        
                        # Clean and format team names using same logic
                        team1 = self.clean_team_name(team1_raw)
                        team2 = self.clean_team_name(team2_raw)
                        
                        if team1 != "Team" and team2 != "Team":
                            return {'team1_name': team1, 'team2_name': team2}
            
            # Pattern 3: Fallback pattern for simpler URLs
            match = re.search(r'([^/]+)-vs-([^/]+)', url_path)
            if match:
                team1 = self.clean_team_name(match.group(1))
                team2 = self.clean_team_name(match.group(2))
                if team1 != "Team" and team2 != "Team":
                    return {'team1_name': team1, 'team2_name': team2}
            
            return None
            
        except Exception as e:
            print(f"⚠️ URL team fixing failed: {e}")
            return None
    
    def clean_team_name(self, raw_name: str) -> str:
        """Clean and format team name from URL - Same logic as enhanced_automated_pipeline"""
        try:
            # Remove common prefixes/suffixes and clean up
            name = raw_name.replace('-', ' ')
            
            # Check against team mappings first
            name_lower = name.lower().strip()
            if name_lower in self.team_mappings:
                return self.team_mappings[name_lower]
            
            # FIXED: Remove duplicated team names (like "Sinners Esports Sinners" -> "Sinners Esports")
            words = name.split()
            if len(words) >= 3:
                # Check if last word is same as first word (case insensitive)
                if words[0].lower() == words[-1].lower():
                    words = words[:-1]  # Remove the last duplicated word
                # Check for pattern like "Team Name Team" -> "Team Name"
                elif len(words) == 3 and words[0].lower() == words[2].lower():
                    words = words[:2]
            
            # Default cleaning - capitalize each word
            cleaned_words = []
            for word in words:
                if word.upper() in ['MIBR', 'FURIA', 'HEROIC', 'TYLOO', 'M80', 'B8', 'FAZE', '3DMAX', 'CS', 'PA', 'LVG', 'ICE', 'CPHW', 'Z10']:
                    cleaned_words.append(word.upper())
                elif len(word) <= 3 and word.isalpha():
                    cleaned_words.append(word.upper())
                else:
                    cleaned_words.append(word.capitalize())
            
            return ' '.join(cleaned_words)
        except:
            return raw_name.replace('-', ' ').title()
    
    def extract_team_from_part(self, part: str) -> Optional[str]:
        """Extract team name from URL part"""
        try:
            part = part.lower().strip()
            
            # Direct mapping check
            if part in self.team_mappings:
                return self.team_mappings[part]
            
            # Handle duplicated names (like "b8-b8" -> "B8")
            if '-' in part:
                words = part.split('-')
                if len(words) >= 2 and words[0] == words[1]:
                    # Duplicated team name
                    if words[0] in self.team_mappings:
                        return self.team_mappings[words[0]]
                
                # Check for team-abbreviation pattern
                full_part = '-'.join(words)
                if full_part in self.team_mappings:
                    return self.team_mappings[full_part]
                
                # Try first word
                if words[0] in self.team_mappings:
                    return self.team_mappings[words[0]]
            
            # Fallback: use clean_team_name
            return self.clean_team_name(part)
            
        except Exception:
            return None
    
    def clean_extracted_name(self, name: str) -> str:
        """Clean team name extracted from page content"""
        if not name:
            return "Team"
        
        # Remove common artifacts
        artifacts = [
            r'^\d+\s+',  # Remove leading numbers
            r'\s+\d+\s*$',  # Remove trailing numbers
            r'\s+vs\s+.*',  # Remove everything after "vs"
            r'\s+VS\s+.*',  # Remove everything after "VS"
            r'\s+logo.*',  # Remove logo references
            r'\s+\d{4}-\d{2}-\d{2}.*',  # Remove dates
            r'\s+blast.*',  # Remove tournament info
            r'[<>{}()\[\]]'  # Remove brackets
        ]
        
        cleaned = name
        for pattern in artifacts:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # Remove duplicate words
        words = cleaned.split()
        if len(words) > 1 and words[0].lower() == words[1].lower():
            cleaned = words[0]
        
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # Apply direct mappings
        cleaned_lower = cleaned.lower()
        for key, value in self.team_mappings.items():
            if key == cleaned_lower or key in cleaned_lower:
                return value
        
        # Final cleanup
        if len(cleaned) <= 2:
            return "Team"
        
        return cleaned.strip()
    
    def fix_team_names_from_content(self, content: str) -> Optional[Dict[str, str]]:
        """Extract and fix team names from page content"""
        try:
            # Look for VS patterns in content
            vs_patterns = [
                r'([A-Za-z][A-Za-z\s\-\.]{2,20})\s+vs\s+([A-Za-z][A-Za-z\s\-\.]{2,20})',
                r'([A-Za-z][A-Za-z\s\-\.]{2,20})\s+VS\s+([A-Za-z][A-Za-z\s\-\.]{2,20})',
            ]
            
            for pattern in vs_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    team1 = self.clean_extracted_name(match.group(1))
                    team2 = self.clean_extracted_name(match.group(2))
                    
                    if (team1 and team2 and 
                        len(team1) > 2 and len(team2) > 2 and
                        team1.lower() != team2.lower() and
                        not team1[0].isdigit() and not team2[0].isdigit()):
                        return {'team1_name': team1, 'team2_name': team2}
            
            return None
            
        except Exception as e:
            print(f"⚠️ Content team fixing failed: {e}")
            return None

# Test the fixer
if __name__ == "__main__":
    fixer = TeamNameFixer()
    
    # Test problematic URLs
    test_urls = [
        "https://ensigame.com/matches/cs-2/1325638-b8-b8-vs-og-gaming-og-blast-03-06-25",
        "https://ensigame.com/matches/cs-2/1325642-imperial-esports-imperial-vs-nemiga-gaming-nemiga-blast-03-06-25",
        "https://ensigame.com/matches/cs-2/1366164-complexity-gaming-col-vs-fluxo-fluxo-blast-04-06-25"
    ]
    
    print("🧪 Testing Team Name Fixer")
    print("=" * 40)
    
    for url in test_urls:
        result = fixer.fix_teams_from_url(url)
        if result:
            print(f"✅ {url.split('/')[-1]}")
            print(f"   → {result['team1_name']} vs {result['team2_name']}")
        else:
            print(f"❌ {url.split('/')[-1]}")
        print() 