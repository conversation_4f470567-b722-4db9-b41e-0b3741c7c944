{"timestamp": "20250621_160224", "total_matches": 1, "successful_scrapes": 1, "failed_urls": [], "success_rate": 100.0, "processing_time": 21.***************, "min_confidence": 60.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "<PERSON><PERSON>", "ranking": 3, "ensi_score": 2025, "winrate_10": 70.0, "winrate_30": 67.0, "current_shape": 103.0, "avg_kd": 1.12, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.28}, {"name": "<PERSON>pp<PERSON>", "nationality": "", "kd_ratio": 1.18}, {"name": "Spinx", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.05}, {"name": "xertioN", "nationality": "", "kd_ratio": 0.99}]}, "team2": {"name": "Team Vitality Vitality", "ranking": 1, "ensi_score": 2217, "winrate_10": 90.0, "winrate_30": 97.0, "current_shape": 93.0, "avg_kd": 1.14, "players": [{"name": "ZywOo", "nationality": "", "kd_ratio": 1.6}, {"name": "ropz", "nationality": "", "kd_ratio": 1.28}, {"name": "mezii", "nationality": "", "kd_ratio": 1.11}, {"name": "flameZ", "nationality": "", "kd_ratio": 0.93}, {"name": "apEX", "nationality": "", "kd_ratio": 0.77}]}, "h2h_record": "MOUZ: 4 - Draws: 0 - Vitality: 14 (22% vs 78%)", "prediction": "Team Vitality Vitality", "confidence": 76.9961666666667, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - ZywOo most kills vs to<PERSON><PERSON> (80% confidence) | Alternative: MATCH_WINNER (77.**************%)", "key_factors": ["🆚 H2H record: Team Vitality Vitality leads (14-4) ⚡ (Some roster changes - moderate relevance)", "📈 ENSI advantage: Team Vitality Vitality (2025 vs 2217)", "⚡ Better current shape: <PERSON><PERSON> (103.0% vs 93.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 18, "h2h_record": "MOUZ: 4 - Draws: 0 - Vitality: 14 (22% vs 78%)", "team1_wins": 4, "team2_wins": 14, "draws": 0, "recent_matches": [": Team Vitality 3:0 Vitality", ": Team Vitality 2:1 Vitality", ": Team Vitality 3:2 Vitality", ": MOUZ 0:2 MOUZ"], "team1_name": "MOUZ", "team2_name": "Vitality", "team1_win_percentage": 22, "team2_win_percentage": 78, "competitive_encounters": 18, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-1", "tournament": "2025 Intel Extreme Masters Dallas", "h2h_history": [{"score": "3:0", "context": "recent_match"}, {"score": "3:0", "context": "recent_match"}, {"score": "3:0", "context": "recent_match"}, {"score": "3:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 4}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.28, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.28}, {"name": "<PERSON>pp<PERSON>", "nationality": "", "kd_ratio": 1.18}, {"name": "Spinx", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.05}, {"name": "xertioN", "nationality": "", "kd_ratio": 0.99}], "team2_players": [{"name": "ZywOo", "nationality": "", "kd_ratio": 1.6}, {"name": "ropz", "nationality": "", "kd_ratio": 1.28}, {"name": "mezii", "nationality": "", "kd_ratio": 1.11}, {"name": "flameZ", "nationality": "", "kd_ratio": 0.93}, {"name": "apEX", "nationality": "", "kd_ratio": 0.77}], "team1_avg_kd": 1.12, "team2_avg_kd": 1.14}, "recent_performance": {"team1_recent_matches": [{"score": "1:1", "result": "L", "opponent": "LIVE\n        \n\n\n\n\n\n\n\nVitality\n\n\n\n\n\n\n\napEX\n\nDan Madesclaire\n\n\n\n\nZywOo\n\nMathieu <PERSON>\n\n\n\n\nropz\n\nRobin <PERSON>ol\n\n\n\n\nflameZ\n\n<PERSON>\n\n\n\n\nmezi<PERSON>\n\n\n\n\n\n                    Create Prediction\n                \n\n                Betting Tips", "tournament": "Recent", "date": "Recent"}, {"score": "3:0", "result": "W", "opponent": "MOUZ\nMOUZ\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "MOUZ\nMOUZ\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "3:2", "result": "W", "opponent": "MOUZ\nMOUZ\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Team Vitality\nVitality\n\n\n\n\n\n\n\n\n\n\n\n            All MOUZ and Vitality Encounters", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:1", "result": "L", "opponent": "1 \n\n3rd Map\n\n\n\n\n\n\n\nBrollan\n\nLudvig Brolin\n\n\n\n\nto<PERSON><PERSON>ás\n\n\n\n\nSpinx\n\nLotan Giladi\n\n\n\n\nJimpphat\n\nJimi Salo\n\n\n\n\nxertioN\n\nDorian <PERSON>rman\n\n\n\n\n\n\n\n\nMOUZ", "tournament": "Recent", "date": "Recent"}, {"score": "0:3", "result": "L", "opponent": "46\n\n\n\n\n\n\nTeam Vitality\nVitality", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "02\n\n\n\n\n\n\nTeam Vitality\nVitality", "tournament": "Recent", "date": "Recent"}, {"score": "2:3", "result": "L", "opponent": "48\n\n\n\n\n\n\nTeam Vitality\nVitality", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "07\n\n\n\n\n\n\nMOUZ\nMOUZ", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 10, "losses": 16}, "team2_recent_form": {"wins": 16, "losses": 13}, "team1_opponents": ["LIV", "MOU", "MOU", "MOU", "Tea", "MOU", "Tea", "FaZ", "Tea", "Tea", "Nem", "Leg", "LIV", "LIV", "Tea", "MOU", "MOU", "MOU", "Leg", "FaZ", "MOU", "Tea", "FaZ", "Tea", "Nem", "Leg"], "team2_opponents": ["Pro", "<PERSON><PERSON>", "MOU", "Tea", "Tea", "Tea", "MOU", "Tea", "Leg", "<PERSON><PERSON>", "MOU", "MOU", "Tea", "Tea"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 22, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Team Vitality", "Vitality", "MOUZ", "Team Falcons", "Falcons", "The Mongolz", "Mongolz", "Legacy", "Team Spirit", "Spirit", "Virtus.Pro", "Aurora Gaming", "Aurora", "Team Liquid", "Liquid", "FaZe Clan", "FaZe", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "3DMAX", "Nemiga Gaming", "Nemiga"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"torzsi": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 128.0}, "Jimpphat": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 118.0}, "Spinx": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "Brollan": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 105.0}, "xertioN": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}}, "team2_form_trends": {"ZywOo": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 160.0}, "ropz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 128.0}, "mezii": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "flameZ": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "apEX": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 77.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "<PERSON><PERSON><PERSON>", "player1_kd": 1.28, "player2": "ZywOo", "player2_kd": 1.6, "impact": "VERY_HIGH", "description": "Battle of star players: <PERSON><PERSON><PERSON> vs ZywOo"}, {"type": "IGL_BATTLE", "player1": "xertioN", "player1_kd": 0.99, "player2": "apEX", "player2_kd": 0.77, "impact": "MEDIUM", "description": "Tactical battle: xertioN vs apEX"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"torzsi": {"team": "<PERSON><PERSON>", "kd_ratio": 1.28, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "ZywOo": {"team": "Team Vitality Vitality", "kd_ratio": 1.6, "impact_level": "HIGH", "recent_form": "IMPROVING"}, "ropz": {"team": "Team Vitality Vitality", "kd_ratio": 1.28, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Team Vitality Vitality to win match", "confidence": 77.**************, "reasoning": ["Team strength difference: 14.9", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Mouz Mouz +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 65.11366666666665, "reasoning": ["Based on team strength difference: 14.9"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 14.9 difference"]}, "CORRECT_SCORE": {"prediction": "Team Vitality Vitality 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "ZywOo most kills vs to<PERSON><PERSON>", "confidence": 80, "reasoning": ["K/D comparison: 1.28 vs 1.60"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1369183-mouz-mouz-vs-team-vitality-vitality-blast-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-21 16:02:24.285416"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 250.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 100, "recommendations": [{"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Player Props", "recommendation": "ZywOo most kills vs to<PERSON><PERSON>", "confidence": 80, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.28 vs 1.60"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Moneyline", "recommendation": "Team Vitality Vitality to win match", "confidence": 77.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 14.9", "Primary betting market"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Correct Score", "recommendation": "Team Vitality Vitality 2-1", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength and historical patterns"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Map Handicap", "recommendation": "Mouz Mouz +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 65.11366666666665, "value_rating": 0.30227333333333295, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 14.9"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 14.9 difference"]}]}, "enhancement_stats": {"total_predictions": 1, "average_confidence": 76.9961666666667, "premium_bets": 0, "strong_bets": 0, "good_bets": 1, "lean_bets": 0, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 0.0, "good_pct": 100.0, "lean_pct": 0.0}}}