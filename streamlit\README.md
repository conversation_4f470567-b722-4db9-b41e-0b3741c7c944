# CS2 Betting Analysis Dashboard

A Streamlit web application for visualizing CS2 betting predictions and analysis from batch report files.

## Features

🎯 **Interactive Dashboard**
- View betting predictions from multiple batch report files
- Confidence distribution charts
- Performance metrics over time
- Match-by-match analysis

📊 **Key Metrics**
- Success rates
- Confidence levels
- Bet distribution (Premium, Strong, Good, Lean)
- Team rankings and predictions

📈 **Analytics**
- Multi-report comparison
- Performance trends
- Historical analysis

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Run the Streamlit app:
```bash
streamlit run app.py
```

## Usage

The app will automatically detect all files with "batch_report" in their filename from the `../logs` directory. You can:

- Select different report files from the sidebar
- View detailed match predictions
- Analyze confidence distributions
- Compare performance across multiple reports

## File Structure

```
streamlit/
├── app.py              # Main Streamlit application
├── requirements.txt    # Python dependencies
└── README.md          # This file
```

## Features

- **Dynamic File Detection**: Automatically finds new batch report files
- **Multiple File Support**: Switch between different reports
- **Interactive Visualizations**: Charts and graphs using Plotly
- **Responsive Design**: Works on desktop and mobile
- **Real-time Updates**: Refresh to see new reports

## Data Source

The app reads from batch report files located in `../logs/` that contain "batch_report" in their filename. These files are generated by the CS2 betting analysis system and contain:

- Match predictions
- Team rankings
- Confidence scores
- Betting recommendations
- Performance statistics

## Troubleshooting

If you don't see any reports:
1. Make sure batch report files exist in the `../logs` directory
2. Ensure files have "batch_report" somewhere in their filename
3. Check that files are properly formatted text files

## Support

This dashboard is designed to work with the CS2 Betting Analysis System's batch report output format. 