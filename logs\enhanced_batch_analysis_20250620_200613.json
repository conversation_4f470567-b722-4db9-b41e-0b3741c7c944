{"timestamp": "20250620_200613", "total_matches": 2, "successful_scrapes": 2, "failed_urls": [], "success_rate": 100.0, "processing_time": 42.**************, "min_confidence": 65.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "<PERSON><PERSON>", "ranking": 3, "ensi_score": 2025, "winrate_10": 70.0, "winrate_30": 67.0, "current_shape": 103.0, "avg_kd": 1.12, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.28}, {"name": "<PERSON>pp<PERSON>", "nationality": "", "kd_ratio": 1.18}, {"name": "Spinx", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.05}, {"name": "xertioN", "nationality": "", "kd_ratio": 0.99}]}, "team2": {"name": "Team Vitality Vitality", "ranking": 1, "ensi_score": 2217, "winrate_10": 90.0, "winrate_30": 97.0, "current_shape": 93.0, "avg_kd": 1.14, "players": [{"name": "ZywOo", "nationality": "", "kd_ratio": 1.6}, {"name": "ropz", "nationality": "", "kd_ratio": 1.28}, {"name": "mezii", "nationality": "", "kd_ratio": 1.11}, {"name": "flameZ", "nationality": "", "kd_ratio": 0.93}, {"name": "apEX", "nationality": "", "kd_ratio": 0.77}]}, "h2h_record": "MOUZ: 4 - Draws: 0 - Vitality: 14 (22% vs 78%)", "prediction": "Team Vitality Vitality", "confidence": 76.9961666666667, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - ZywOo most kills vs to<PERSON><PERSON> (80% confidence) | Alternative: MATCH_WINNER (77.**************%)", "key_factors": ["🆚 H2H record: Team Vitality Vitality leads (14-4) ⚡ (Some roster changes - moderate relevance)", "📈 ENSI advantage: Team Vitality Vitality (2025 vs 2217)", "⚡ Better current shape: <PERSON><PERSON> (103.0% vs 93.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 18, "h2h_record": "MOUZ: 4 - Draws: 0 - Vitality: 14 (22% vs 78%)", "team1_wins": 4, "team2_wins": 14, "draws": 0, "recent_matches": [": Team Vitality 3:0 Vitality", ": Team Vitality 2:1 Vitality", ": Team Vitality 3:2 Vitality", ": MOUZ 0:2 MOUZ"], "team1_name": "MOUZ", "team2_name": "Vitality", "team1_win_percentage": 22, "team2_win_percentage": 78, "competitive_encounters": 18, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-1", "tournament": "2025 Intel Extreme Masters Dallas", "h2h_history": [{"score": "3:0", "context": "recent_match"}, {"score": "3:0", "context": "recent_match"}, {"score": "3:0", "context": "recent_match"}, {"score": "3:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 4}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.28, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.28}, {"name": "<PERSON>pp<PERSON>", "nationality": "", "kd_ratio": 1.18}, {"name": "Spinx", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.05}, {"name": "xertioN", "nationality": "", "kd_ratio": 0.99}], "team2_players": [{"name": "ZywOo", "nationality": "", "kd_ratio": 1.6}, {"name": "ropz", "nationality": "", "kd_ratio": 1.28}, {"name": "mezii", "nationality": "", "kd_ratio": 1.11}, {"name": "flameZ", "nationality": "", "kd_ratio": 0.93}, {"name": "apEX", "nationality": "", "kd_ratio": 0.77}], "team1_avg_kd": 1.12, "team2_avg_kd": 1.14}, "recent_performance": {"team1_recent_matches": [{"score": "3:0", "result": "W", "opponent": "MOUZ\nMOUZ\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "MOUZ\nMOUZ\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "3:2", "result": "W", "opponent": "MOUZ\nMOUZ\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Team Vitality\nVitality\n\n\n\n\n\n\n\n\n\n\n\n            All MOUZ and Vitality Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "MOUZ\nMOUZ\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:3", "result": "L", "opponent": "46\n\n\n\n\n\n\nTeam Vitality\nVitality", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "02\n\n\n\n\n\n\nTeam Vitality\nVitality", "tournament": "Recent", "date": "Recent"}, {"score": "2:3", "result": "L", "opponent": "48\n\n\n\n\n\n\nTeam Vitality\nVitality", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "07\n\n\n\n\n\n\nMOUZ\nMOUZ", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "21\n\n\n\n\n\n\nTeam Spirit\nSpirit", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 10, "losses": 13}, "team2_recent_form": {"wins": 16, "losses": 10}, "team1_opponents": ["MOU", "MOU", "MOU", "Tea", "MOU", "Tea", "FaZ", "Tea", "Tea", "Nem", "Leg", "Tea", "MOU", "MOU", "MOU", "Leg", "FaZ", "MOU", "Tea", "FaZ", "Tea", "Nem", "Leg"], "team2_opponents": ["Pro", "Tea", "Tea", "Tea", "MOU", "Tea", "Leg", "<PERSON><PERSON>", "MOU", "MOU", "Tea", "Tea"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 22, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Team Vitality", "Vitality", "MOUZ", "Team Falcons", "Falcons", "The Mongolz", "Mongolz", "Legacy", "Team Liquid", "Liquid", "Team Spirit", "Spirit", "Aurora Gaming", "Aurora", "FaZe Clan", "FaZe", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Virtus.Pro", "3DMAX", "Nemiga Gaming", "Nemiga"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"torzsi": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 128.0}, "Jimpphat": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 118.0}, "Spinx": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "Brollan": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 105.0}, "xertioN": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}}, "team2_form_trends": {"ZywOo": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 160.0}, "ropz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 128.0}, "mezii": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "flameZ": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "apEX": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 77.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "<PERSON><PERSON><PERSON>", "player1_kd": 1.28, "player2": "ZywOo", "player2_kd": 1.6, "impact": "VERY_HIGH", "description": "Battle of star players: <PERSON><PERSON><PERSON> vs ZywOo"}, {"type": "IGL_BATTLE", "player1": "xertioN", "player1_kd": 0.99, "player2": "apEX", "player2_kd": 0.77, "impact": "MEDIUM", "description": "Tactical battle: xertioN vs apEX"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"torzsi": {"team": "<PERSON><PERSON>", "kd_ratio": 1.28, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "ZywOo": {"team": "Team Vitality Vitality", "kd_ratio": 1.6, "impact_level": "HIGH", "recent_form": "IMPROVING"}, "ropz": {"team": "Team Vitality Vitality", "kd_ratio": 1.28, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Team Vitality Vitality to win match", "confidence": 77.**************, "reasoning": ["Team strength difference: 14.9", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Mouz Mouz +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 65.11366666666665, "reasoning": ["Based on team strength difference: 14.9"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 14.9 difference"]}, "CORRECT_SCORE": {"prediction": "Team Vitality Vitality 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "ZywOo most kills vs to<PERSON><PERSON>", "confidence": 80, "reasoning": ["K/D comparison: 1.28 vs 1.60"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1369183-mouz-mouz-vs-team-vitality-vitality-blast-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 20:05:51.297209"}}, "page_content": ""}, {"team1": {"name": "THE Mongolz Mongolz", "ranking": 4, "ensi_score": 1959, "winrate_10": 60.0, "winrate_30": 57.0, "current_shape": 103.0, "avg_kd": 1.13, "players": [{"name": "senzu", "nationality": "", "kd_ratio": 1.25}, {"name": "910", "nationality": "", "kd_ratio": 1.21}, {"name": "m<PERSON>ho", "nationality": "", "kd_ratio": 1.13}, {"name": "bLitz", "nationality": "", "kd_ratio": 1.08}, {"name": "Techno4k", "nationality": "", "kd_ratio": 0.98}]}, "team2": {"name": "paiN Gaming", "ranking": 20, "ensi_score": 1783, "winrate_10": 70.0, "winrate_30": 40.0, "current_shape": 130.0, "avg_kd": 1.09, "players": [{"name": "nqz", "nationality": "", "kd_ratio": 1.27}, {"name": "Snow", "nationality": "", "kd_ratio": 1.14}, {"name": "dgt", "nationality": "", "kd_ratio": 1.12}, {"name": "dav1deuS", "nationality": "", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}]}, "h2h_record": "Mongolz: 1 - Draws: 0 - paiN: 2 (33% vs 67%)", "prediction": "THE Mongolz Mongolz", "confidence": 48.068250000000006, "betting_advice": "🟢 BEST BET: TOTAL_MAPS - OVER 2.5 maps (3 maps likely) (76.9545% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🆚 H2H record: paiN Gaming leads (2-1) 🎯 (Similar rosters - high relevance)", "🏆 Ranking advantage: THE Mongolz Mongolz (#4 vs #20)", "📈 ENSI advantage: THE Mongolz Mongolz (1959 vs 1783)", "⚡ Better current shape: paiN Gaming (103.0% vs 130.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 3, "h2h_record": "Mongolz: 1 - Draws: 0 - paiN: 2 (33% vs 67%)", "team1_wins": 1, "team2_wins": 2, "draws": 0, "recent_matches": [": paiN Gaming 1:2 paiN", ": The Mongolz 1:2 Mongolz", ": paiN Gaming 2:1 paiN"], "team1_name": "Mongolz", "team2_name": "paiN", "team1_win_percentage": 33, "team2_win_percentage": 67, "competitive_encounters": 3, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-1", "tournament": "2025 PGL on FiRe Buenos Aires", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.25, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "senzu", "nationality": "", "kd_ratio": 1.25}, {"name": "910", "nationality": "", "kd_ratio": 1.21}, {"name": "m<PERSON>ho", "nationality": "", "kd_ratio": 1.13}, {"name": "bLitz", "nationality": "", "kd_ratio": 1.08}, {"name": "Techno4k", "nationality": "", "kd_ratio": 0.98}], "team2_players": [{"name": "nqz", "nationality": "", "kd_ratio": 1.27}, {"name": "Snow", "nationality": "", "kd_ratio": 1.14}, {"name": "dgt", "nationality": "", "kd_ratio": 1.12}, {"name": "dav1deuS", "nationality": "", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}], "team1_avg_kd": 1.13, "team2_avg_kd": 1.09}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "The Mongolz\nMongolz\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "paiN Gaming\npaiN\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "The Mongolz\nMongolz\n\n\n\n\n\n\n\n\n\n\n\n            All Mongolz and paiN Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "The Mongolz\nMongolz\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "G2 Esports\nG2\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "06\n\n\n\n\n\n\npaiN Gaming\npaiN", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "39\n\n\n\n\n\n\nThe Mongolz\nMongolz", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "09\n\n\n\n\n\n\npaiN Gaming\npaiN", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "25\n\n\n\n\n\n\nFaZe Clan\nFaZe", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "15\n\n\n\n\n\n\nThe Mongolz\nMongolz", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 10}, "team2_recent_form": {"wins": 13, "losses": 10}, "team1_opponents": ["The", "pai", "The", "The", "FaZ", "<PERSON><PERSON>", "Tea", "pai", "Nem", "pai", "The", "The", "pai", "pai", "Tea", "The", "FaZ", "<PERSON><PERSON>", "Tea", "pai", "Nem", "pai"], "team2_opponents": ["Pro", "pai", "The", "pai", "FaZ", "The", "The", "The", "FUR"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 20, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["paiN Gaming", "paiN", "The Mongolz", "Mongolz", "FURIA Esports", "FURIA", "G2 Esports", "MOUZ", "Lynn Vision Gaming", "LVG", "Made in Brazil", "MIBR", "FaZe Clan", "FaZe", "Team Liquid", "Liquid", "Virtus.Pro", "3DMAX", "Nemiga Gaming", "Nemiga"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"senzu": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 125.0}, "910": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "mzinho": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "bLitz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "Techno4k": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}}, "team2_form_trends": {"nqz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 127.0}, "Snow": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 113.99999999999999}, "dgt": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "dav1deuS": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "biguzera": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "senzu", "player1_kd": 1.25, "player2": "nqz", "player2_kd": 1.27, "impact": "VERY_HIGH", "description": "Battle of star players: se<PERSON>u vs nqz"}, {"type": "IGL_BATTLE", "player1": "Techno4k", "player1_kd": 0.98, "player2": "<PERSON><PERSON><PERSON>", "player2_kd": 0.95, "impact": "MEDIUM", "description": "Tactical battle: Techno4k vs biguzera"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"senzu": {"team": "THE Mongolz Mongolz", "kd_ratio": 1.25, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "910": {"team": "THE Mongolz Mongolz", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "nqz": {"team": "paiN Gaming", "kd_ratio": 1.27, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "THE Mongolz Mongolz to win match", "confidence": 51.568250000000006, "reasoning": ["Team strength difference: 1.0", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "paiN Gaming +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 1.0"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 76.9545, "reasoning": ["Team strength analysis: 1.0 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "paiN Gaming first map", "confidence": 66.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: se<PERSON><PERSON> vs nqz", "confidence": 55, "reasoning": ["K/D comparison: 1.25 vs 1.27"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1369184-the-mongolz-mongolz-vs-pain-gaming-pain-blast-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 20:06:13.790530"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 350.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 100, "recommendations": [{"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Player Props", "recommendation": "ZywOo most kills vs to<PERSON><PERSON>", "confidence": 80, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.28 vs 1.60"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Moneyline", "recommendation": "Team Vitality Vitality to win match", "confidence": 77.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 14.9", "Primary betting market"]}, {"match": "THE Mongolz Mongolz vs paiN Gaming", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 76.9545, "value_rating": 0.5390899999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 1.0 difference"]}, {"match": "THE Mongolz Mongolz vs paiN Gaming", "bet_type": "Map Handicap", "recommendation": "paiN Gaming +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 1.0"]}, {"match": "THE Mongolz Mongolz vs paiN Gaming", "bet_type": "Total Rounds", "recommendation": "paiN Gaming first map", "confidence": 66.5, "value_rating": 0.33000000000000007, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Map Handicap", "recommendation": "Mouz Mouz +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 65.11366666666665, "value_rating": 0.30227333333333295, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 14.9"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 14.9 difference"]}]}, "enhancement_stats": {"total_predictions": 2, "average_confidence": 62.53220833333335, "premium_bets": 0, "strong_bets": 0, "good_bets": 1, "lean_bets": 0, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 0.0, "good_pct": 50.0, "lean_pct": 0.0}}}