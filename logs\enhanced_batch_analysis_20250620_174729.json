{"timestamp": "20250620_174729", "total_matches": 15, "successful_scrapes": 15, "failed_urls": [], "success_rate": 100.0, "processing_time": 325.**************, "min_confidence": 65.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "FaZe Clan", "ranking": 7, "ensi_score": 1893, "winrate_10": 60.0, "winrate_30": 53.0, "current_shape": 107.0, "avg_kd": 1.03, "players": [{"name": "s1mple", "nationality": "", "kd_ratio": 1.17}, {"name": "EliGE", "nationality": "United States", "kd_ratio": 1.16}, {"name": "rain", "nationality": "Norway", "kd_ratio": 1.08}, {"name": "frozen", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.75}]}, "team2": {"name": "THE Mongolz Mongolz", "ranking": 5, "ensi_score": 1946, "winrate_10": 60.0, "winrate_30": 57.0, "current_shape": 103.0, "avg_kd": 1.13, "players": [{"name": "senzu", "nationality": "", "kd_ratio": 1.25}, {"name": "910", "nationality": "", "kd_ratio": 1.21}, {"name": "m<PERSON>ho", "nationality": "", "kd_ratio": 1.13}, {"name": "bLitz", "nationality": "", "kd_ratio": 1.08}, {"name": "Techno4k", "nationality": "", "kd_ratio": 0.98}]}, "h2h_record": "FaZe: 1 - Draws: 0 - Mongolz: 1 (50% vs 50%)", "prediction": "THE Mongolz Mongolz", "confidence": 62.11249999999997, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - FaZe Clan +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (71.92500000000001% confidence) | Alternative: MATCH_WINNER (62.11249999999997%)", "key_factors": ["🆚 H2H record: Even (1-1) 🎯 (Similar rosters - high relevance)", "📈 ENSI advantage: THE Mongolz Mongolz (1893 vs 1946)"], "additional_factors": {"h2h_data": {"previous_encounters": 2, "h2h_record": "FaZe: 1 - Draws: 0 - Mongolz: 1 (50% vs 50%)", "team1_wins": 1, "team2_wins": 1, "draws": 0, "recent_matches": [": The Mongolz 0:2 Mongolz", ": FaZe Clan 0:2 FaZe"], "team1_name": "FaZe", "team2_name": "Mongolz", "team1_win_percentage": 50, "team2_win_percentage": 50, "competitive_encounters": 2, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-1", "tournament": "2025 BLAST.tv Austin Major", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.17, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "s1mple", "nationality": "", "kd_ratio": 1.17}, {"name": "EliGE", "nationality": "United States", "kd_ratio": 1.16}, {"name": "rain", "nationality": "Norway", "kd_ratio": 1.08}, {"name": "frozen", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.75}], "team2_players": [{"name": "senzu", "nationality": "", "kd_ratio": 1.25}, {"name": "910", "nationality": "", "kd_ratio": 1.21}, {"name": "m<PERSON>ho", "nationality": "", "kd_ratio": 1.13}, {"name": "bLitz", "nationality": "", "kd_ratio": 1.08}, {"name": "Techno4k", "nationality": "", "kd_ratio": 0.98}], "team1_avg_kd": 1.03, "team2_avg_kd": 1.13}, "recent_performance": {"team1_recent_matches": [{"score": "0:0", "result": "L", "opponent": "LIVE\n        \n\n\n\n\n\n\n\nMongolz\n\n\n\n\n\n\n\nbLitz\n\nGaridmagnai Byambasuren\n\n\n\n\nTechno4k\n\nMunkhbold Sodbayar\n\n\n\n\nsenzu\n\nAzbayar Munkhbold\n\n\n\n\nmzinho\n\nAyush Batbold\n\n\n\n\n910\n\nUsukhbayar Banzragch\n\n\n\n\n\n                    Create Prediction\n                \n\n                Betting Tips", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "FaZe Clan\nFaZe\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "The Mongolz\nMongolz\n\n\n\n\n\n\n\n\n\n\n\n            All FaZe and Mongolz Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "FaZe Clan\nFaZe\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "FaZe Clan\nFaZe\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:0", "result": "L", "opponent": "1 \n\n1st Map\n\n\n\n\n\n\n\n<PERSON><PERSON>\n\n\n\n\ns1<PERSON>\n\n<PERSON><PERSON><PERSON><PERSON>v\n\n\n\n\nrain\n\n<PERSON><PERSON><PERSON>\n\n\n\n\nEliGE\n\n<PERSON>\n\n\n\n\nfrozen\n\n<PERSON>ký\n\n\n\n\n\n\n\n\nFaZe", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "16\n\n\n\n\n\n\nThe Mongolz\nMongolz", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "46\n\n\n\n\n\n\nFaZe Clan\nFaZe", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "56\n\n\n\n\n\n\nLegacy\nLegacy", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "30\n\n\n\n\n\n\nMOUZ\nMOUZ", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 11, "losses": 12}, "team2_recent_form": {"wins": 10, "losses": 12}, "team1_opponents": ["LIV", "FaZ", "The", "FaZ", "FaZ", "Mad", "<PERSON><PERSON>", "Tea", "FUR", "LIV", "LIV", "The", "FaZ", "FUR", "Mad", "FaZ", "FaZ", "Mad", "<PERSON><PERSON>", "Tea", "FUR"], "team2_opponents": ["<PERSON><PERSON>", "FaZ", "The", "FaZ", "Leg", "MOU", "<PERSON><PERSON>", "FaZ", "The", "The", "The"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["The Mongolz", "Mongolz", "FaZe Clan", "FaZe", "MOUZ", "Aurora Gaming", "Aurora", "Team Liquid", "Liquid", "Made in Brazil", "MIBR", "Legacy", "G2 Esports", "Lynn Vision Gaming", "LVG", "FURIA Esports", "FURIA"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"s1mple": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 117.0}, "EliGE": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "rain": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "frozen": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Karrigan": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 75.0}}, "team2_form_trends": {"senzu": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 125.0}, "910": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "mzinho": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "bLitz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "Techno4k": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON>", "player1_kd": 0.75, "player2": "Techno4k", "player2_kd": 0.98, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON> vs Techno4k"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"senzu": {"team": "THE Mongolz Mongolz", "kd_ratio": 1.25, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "910": {"team": "THE Mongolz Mongolz", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "THE Mongolz Mongolz to win match", "confidence": 62.11249999999997, "reasoning": ["Team strength difference: 8.1", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "FaZe Clan +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 71.92500000000001, "reasoning": ["Based on team strength difference: 8.1"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 8.1 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: s1<PERSON> vs senzu", "confidence": 55, "reasoning": ["K/D comparison: 1.17 vs 1.25"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1369181-faze-clan-faze-vs-the-mongolz-mongolz-blast-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:42:23.526838"}}, "page_content": ""}, {"team1": {"name": "MIBR Academy MIBR Acad", "ranking": 299, "ensi_score": 1337, "winrate_10": 30.0, "winrate_30": 40.0, "current_shape": 90.0, "avg_kd": 1.01, "players": [{"name": "card", "nationality": "", "kd_ratio": 1.17}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.1}, {"name": "<PERSON>ez", "nationality": "", "kd_ratio": 1.02}, {"name": "brn$", "nationality": "", "kd_ratio": 0.93}, {"name": "stormzyn", "nationality": "", "kd_ratio": 0.83}]}, "team2": {"name": "Elevate ELV CCT SA 20 06 25", "ranking": 102, "ensi_score": 1497, "winrate_10": 30.0, "winrate_30": 50.0, "current_shape": 80.0, "avg_kd": 1.1, "players": [{"name": "lash", "nationality": "", "kd_ratio": 1.18}, {"name": "skr", "nationality": "", "kd_ratio": 1.15}, {"name": "zede", "nationality": "", "kd_ratio": 1.14}, {"name": "short", "nationality": "", "kd_ratio": 1.03}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.99}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Elevate ELV CCT SA 20 06 25", "confidence": 52.**************, "betting_advice": "🟢 BEST BET: TOTAL_MAPS - OVER 2.5 maps (3 maps likely) (76.38688888888889% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🏆 Ranking advantage: Elevate ELV CCT SA 20 06 25 (#299 vs #102)", "📈 ENSI advantage: Elevate ELV CCT SA 20 06 25 (1337 vs 1497)", "⚡ Better current shape: MIBR Academy MIBR Acad (90.0% vs 80.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 3 South American Series #1", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.17, "team2_odds": 1.02, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "card", "nationality": "", "kd_ratio": 1.17}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.1}, {"name": "<PERSON>ez", "nationality": "", "kd_ratio": 1.02}, {"name": "brn$", "nationality": "", "kd_ratio": 0.93}, {"name": "stormzyn", "nationality": "", "kd_ratio": 0.83}], "team2_players": [{"name": "lash", "nationality": "", "kd_ratio": 1.18}, {"name": "skr", "nationality": "", "kd_ratio": 1.15}, {"name": "zede", "nationality": "", "kd_ratio": 1.14}, {"name": "short", "nationality": "", "kd_ratio": 1.03}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.99}], "team1_avg_kd": 1.01, "team2_avg_kd": 1.1}, "recent_performance": {"team1_recent_matches": [{"score": "0:0", "result": "L", "opponent": "LIVE\n        \n\n\n\n\n\n\n\neLv\n\n\n\n\n\n\n\nshort\n\n<PERSON>\n\n\n\n\nzede\n\nVinicius Reis Cruz Batista\n\n\n\n\ndiozera\n\nDiogo <PERSON>unes\n\n\n\n\nlash\n\nlash\n\n\n\n\nskr\n\n<PERSON><PERSON> Ka<PERSON>u\n\n\n\n\n\n                    Create Prediction\n                \n\n                Betting Tips", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "MIBR Academy\nMIBR Acad\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "MIBR Academy\nMIBR Acad\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "RED Canids Academy\nRC Acad\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "MIBR Academy\nMIBR Acad\n\n\n\n\n\n\n\n\n\n\n                    All MIBR Acad Encounters\n                \n\n\n\n\nElevate\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:0", "result": "L", "opponent": "<PERSON>\n\n\n\n\nRenan Z<PERSON>\n\n\n\n\n\n\n\n\nMIBR Acad", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "35\n\n\n\n\n\n\nGame Hunters\nGH", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "29\n\n\n\n\n\n\nPlayers\nPlayers", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "24\n\n\n\n\n\n\nMIBR Academy\nMIBR Acad", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "GG", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 10, "losses": 11}, "team2_recent_form": {"wins": 8, "losses": 11}, "team1_opponents": ["LIV", "MIB", "MIB", "RED", "MIB", "Ele", "Ele", "Ele", "LIV", "LIV", "Ele", "MIB", "MIB", "MIB", "RED", "MIB", "Ele", "Ele", "Ele"], "team2_opponents": ["Bru", "MIB", "Gam", "Pla", "MIB", "Pla", "Fla", "<PERSON><PERSON>", "Ele", "ODD"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 15, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Players", "Flamengo Esports", "Flamengo", "Game Hunters", "MIBR Academy", "MIBR Acad", "2GAME", "2Game", "RED Canids Academy", "RC Acad", "Nitro.GG", "Elevate", "eLv", "<PERSON>", "ODDIK"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"card": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 117.0}, "Renan Zin": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 110.00000000000001}, "perez": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "brn$": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "stormzyn": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 83.0}}, "team2_form_trends": {"lash": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 118.0}, "skr": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "zede": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 113.99999999999999}, "short": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 103.0}, "diozera": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "stormzyn", "player1_kd": 0.83, "player2": "<PERSON><PERSON><PERSON>", "player2_kd": 0.99, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs dioz<PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Elevate ELV CCT SA 20 06 25 to win match", "confidence": 52.**************, "reasoning": ["Team strength difference: 1.6", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "MIBR Academy MIBR Acad +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 1.6"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 76.38688888888889, "reasoning": ["Team strength analysis: 1.6 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: card vs lash", "confidence": 55, "reasoning": ["K/D comparison: 1.17 vs 1.18"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387720-mibr-academy-mibr-acad-vs-elevate-elv-cct-sa-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:42:45.500329"}}, "page_content": ""}, {"team1": {"name": "Team Next Level TNL", "ranking": 36, "ensi_score": 1731, "winrate_10": 70.0, "winrate_30": 73.0, "current_shape": 97.0, "avg_kd": 1.11, "players": [{"name": "cairne", "nationality": "", "kd_ratio": 1.27}, {"name": "Flierax", "nationality": "", "kd_ratio": 1.16}, {"name": "nifee", "nationality": "", "kd_ratio": 1.13}, {"name": "onic", "nationality": "", "kd_ratio": 1.0}, {"name": "Dawy", "nationality": "", "kd_ratio": 0.99}]}, "team2": {"name": "Ninjas IN Pyjamas NIP CCT EU 21 06 25", "ranking": 29, "ensi_score": 1746, "winrate_10": 70.0, "winrate_30": 70.0, "current_shape": 100.0, "avg_kd": 1.04, "players": [{"name": "R1nkle", "nationality": "", "kd_ratio": 1.27}, {"name": "arrozdoce", "nationality": "", "kd_ratio": 1.06}, {"name": "ew<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.04}, {"name": "s<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.87}]}, "h2h_record": "TNL: 1 - Draws: 0 - NiP: 0 (100% vs 0%)", "prediction": "Ninjas IN Pyjamas NIP CCT EU 21 06 25", "confidence": 40.60925000000001, "betting_advice": "🟢 BEST BET: TOTAL_MAPS - OVER 2.5 maps (3 maps likely) (77.2605% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🆚 H2H record: Team Next Level TNL leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: Team Next Level TNL (16W-8L vs 6W-13L)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "TNL: 1 - Draws: 0 - NiP: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": TEAM NEXT LEVEL 2:1 TNL"], "team1_name": "TNL", "team2_name": "NiP", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 Level Up LUND!", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.27, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "cairne", "nationality": "", "kd_ratio": 1.27}, {"name": "Flierax", "nationality": "", "kd_ratio": 1.16}, {"name": "nifee", "nationality": "", "kd_ratio": 1.13}, {"name": "onic", "nationality": "", "kd_ratio": 1.0}, {"name": "Dawy", "nationality": "", "kd_ratio": 0.99}], "team2_players": [{"name": "R1nkle", "nationality": "", "kd_ratio": 1.27}, {"name": "arrozdoce", "nationality": "", "kd_ratio": 1.06}, {"name": "ew<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.04}, {"name": "s<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.87}], "team1_avg_kd": 1.11, "team2_avg_kd": 1.04}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Ninjas in Pyjamas\nNiP\n\n\n\n\n\n\n\n\n\n\n\n            All TNL and NiP Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Monte\nMonte\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "9INE\n9INE\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "TEAM NEXT LEVEL\nTNL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Wild Lotus\nWL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "42\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "58\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "41\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "06\n\n\n\n\n\n\nECSTATIC\nECSTATIC", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "34\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 16, "losses": 8}, "team2_recent_form": {"wins": 6, "losses": 13}, "team1_opponents": ["<PERSON>n", "Mon", "TEA", "Wil", "All", "<PERSON><PERSON>", "CYB", "Ast", "ECS", "Fna", "<PERSON>n", "Fna", "All", "Mon", "TEA", "Wil", "All", "<PERSON><PERSON>", "CYB", "Ast", "ECS", "Fna"], "team2_opponents": ["TEA", "TEA", "ECS", "TEA", "<PERSON>n", "<PERSON>n", "<PERSON>n", "<PERSON>n"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 18, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["TEAM NEXT LEVEL", "TNL", "Ninjas in Pyjamas", "NiP", "ECSTATIC", "Astrum", "Iberian Soul", "Metizport", "Metz", "Nexus Gaming", "Nexus", "Monte", "9INE", "<PERSON>", "Alliance", "CYBERSHOKE Esports", "Fnatic", "FNC"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"cairne": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 127.0}, "Flierax": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "nifee": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "onic": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Dawy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}}, "team2_form_trends": {"R1nkle": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 127.0}, "arrozdoce": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 106.0}, "ewjerkz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "sjuush": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "Snappi": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "cairne", "player1_kd": 1.27, "player2": "R1nkle", "player2_kd": 1.27, "impact": "VERY_HIGH", "description": "Battle of star players: <PERSON>irn<PERSON> vs <PERSON><PERSON><PERSON><PERSON>"}, {"type": "IGL_BATTLE", "player1": "Dawy", "player1_kd": 0.99, "player2": "<PERSON><PERSON><PERSON>", "player2_kd": 0.87, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON> vs <PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"cairne": {"team": "Team Next Level TNL", "kd_ratio": 1.27, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "R1nkle": {"team": "Ninjas IN Pyjamas NIP CCT EU 21 06 25", "kd_ratio": 1.27, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Ninjas IN Pyjamas NIP CCT EU 21 06 25 to win match", "confidence": 51.10925000000001, "reasoning": ["Team strength difference: 0.7", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Team Next Level TNL +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 0.7"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 77.2605, "reasoning": ["Team strength analysis: 0.7 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON>irn<PERSON> vs R<PERSON><PERSON>le", "confidence": 55, "reasoning": ["K/D comparison: 1.27 vs 1.27"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1379849-team-next-level-tnl-vs-ninjas-in-pyjamas-nip-cct-eu-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:43:07.299032"}}, "page_content": ""}, {"team1": {"name": "EX Astralis Talent EX Astra", "ranking": 127, "ensi_score": 1455, "winrate_10": 40.0, "winrate_30": 62.0, "current_shape": 79.0, "avg_kd": 0.92, "players": [{"name": "Zanto", "nationality": "Denmark", "kd_ratio": 1.07}, {"name": "suma", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "kiR", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "thamlike", "nationality": "Denmark", "kd_ratio": 0.9}, {"name": "Skodo", "nationality": "Denmark", "kd_ratio": 0.72}]}, "team2": {"name": "EnergyUltra", "ranking": 287, "ensi_score": 1348, "winrate_10": 20.0, "winrate_30": 20.0, "current_shape": 100.0, "avg_kd": 1.02, "players": [{"name": "wallen", "nationality": "Sweden", "kd_ratio": 1.41}, {"name": "Silence", "nationality": "Sweden", "kd_ratio": 1.01}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}, {"name": "dezt", "nationality": "Sweden", "kd_ratio": 0.88}, {"name": "zodi", "nationality": "Denmark", "kd_ratio": 0.84}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "EX Astralis Talent EX Astra", "confidence": 55.14958333333333, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - wallen most kills vs Zanto (80% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🏆 Ranking advantage: EX Astralis Talent EX Astra (#127 vs #287)", "📈 ENSI advantage: EX Astralis Talent EX Astra (1455 vs 1348)", "⚡ Better current shape: EnergyUltra (79.0% vs 100.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.07, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "Zanto", "nationality": "Denmark", "kd_ratio": 1.07}, {"name": "suma", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "kiR", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "thamlike", "nationality": "Denmark", "kd_ratio": 0.9}, {"name": "Skodo", "nationality": "Denmark", "kd_ratio": 0.72}], "team2_players": [{"name": "wallen", "nationality": "Sweden", "kd_ratio": 1.41}, {"name": "Silence", "nationality": "Sweden", "kd_ratio": 1.01}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}, {"name": "dezt", "nationality": "Sweden", "kd_ratio": 0.88}, {"name": "zodi", "nationality": "Denmark", "kd_ratio": 0.84}], "team1_avg_kd": 0.92, "team2_avg_kd": 1.02}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Prestige\nPrestige\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n                    All Ex", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Kronjyllands esports\nKronjy\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "10\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Astra", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "51\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "25\n\n\n\n\n\n\nBrute\nBrute", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "29\n\n\n\n\n\n\nENERGYULTRA\nENY", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 9, "losses": 8}, "team2_recent_form": {"wins": 8, "losses": 9}, "team1_opponents": ["Pre", "<PERSON><PERSON>", "ESC", "ENE", "ENE", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pre", "<PERSON><PERSON>", "ESC", "ENE", "ENE", "<PERSON><PERSON>"], "team2_opponents": ["Ast", "Fis", "Gen", "Bru", "ENE", "ENE", "Nor", "Nor", "ENE"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Kubix Esports", "kubix", "ESC Gaming", "ESC", "Fish123", "Ex-<PERSON><PERSON><PERSON>", "Ex-<PERSON><PERSON>", "Prestige", "GenOne", "GOne", "Bru<PERSON>", "XI Esport", "ENERGYULTRA", "ENY", "Kronjyllands esports", "<PERSON><PERSON><PERSON><PERSON>", "Northern Lights"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Zanto": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "suma": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "kiR": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "thamlike": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "Skodo": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 72.0}}, "team2_form_trends": {"wallen": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 141.0}, "Silence": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "Majky": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "dezt": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "zodi": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Skodo", "player1_kd": 0.72, "player2": "zodi", "player2_kd": 0.84, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs zodi"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"wallen": {"team": "EnergyUltra", "kd_ratio": 1.41, "impact_level": "HIGH", "recent_form": "IMPROVING"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "EX Astralis Talent EX Astra to win match", "confidence": 55.14958333333333, "reasoning": ["Team strength difference: 3.4", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "EnergyUltra +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 3.4"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 74.56694444444445, "reasoning": ["Team strength analysis: 3.4 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "EnergyUltra first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "wallen most kills vs Zanto", "confidence": 80, "reasoning": ["K/D comparison: 1.07 vs 1.41"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375297-ex-astralis-talent-ex-astra-vs-energyultra-eny-epl-s28-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:43:28.995202"}}, "page_content": ""}, {"team1": {"name": "<PERSON><PERSON> Navi <PERSON>", "ranking": 23, "ensi_score": 1779, "winrate_10": 80.0, "winrate_30": 73.0, "current_shape": 107.0, "avg_kd": 1.05, "players": [{"name": "Dem0N", "nationality": "", "kd_ratio": 1.13}, {"name": "cmtry", "nationality": "", "kd_ratio": 1.07}, {"name": "dzi<PERSON>s", "nationality": "", "kd_ratio": 1.04}, {"name": "krabeni", "nationality": "", "kd_ratio": 1.02}, {"name": "makazze", "nationality": "", "kd_ratio": 1.01}]}, "team2": {"name": "Iberian Soul IS CCT EU 21 06 25", "ranking": 47, "ensi_score": 1677, "winrate_10": 80.0, "winrate_30": 60.0, "current_shape": 120.0, "avg_kd": 0.94, "players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}]}, "h2h_record": "NaVi Jr.: 1 - Draws: 0 - IS: 0 (100% vs 0%)", "prediction": "<PERSON><PERSON> Navi <PERSON>", "confidence": 64.95725, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Iberian Soul IS CCT EU 21 06 25 +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (74.2285% confidence) | Alternative: TOTAL_MAPS (72.2285%)", "key_factors": ["🆚 H2H record: <PERSON><PERSON> Na<PERSON> leads (1-0) ⚡ (Some roster changes - moderate relevance)", "🏆 Ranking advantage: <PERSON><PERSON> (#23 vs #47)", "📈 ENSI advantage: <PERSON><PERSON> (1779 vs 1677)", "⚡ Better current shape: Iberian Soul IS CCT EU 21 06 25 (107.0% vs 120.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "NaVi Jr.: 1 - Draws: 0 - IS: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": <PERSON><PERSON> Junior 2:0 Na<PERSON><PERSON> Jr."], "team1_name": "<PERSON><PERSON><PERSON>.", "team2_name": "IS", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 2 European Series #20", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 5}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.13, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "Dem0N", "nationality": "", "kd_ratio": 1.13}, {"name": "cmtry", "nationality": "", "kd_ratio": 1.07}, {"name": "dzi<PERSON>s", "nationality": "", "kd_ratio": 1.04}, {"name": "krabeni", "nationality": "", "kd_ratio": 1.02}, {"name": "makazze", "nationality": "", "kd_ratio": 1.01}], "team2_players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}], "team1_avg_kd": 1.05, "team2_avg_kd": 0.94}, "recent_performance": {"team1_recent_matches": [{"score": "0:2", "result": "L", "opponent": "<PERSON><PERSON> Junior\nNaVi Jr", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "CYBERSHOKE Esports\nCS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n                    All IS Encounters\n                \n\n\n\nTeams News\n\n\n\n            No Posts yet", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:0", "result": "W", "opponent": "42\n\n\n\n\n\n\nPassion UA\nPassion UA", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "57\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "56\n\n\n\n\n\n\n500\n500", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "11\n\n\n\n\n\n\n9INE\n9INE", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "51\n\n\n\n\n\n\nIberian Soul\nIS", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 7, "losses": 3}, "team2_recent_form": {"wins": 6, "losses": 5}, "team1_opponents": ["Nat", "<PERSON><PERSON>", "<PERSON><PERSON>", "CYB", "Ast", "Ast", "<PERSON><PERSON>", "<PERSON><PERSON>", "CYB", "Ast"], "team2_opponents": ["Pas", "<PERSON>n", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>.", "Iberian Soul", "Sashi Esport", "<PERSON><PERSON>", "Passion UA", "9INE", "Ninjas in Pyjamas", "NiP", "Sangal Esports", "<PERSON><PERSON>", "<PERSON>", "Partizan Esports", "Partizan", "500", "CYBERSHOKE Esports", "Astrum"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Dem0N": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "cmtry": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "dziugss": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "krabeni": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "makazze": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}}, "team2_form_trends": {"sausol": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "stadodo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "mopoz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "ALEX": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "dav1g": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "makazze", "player1_kd": 1.01, "player2": "dav1g", "player2_kd": 0.79, "impact": "MEDIUM", "description": "Tactical battle: ma<PERSON><PERSON><PERSON> vs dav1g"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "<PERSON><PERSON> Junior Navi <PERSON> to win match", "confidence": 58.657250000000005, "reasoning": ["Team strength difference: 5.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Iberian Soul IS CCT EU 21 06 25 +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 74.2285, "reasoning": ["Based on team strength difference: 5.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 72.2285, "reasoning": ["Team strength analysis: 5.8 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Dem0N most kills vs sausol", "confidence": 70.99999999999999, "reasoning": ["K/D comparison: 1.13 vs 1.02"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1379850-natus-vincere-junior-navi-jr-vs-iberian-soul-is-cct-eu-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:43:50.623807"}}, "page_content": ""}, {"team1": {"name": "Genone Gone", "ranking": 91, "ensi_score": 1537, "winrate_10": 60.0, "winrate_30": 63.0, "current_shape": 97.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "XI Esport", "ranking": 327, "ensi_score": 1269, "winrate_10": 30.0, "winrate_30": 27.0, "current_shape": 103.0, "avg_kd": 0.87, "players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "Few", "nationality": "Denmark", "kd_ratio": 0.79}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.64}, {"name": "Sin<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Genone Gone", "confidence": 71.18199999999999, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - <PERSON><PERSON> most kills vs Skejs (76.0% confidence) | Alternative: MATCH_WINNER (71.18199999999999%)", "key_factors": ["📈 Better recent form: Genone Gone (14W-2L vs 2W-14L)", "🏆 Ranking advantage: <PERSON><PERSON> (#91 vs #327)", "📈 ENSI advantage: Genone Gone (1537 vs 1269)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 5}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.16, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "Few", "nationality": "Denmark", "kd_ratio": 0.79}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.64}, {"name": "Sin<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}], "team1_avg_kd": 1.01, "team2_avg_kd": 0.87}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "los kogutos\nLK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "GenOne\nGOne\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Kubix Esports\nkubix\n\n\n\n\n\n\n\n\n\n\n                    All GOne Encounters\n                \n\n\n\n\nXI Esport\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "28\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "34\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "51\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "17\n\n\n\n\n\n\nPrestige\nPrestige", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "50\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 2}, "team2_recent_form": {"wins": 2, "losses": 14}, "team1_opponents": ["Vol", "los", "Gen", "<PERSON><PERSON>", "Vol", "<PERSON><PERSON>", "Vol", "los", "Gen", "<PERSON><PERSON>", "Vol"], "team2_opponents": ["Ast", "Gen", "Pre", "Gen", "Pre"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 12, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Volt", "Ex-<PERSON><PERSON><PERSON>", "Ex-<PERSON><PERSON>", "Kubix Esports", "kubix", "Prestige", "GenOne", "GOne", "los kogutos", "XI Esport", "HEROIC Academy", "Hero.A"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Chucky": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "Brooxsy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Tarkky": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "cHeuuuuk": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "SLIE9000": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"Skejs": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Stesso": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "Few": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}, "Kragh": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 64.0}, "Sinzey": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "player1_kd": 0.87, "player2": "<PERSON><PERSON><PERSON>", "player2_kd": 0.64, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Genone Gone to win match", "confidence": 71.18199999999999, "reasoning": ["Team strength difference: 14.1", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 65.87866666666667, "reasoning": ["Based on team strength difference: 14.1"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 14.1 difference"]}, "CORRECT_SCORE": {"prediction": "Genone Gone 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "<PERSON><PERSON> most kills vs Skejs", "confidence": 76.0, "reasoning": ["K/D comparison: 1.16 vs 1.00"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375296-genone-gone-vs-xi-esport-xi-epl-s28-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:44:11.800719"}}, "page_content": ""}, {"team1": {"name": "Shinden Shinden", "ranking": 128, "ensi_score": 1452, "winrate_10": 40.0, "winrate_30": 43.0, "current_shape": 97.0, "avg_kd": 0.97, "players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.21}, {"name": "abizz", "nationality": "", "kd_ratio": 1.02}, {"name": "nacho", "nationality": "", "kd_ratio": 0.89}, {"name": "roy", "nationality": "", "kd_ratio": 0.87}, {"name": "BK1", "nationality": "", "kd_ratio": 0.86}]}, "team2": {"name": "Bounty Hunters BH CCT SA 21 06 25", "ranking": 124, "ensi_score": 1458, "winrate_10": 50.0, "winrate_30": 37.0, "current_shape": 113.0, "avg_kd": 0.98, "players": [{"name": "meyern", "nationality": "", "kd_ratio": 1.16}, {"name": "KAISER", "nationality": "", "kd_ratio": 1.06}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.91}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.89}, {"name": "zock", "nationality": "", "kd_ratio": 0.89}]}, "h2h_record": "ShindeN: 0 - Draws: 0 - BH: 2 (0% vs 100%)", "prediction": "Bounty Hunters BH CCT SA 21 06 25", "confidence": 67.6995, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Shinden Shinden +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (75% confidence) | Alternative: TOTAL_MAPS (73.20033333333333%)", "key_factors": ["🆚 H2H record: Bounty Hunters BH CCT SA 21 06 25 leads (2-0) 🎯 (Similar rosters - high relevance)", "⚡ Better current shape: Bounty Hunters BH CCT SA 21 06 25 (97.0% vs 113.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 2, "h2h_record": "ShindeN: 0 - Draws: 0 - BH: 2 (0% vs 100%)", "team1_wins": 0, "team2_wins": 2, "draws": 0, "recent_matches": [": Bounty Hunters 2:1 BH", ": ShindeN 0:2 ShindeN"], "team1_name": "ShindeN", "team2_name": "BH", "team1_win_percentage": 0, "team2_win_percentage": 100, "competitive_encounters": 2, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 FiReLEAGUE Buenos Aires", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.21, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "Unknown Tournament", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.21}, {"name": "abizz", "nationality": "", "kd_ratio": 1.02}, {"name": "nacho", "nationality": "", "kd_ratio": 0.89}, {"name": "roy", "nationality": "", "kd_ratio": 0.87}, {"name": "BK1", "nationality": "", "kd_ratio": 0.86}], "team2_players": [{"name": "meyern", "nationality": "", "kd_ratio": 1.16}, {"name": "KAISER", "nationality": "", "kd_ratio": 1.06}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.91}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.89}, {"name": "zock", "nationality": "", "kd_ratio": 0.89}], "team1_avg_kd": 0.97, "team2_avg_kd": 0.98}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "ShindeN\nShindeN\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Bounty Hunters\nBH\n\n\n\n\n\n\n\n\n\n\n\n            All ShindeN and BH Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "LP\nLP\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "KRU Esport\nKRU\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Yawara Esports\nYawara\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "08\n\n\n\n\n\n\nBounty Hunters\nBH", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "16\n\n\n\n\n\n\nShindeN\nShindeN", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "47\n\n\n\n\n\n\nShindeN\nShindeN", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "52\n\n\n\n\n\n\nShinde<PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "50\n\n\n\n\n\n\nShindeN\nShindeN", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 10, "losses": 12}, "team2_recent_form": {"wins": 11, "losses": 8}, "team1_opponents": ["Shi", "<PERSON><PERSON>", "KRU", "Yaw", "VEL", "Shi", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Shi", "<PERSON><PERSON>", "Shi", "KRU", "Yaw", "VEL", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "team2_opponents": ["<PERSON><PERSON>", "Shi", "Shi", "Shi", "Shi", "JER", "<PERSON><PERSON>", "Swi"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 15, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Bounty Hunters", "ShindeN", "Yawara Esports", "<PERSON><PERSON>", "KRU Esport", "KRU", "VELOX Argentina", "2GAME", "2Game", "JERSA", "Quem sao elas", "QSE", "Sharks Esports", "Sharks", "Swingers"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"ivanzinho": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "abizz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "nacho": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}, "roy": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "BK1": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 86.0}}, "team2_form_trends": {"meyern": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "KAISER": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 106.0}, "Tuurtle": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 91.0}, "bruninho": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}, "zock": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "BK1", "player1_kd": 0.86, "player2": "<PERSON><PERSON><PERSON><PERSON>", "player2_kd": 0.89, "impact": "MEDIUM", "description": "Tactical battle: BK1 vs <PERSON><PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"ivanzinho": {"team": "Shinden Shinden", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Bounty Hunters BH CCT SA 21 06 25 to win match", "confidence": 57.19949999999999, "reasoning": ["Team strength difference: 4.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Shinden Shinden +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 4.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 73.20033333333333, "reasoning": ["Team strength analysis: 4.8 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Bounty Hunters BH CCT SA 21 06 25 first map", "confidence": 63.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "confidence": 55, "reasoning": ["K/D comparison: 1.21 vs 1.16"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387721-shinden-shinden-vs-bounty-hunters-bh-cct-sa-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:44:33.700422"}}, "page_content": ""}, {"team1": {"name": "BIG BIG", "ranking": 44, "ensi_score": 1692, "winrate_10": 50.0, "winrate_30": 47.0, "current_shape": 103.0, "avg_kd": 1.05, "players": [{"name": "hyped", "nationality": "", "kd_ratio": 1.24}, {"name": "Krimbo", "nationality": "", "kd_ratio": 1.12}, {"name": "tabseN", "nationality": "", "kd_ratio": 1.0}, {"name": "JDC", "nationality": "", "kd_ratio": 0.97}, {"name": "k<PERSON><PERSON>i", "nationality": "", "kd_ratio": 0.91}]}, "team2": {"name": "Gun5 Esports Gun5 CCT EU 21 06 25", "ranking": 60, "ensi_score": 1604, "winrate_10": 50.0, "winrate_30": 43.0, "current_shape": 107.0, "avg_kd": 0.95, "players": [{"name": "fineshine52", "nationality": "", "kd_ratio": 1.09}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}, {"name": "SELLTER", "nationality": "", "kd_ratio": 0.93}, {"name": "XiELO", "nationality": "", "kd_ratio": 0.92}, {"name": "Lack1", "nationality": "", "kd_ratio": 0.84}]}, "h2h_record": "BIG: 0 - Draws: 0 - GUN5: 1 (0% vs 100%)", "prediction": "BIG BIG", "confidence": 46.719333333333324, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Gun5 Esports Gun5 CCT EU 21 06 25 +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (75% confidence) | Alternative: PLAYER_PROPS (74.99999999999999%)", "key_factors": ["🆚 H2H record: Gun5 Esports Gun5 CCT EU 21 06 25 leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: BIG BIG (15W-9L vs 7W-12L)", "🏆 Ranking advantage: BIG BIG (#44 vs #60)", "📈 ENSI advantage: BIG BIG (1692 vs 1604)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "BIG: 0 - Draws: 0 - GUN5: 1 (0% vs 100%)", "team1_wins": 0, "team2_wins": 1, "draws": 0, "recent_matches": [": BIG 1:2 BIG"], "team1_name": "BIG", "team2_name": "GUN5", "team1_win_percentage": 0, "team2_win_percentage": 100, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2024 YaLLa Compass Fall", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.24, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "hyped", "nationality": "", "kd_ratio": 1.24}, {"name": "Krimbo", "nationality": "", "kd_ratio": 1.12}, {"name": "tabseN", "nationality": "", "kd_ratio": 1.0}, {"name": "JDC", "nationality": "", "kd_ratio": 0.97}, {"name": "k<PERSON><PERSON>i", "nationality": "", "kd_ratio": 0.91}], "team2_players": [{"name": "fineshine52", "nationality": "", "kd_ratio": 1.09}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}, {"name": "SELLTER", "nationality": "", "kd_ratio": 0.93}, {"name": "XiELO", "nationality": "", "kd_ratio": 0.92}, {"name": "Lack1", "nationality": "", "kd_ratio": 0.84}], "team1_avg_kd": 1.05, "team2_avg_kd": 0.95}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "GUN5 Esports\nGUN5\n\n\n\n\n\n\n\n\n\n\n\n            All BIG  and GUN5 Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Zero Tenacity\nZ10\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "PARIVISION\nPARIVISION\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "TEAM NEXT LEVEL\nTNL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "17\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "56\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "21\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "42\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "59\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 15, "losses": 9}, "team2_recent_form": {"wins": 7, "losses": 12}, "team1_opponents": ["GUN", "<PERSON><PERSON>", "PAR", "TEA", "Ast", "CYB", "GUN", "GUN", "<PERSON><PERSON>", "RUB", "Fis", "GUN", "Fis", "CYB", "<PERSON><PERSON>", "PAR", "TEA", "Ast", "CYB", "GUN", "GUN", "<PERSON><PERSON>", "RUB", "Fis"], "team2_opponents": ["BIG", "BIG", "BIG", "BIG", "Pas", "Nex", "GUN", "GUN"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["BIG", "GUN5 Esports", "GUN5", "PARIVISION", "Zero Tenacity", "Z10", "TEAM NEXT LEVEL", "TNL", "Astrum", "CYBERSHOKE Esports", "Passion UA", "Nexus Gaming", "Nexus", "Dynamo Eclot", "Eclot", "RUBY", "Fisher College"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"hyped": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 124.0}, "Krimbo": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "tabseN": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "JDC": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "kyuubii": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 91.0}}, "team2_form_trends": {"fineshine52": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "Sdaim": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "SELLTER": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "XiELO": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "Lack1": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "k<PERSON><PERSON>i", "player1_kd": 0.91, "player2": "Lack1", "player2_kd": 0.84, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs Lack1"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"hyped": {"team": "BIG BIG", "kd_ratio": 1.24, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "BIG BIG to win match", "confidence": 57.219333333333324, "reasoning": ["Team strength difference: 4.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Gun5 Esports Gun5 CCT EU 21 06 25 +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 4.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 73.18711111111111, "reasoning": ["Team strength analysis: 4.8 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "hyped most kills vs fineshine52", "confidence": 74.99999999999999, "reasoning": ["K/D comparison: 1.24 vs 1.09"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1379848-big-big-vs-gun5-esports-gun5-cct-eu-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:44:55.496703"}}, "page_content": ""}, {"team1": {"name": "Team Next Level TNL", "ranking": 36, "ensi_score": 1731, "winrate_10": 70.0, "winrate_30": 73.0, "current_shape": 97.0, "avg_kd": 1.11, "players": [{"name": "cairne", "nationality": "", "kd_ratio": 1.27}, {"name": "Flierax", "nationality": "", "kd_ratio": 1.16}, {"name": "nifee", "nationality": "", "kd_ratio": 1.13}, {"name": "onic", "nationality": "", "kd_ratio": 1.0}, {"name": "Dawy", "nationality": "", "kd_ratio": 0.99}]}, "team2": {"name": "Favbet Team", "ranking": 76, "ensi_score": 1564, "winrate_10": 50.0, "winrate_30": 53.0, "current_shape": 97.0, "avg_kd": 1.1, "players": [{"name": "t3ns1ion", "nationality": "", "kd_ratio": 1.21}, {"name": "j3kie", "nationality": "", "kd_ratio": 1.1}, {"name": "Smash", "nationality": "", "kd_ratio": 1.09}, {"name": "bondik", "nationality": "", "kd_ratio": 1.07}, {"name": "Marix", "nationality": "", "kd_ratio": 1.02}]}, "h2h_record": "TNL: 1 - Draws: 0 - FAVBET: 3 (25% vs 75%)", "prediction": "Team Next Level TNL", "confidence": 57.94200000000001, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Favbet Team +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (71.20533333333333% confidence) | Alternative: MATCH_WINNER (63.19200000000001%)", "key_factors": ["🆚 H2H record: Favbet Team leads (3-1) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: Team Next Level TNL (18W-9L vs 9W-15L)", "🏆 Ranking advantage: Team Next Level TNL (#36 vs #76)", "📈 ENSI advantage: Team Next Level TNL (1731 vs 1564)"], "additional_factors": {"h2h_data": {"previous_encounters": 4, "h2h_record": "TNL: 1 - Draws: 0 - FAVBET: 3 (25% vs 75%)", "team1_wins": 1, "team2_wins": 3, "draws": 0, "recent_matches": [": TEAM NEXT LEVEL 2:0 TNL", ": TEAM NEXT LEVEL 0:2 TNL", ": TEAM NEXT LEVEL 0:3 TNL", ": FAVBET Team 2:0 FAVBET"], "team1_name": "TNL", "team2_name": "FAVBET", "team1_win_percentage": 25, "team2_win_percentage": 75, "competitive_encounters": 4, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 3 European Series #2", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.27, "team2_odds": 1.16, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "cairne", "nationality": "", "kd_ratio": 1.27}, {"name": "Flierax", "nationality": "", "kd_ratio": 1.16}, {"name": "nifee", "nationality": "", "kd_ratio": 1.13}, {"name": "onic", "nationality": "", "kd_ratio": 1.0}, {"name": "Dawy", "nationality": "", "kd_ratio": 0.99}], "team2_players": [{"name": "t3ns1ion", "nationality": "", "kd_ratio": 1.21}, {"name": "j3kie", "nationality": "", "kd_ratio": 1.1}, {"name": "Smash", "nationality": "", "kd_ratio": 1.09}, {"name": "bondik", "nationality": "", "kd_ratio": 1.07}, {"name": "Marix", "nationality": "", "kd_ratio": 1.02}], "team1_avg_kd": 1.11, "team2_avg_kd": 1.1}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "FAVBET Team\nFAVBET\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "FAVBET Team\nFAVBET\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:3", "result": "L", "opponent": "FAVBET Team\nFAVBET\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "TEAM NEXT LEVEL\nTNL\n\n\n\n\n\n\n\n\n\n\n\n            All TNL and FAVBET Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Monte\nMonte\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "40\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "01\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "3:0", "result": "W", "opponent": "28\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "34\n\n\n\n\n\n\nFAVBET Team\nFAVBET", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "58\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 18, "losses": 9}, "team2_recent_form": {"wins": 9, "losses": 15}, "team1_opponents": ["FAV", "FAV", "FAV", "TEA", "Mon", "TEA", "Wil", "All", "UNi", "FAV", "Rol", "FAV", "TEA", "FAV", "FAV", "FAV", "FAV", "All", "Mon", "TEA", "Wil", "All", "UNi", "FAV", "Rol"], "team2_opponents": ["TEA", "TEA", "TEA", "FAV", "TEA", "ECS", "<PERSON><PERSON>", "FAV", "Ast", "Nex"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 20, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["TEAM NEXT LEVEL", "TNL", "FAVBET Team", "FAVBET", "Partizan Esports", "Partizan", "Iberian Soul", "Monte", "9INE", "Astrum", "ECSTATIC", "<PERSON>", "Alliance", "UNiTY Esports", "UNiTY", "Dynamo Eclot", "Eclot", "Roler <PERSON>", "Nexus Gaming", "Nexus"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"cairne": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 127.0}, "Flierax": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "nifee": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "onic": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Dawy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}}, "team2_form_trends": {"t3ns1ion": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "j3kie": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 110.00000000000001}, "Smash": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "bondik": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "Marix": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "cairne", "player1_kd": 1.27, "player2": "t3ns1ion", "player2_kd": 1.21, "impact": "VERY_HIGH", "description": "Battle of star players: cairne vs t3ns1ion"}, {"type": "IGL_BATTLE", "player1": "Dawy", "player1_kd": 0.99, "player2": "Marix", "player2_kd": 1.02, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON> vs <PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"cairne": {"team": "Team Next Level TNL", "kd_ratio": 1.27, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "t3ns1ion": {"team": "Favbet Team", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Team Next Level TNL to win match", "confidence": 63.19200000000001, "reasoning": ["Team strength difference: 8.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Favbet Team +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 71.20533333333333, "reasoning": ["Based on team strength difference: 8.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 8.8 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: cairne vs t3ns1ion", "confidence": 55, "reasoning": ["K/D comparison: 1.27 vs 1.21"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378193-team-next-level-tnl-vs-favbet-team-favbet-gb-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:45:17.506748"}}, "page_content": ""}, {"team1": {"name": "KS Esports", "ranking": 149, "ensi_score": 1433, "winrate_10": 50.0, "winrate_30": 54.0, "current_shape": 96.0, "avg_kd": 0.95, "players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}]}, "team2": {"name": "Volt Volt", "ranking": 302, "ensi_score": 1331, "winrate_10": 40.0, "winrate_30": 40.0, "current_shape": 100.0, "avg_kd": 0.92, "players": [{"name": "Licale", "nationality": "Sweden", "kd_ratio": 1.03}, {"name": "JBOEN", "nationality": "Denmark", "kd_ratio": 1.02}, {"name": "kory", "nationality": "", "kd_ratio": 0.9}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.85}, {"name": "kwezz", "nationality": "Denmark", "kd_ratio": 0.79}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "KS Esports", "confidence": 55.19349999999999, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Volt Volt +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (75% confidence) | Alternative: TOTAL_MAPS (74.53766666666667%)", "key_factors": ["🏆 Ranking advantage: KS Esports (#149 vs #302)", "📈 ENSI advantage: KS Esports (1433 vs 1331)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 European Pro League Season 28: Division 2", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.11, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}], "team2_players": [{"name": "Licale", "nationality": "Sweden", "kd_ratio": 1.03}, {"name": "JBOEN", "nationality": "Denmark", "kd_ratio": 1.02}, {"name": "kory", "nationality": "", "kd_ratio": 0.9}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.85}, {"name": "kwezz", "nationality": "Denmark", "kd_ratio": 0.79}], "team1_avg_kd": 0.95, "team2_avg_kd": 0.92}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "KS Esports\nKS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "KS Esports\nKS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "The Suspect\nSuspect\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "KS Esports\nKS\n\n\n\n\n\n\n\n\n\n\n                    All KS Encounters\n                \n\n\n\n\nVolt\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "A", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "30\n\n\n\n\n\n\nlos kogutos\nLK", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "57\n\n\n\n\n\n\nKS Esports\nKS", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "55\n\n\n\n\n\n\neSportsKosova\neS Kv", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "28\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 9, "losses": 9}, "team2_recent_form": {"wins": 8, "losses": 9}, "team1_opponents": ["The", "Vol", "<PERSON><PERSON>", "Vol", "Vol", "Vol", "The", "Vol", "<PERSON><PERSON>", "Vol", "Vol"], "team2_opponents": ["los", "eSp", "Gen", "Vol", "Fis", "Pre"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 18, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["HEROIC Academy", "Hero.A", "KS Esports", "Kronjyllands esports", "<PERSON><PERSON><PERSON><PERSON>", "los kogutos", "The Suspect", "Suspect", "eSportsKosova", "eS Kv", "GenOne", "GOne", "Volt", "XI Esport", "Anonymo Esports", "Anonymo", "Fisher College", "Prestige"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"tripey": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "BledarD": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "ammar": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Caleyy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "gejmzilla": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 67.0}}, "team2_form_trends": {"Licale": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 103.0}, "JBOEN": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "kory": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "Maze": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 85.0}, "kwezz": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "gejmzilla", "player1_kd": 0.67, "player2": "kwezz", "player2_kd": 0.79, "impact": "MEDIUM", "description": "Tactical battle: g<PERSON>mzilla vs kwezz"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "KS Esports to win match", "confidence": 55.19349999999999, "reasoning": ["Team strength difference: 3.5", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Volt Volt +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 3.5"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 74.53766666666667, "reasoning": ["Team strength analysis: 3.5 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON> vs Licale", "confidence": 55, "reasoning": ["K/D comparison: 1.11 vs 1.03"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384805-ks-esports-ks-vs-volt-volt-untd21-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:45:40.804804"}}, "page_content": ""}, {"team1": {"name": "Iberian Soul IS", "ranking": 47, "ensi_score": 1677, "winrate_10": 80.0, "winrate_30": 60.0, "current_shape": 120.0, "avg_kd": 0.94, "players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}]}, "team2": {"name": "LEO Team", "ranking": 81, "ensi_score": 1560, "winrate_10": 50.0, "winrate_30": 53.0, "current_shape": 97.0, "avg_kd": 0.94, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "OneUn1que", "nationality": "", "kd_ratio": 0.95}, {"name": "kL1o", "nationality": "", "kd_ratio": 0.94}, {"name": "Kr1vda", "nationality": "", "kd_ratio": 0.92}, {"name": "marat2k", "nationality": "", "kd_ratio": 0.8}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Iberian Soul IS", "confidence": 81.**************, "betting_advice": "🟢 BEST BET: MATCH_WINNER - Iberian Soul IS to win match (81.**************% confidence) | Alternative: MAP_HANDICAP (80%)", "key_factors": ["📈 Better recent form: Iberian Soul IS (14W-4L vs 5W-11L)", "🏆 Ranking advantage: Iberian Soul IS (#47 vs #81)", "📈 ENSI advantage: Iberian Soul IS (1677 vs 1560)", "⚡ Better current shape: Iberian Soul IS (120.0% vs 97.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 Exort The Proving Grounds Season 1", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.02, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}], "team2_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "OneUn1que", "nationality": "", "kd_ratio": 0.95}, {"name": "kL1o", "nationality": "", "kd_ratio": 0.94}, {"name": "Kr1vda", "nationality": "", "kd_ratio": 0.92}, {"name": "marat2k", "nationality": "", "kd_ratio": 0.8}], "team1_avg_kd": 0.94, "team2_avg_kd": 0.94}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "CYBERSHOKE Esports\nCS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n                    All IS Encounters\n                \n\n\n\n\nLeo Team\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Novaq\nNovaq\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "57\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "56\n\n\n\n\n\n\n500\n500", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "11\n\n\n\n\n\n\n9INE\n9INE", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "51\n\n\n\n\n\n\nIberian Soul\nIS", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "14\n\n\n\n\n\n\nIberian Soul\nIS", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 4}, "team2_recent_form": {"wins": 5, "losses": 11}, "team1_opponents": ["<PERSON><PERSON>", "<PERSON><PERSON>", "CYB", "Ast", "Nov", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Ast", "<PERSON><PERSON>", "<PERSON><PERSON>", "CYB", "Ast", "Nov", "<PERSON>", "<PERSON>", "<PERSON>"], "team2_opponents": ["<PERSON>n", "<PERSON><PERSON>", "<PERSON>", "Nex", "Mon"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 20, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Monte", "FAVBET Team", "FAVBET", "HEROIC Academy", "Hero.A", "RUBY", "Ninjas in Pyjamas", "NiP", "Iberian Soul", "500", "9INE", "CYBERSHOKE Esports", "Astrum", "Leo Team", "<PERSON>", "Novaq", "ENCE Academy", "ENCE.A", "Nexus Gaming", "Nexus"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"sausol": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "stadodo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "mopoz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "ALEX": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "dav1g": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "team2_form_trends": {"Malkiss": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "OneUn1que": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "kL1o": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "Kr1vda": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "marat2k": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "dav1g", "player1_kd": 0.79, "player2": "marat2k", "player2_kd": 0.8, "impact": "MEDIUM", "description": "Tactical battle: dav1g vs marat2k"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Iberian Soul IS to win match", "confidence": 81.**************, "reasoning": ["Team strength difference: 20.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Iberian Soul IS -1.5 maps (2-0 win)", "confidence": 80, "reasoning": ["Based on team strength difference: 20.8"]}, "TOTAL_MAPS": {"prediction": "UNDER 2.5 maps (2-0 likely)", "confidence": 65.75794444444445, "reasoning": ["Team strength analysis: 20.8 difference"]}, "CORRECT_SCORE": {"prediction": "Iberian Soul IS 2-0", "confidence": 75, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Iberian Soul IS first map", "confidence": 66.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: sausol vs Malkiss", "confidence": 55, "reasoning": ["K/D comparison: 1.02 vs 1.07"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378190-iberian-soul-is-vs-leo-team-leo-gb-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:46:02.854823"}}, "page_content": ""}, {"team1": {"name": "EX Astralis Talent EX Astra", "ranking": 127, "ensi_score": 1455, "winrate_10": 40.0, "winrate_30": 62.0, "current_shape": 79.0, "avg_kd": 0.92, "players": [{"name": "Zanto", "nationality": "Denmark", "kd_ratio": 1.07}, {"name": "suma", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "kiR", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "thamlike", "nationality": "Denmark", "kd_ratio": 0.9}, {"name": "Skodo", "nationality": "Denmark", "kd_ratio": 0.72}]}, "team2": {"name": "Rebels Gaming", "ranking": 90, "ensi_score": 1541, "winrate_10": 70.0, "winrate_30": 53.0, "current_shape": 117.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.22}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "Sobol", "nationality": "", "kd_ratio": 0.98}, {"name": "innocent", "nationality": "", "kd_ratio": 0.96}, {"name": "Qlocu<PERSON>", "nationality": "", "kd_ratio": 0.81}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Rebels Gaming", "confidence": 75.08633333333333, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Rebels Gaming -1.5 maps (2-0 win) (76.72422222222221% confidence) | Alternative: MATCH_WINNER (75.08633333333333%)", "key_factors": ["🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: Rebels Gaming (#127 vs #90)", "📈 ENSI advantage: Rebels Gaming (1455 vs 1541)", "⚡ Better current shape: Rebels Gaming (79.0% vs 117.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.07, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "Zanto", "nationality": "Denmark", "kd_ratio": 1.07}, {"name": "suma", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "kiR", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "thamlike", "nationality": "Denmark", "kd_ratio": 0.9}, {"name": "Skodo", "nationality": "Denmark", "kd_ratio": 0.72}], "team2_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.22}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "Sobol", "nationality": "", "kd_ratio": 0.98}, {"name": "innocent", "nationality": "", "kd_ratio": 0.96}, {"name": "Qlocu<PERSON>", "nationality": "", "kd_ratio": 0.81}], "team1_avg_kd": 0.92, "team2_avg_kd": 1.01}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Prestige\nPrestige\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n                    All Ex", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Rebels Gaming\nREBELS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "10\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Astra", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "51\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "25\n\n\n\n\n\n\nBrute\nBrute", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "43\n\n\n\n\n\n\n9INE\n9INE", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 7, "losses": 9}, "team2_recent_form": {"wins": 10, "losses": 7}, "team1_opponents": ["Pre", "<PERSON><PERSON>", "los", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pre", "<PERSON><PERSON>", "los", "<PERSON><PERSON>", "<PERSON><PERSON>"], "team2_opponents": ["Ast", "Fis", "Gen", "Bru", "<PERSON><PERSON>", "<PERSON><PERSON>", "TPu", "RUB"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Zanto": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "suma": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "kiR": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "thamlike": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "Skodo": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 72.0}}, "team2_form_trends": {"Flayy": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 122.0}, "kisserek": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "Sobol": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "innocent": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "Qlocuu": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 81.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Skodo", "player1_kd": 0.72, "player2": "Qlocu<PERSON>", "player2_kd": 0.81, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"Flayy": {"team": "Rebels Gaming", "kd_ratio": 1.22, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Rebels Gaming to win match", "confidence": 75.08633333333333, "reasoning": ["Team strength difference: 16.7", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Rebels Gaming -1.5 maps (2-0 win)", "confidence": 76.72422222222221, "reasoning": ["Based on team strength difference: 16.7"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 16.7 difference"]}, "CORRECT_SCORE": {"prediction": "Rebels Gaming 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Rebels Gaming first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "<PERSON><PERSON><PERSON> most kills vs Zanto", "confidence": 74.99999999999999, "reasoning": ["K/D comparison: 1.07 vs 1.22"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384804-ex-astralis-talent-ex-astra-vs-rebels-gaming-rebels-untd21-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:46:24.886037"}}, "page_content": ""}, {"team1": {"name": "Gun5 Esports", "ranking": 60, "ensi_score": 1604, "winrate_10": 50.0, "winrate_30": 43.0, "current_shape": 107.0, "avg_kd": 0.97, "players": [{"name": "fineshine52", "nationality": "", "kd_ratio": 1.09}, {"name": "tex1y", "nationality": "", "kd_ratio": 1.04}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}, {"name": "SELLTER", "nationality": "", "kd_ratio": 0.93}, {"name": "Lack1", "nationality": "", "kd_ratio": 0.84}]}, "team2": {"name": "Nexus Gaming", "ranking": 80, "ensi_score": 1560, "winrate_10": 40.0, "winrate_30": 53.0, "current_shape": 87.0, "avg_kd": 1.03, "players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}]}, "h2h_record": "GUN5: 2 - Draws: 0 - Nexus: 1 (67% vs 33%)", "prediction": "Gun5 Esports", "confidence": 72.89133333333335, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - XELLOW most kills vs fineshine52 (71.99999999999999% confidence) | Alternative: MATCH_WINNER (69.39133333333335%)", "key_factors": ["🆚 H2H record: Gun5 Esports leads (2-1) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: Gun5 Esports (14W-6L vs 6W-11L)", "🏆 Ranking advantage: Gun5 Esports (#60 vs #80)", "⚡ Better current shape: Gun5 Esports (107.0% vs 87.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 3, "h2h_record": "GUN5: 2 - Draws: 0 - Nexus: 1 (67% vs 33%)", "team1_wins": 2, "team2_wins": 1, "draws": 0, "recent_matches": [": Nexus Gaming 2:1 Nexus", ": GUN5 Esports 2:0 GUN5", ": Nexus Gaming 0:1 Nexus"], "team1_name": "GUN5", "team2_name": "Nexus", "team1_win_percentage": 67, "team2_win_percentage": 33, "competitive_encounters": 3, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO1", "tier": "Tier-3", "tournament": "2025 Exort The Proving Grounds Season 1", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.09, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "fineshine52", "nationality": "", "kd_ratio": 1.09}, {"name": "tex1y", "nationality": "", "kd_ratio": 1.04}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}, {"name": "SELLTER", "nationality": "", "kd_ratio": 0.93}, {"name": "Lack1", "nationality": "", "kd_ratio": 0.84}], "team2_players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}], "team1_avg_kd": 0.97, "team2_avg_kd": 1.03}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "GUN5 Esports\nGUN5\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Nexus Gaming\nNexus\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "GUN5 Esports\nGUN5\n\n\n\n\n\n\n\n\n\n\n\n            All GUN5 and Nexus Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "GUN5 Esports\nGUN5\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Dynamo Eclot\nEclot\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "30\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "08\n\n\n\n\n\n\nGUN5 Esports\nGUN5", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "58\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "11\n\n\n\n\n\n\nPassion UA\nPassion UA", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "57\n\n\n\n\n\n\nGUN5 Esports\nGUN5", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 6}, "team2_recent_form": {"wins": 6, "losses": 11}, "team1_opponents": ["GUN", "Nex", "GUN", "GUN", "<PERSON><PERSON>", "RUB", "Fis", "Mar", "FAV", "GUN", "GUN", "Nex", "FAV", "Fis", "GUN", "<PERSON><PERSON>", "RUB", "Fis", "Mar", "FAV"], "team2_opponents": ["Nex", "GUN", "Nex", "Pas", "GUN", "Nex", "Ast"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Nexus Gaming", "Nexus", "GUN5 Esports", "GUN5", "Dynamo Eclot", "Eclot", "Partizan Esports", "Partizan", "Passion UA", "RUBY", "FAVBET Team", "FAVBET", "Fisher College", "<PERSON>", "Astrum", "Ex-Sabre Esports", "Ex-Sabre"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"fineshine52": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "tex1y": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "Sdaim": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "SELLTER": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "Lack1": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}}, "team2_form_trends": {"XELLOW": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "ragga": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "lauNX": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "s0und": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "BTN": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 75.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Lack1", "player1_kd": 0.84, "player2": "BTN", "player2_kd": 0.75, "impact": "MEDIUM", "description": "Tactical battle: Lack1 vs BTN"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"XELLOW": {"team": "Nexus Gaming", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Gun5 Esports to win match", "confidence": 69.39133333333335, "reasoning": ["Team strength difference: 12.9", "Primary betting market"]}, "FIRST_MAP_WINNER": {"prediction": "Gun5 Esports first map", "confidence": 62.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "XELLOW most kills vs fineshine52", "confidence": 71.99999999999999, "reasoning": ["K/D comparison: 1.09 vs 1.21"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378192-gun5-esports-gun5-vs-nexus-gaming-nexus-gb-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:46:45.996501"}}, "page_content": ""}, {"team1": {"name": "Team Next Level TNL", "ranking": 36, "ensi_score": 1731, "winrate_10": 70.0, "winrate_30": 73.0, "current_shape": 97.0, "avg_kd": 1.11, "players": [{"name": "cairne", "nationality": "", "kd_ratio": 1.27}, {"name": "Flierax", "nationality": "", "kd_ratio": 1.16}, {"name": "nifee", "nationality": "", "kd_ratio": 1.13}, {"name": "onic", "nationality": "", "kd_ratio": 1.0}, {"name": "Dawy", "nationality": "", "kd_ratio": 0.99}]}, "team2": {"name": "Apogee Apogee", "ranking": 48, "ensi_score": 1676, "winrate_10": 50.0, "winrate_30": 53.0, "current_shape": 97.0, "avg_kd": 0.97, "players": [{"name": "jcobbb", "nationality": "", "kd_ratio": 1.13}, {"name": "hypex", "nationality": "", "kd_ratio": 1.13}, {"name": "demho", "nationality": "", "kd_ratio": 0.94}, {"name": "hfah", "nationality": "", "kd_ratio": 0.85}, {"name": "Prism", "nationality": "", "kd_ratio": 0.78}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Team Next Level TNL", "confidence": 65.81, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - cairne most kills vs jcobbb (74.00000000000001% confidence) | Alternative: CORRECT_SCORE (70%)", "key_factors": ["📈 Better recent form: Team Next Level TNL (14W-8L vs 6W-12L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: Team Next Level TNL (#36 vs #48)", "📈 ENSI advantage: Team Next Level TNL (1731 vs 1676)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 3 European Series #3", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.27, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "cairne", "nationality": "", "kd_ratio": 1.27}, {"name": "Flierax", "nationality": "", "kd_ratio": 1.16}, {"name": "nifee", "nationality": "", "kd_ratio": 1.13}, {"name": "onic", "nationality": "", "kd_ratio": 1.0}, {"name": "Dawy", "nationality": "", "kd_ratio": 0.99}], "team2_players": [{"name": "jcobbb", "nationality": "", "kd_ratio": 1.13}, {"name": "hypex", "nationality": "", "kd_ratio": 1.13}, {"name": "demho", "nationality": "", "kd_ratio": 0.94}, {"name": "hfah", "nationality": "", "kd_ratio": 0.85}, {"name": "Prism", "nationality": "", "kd_ratio": 0.78}], "team1_avg_kd": 1.11, "team2_avg_kd": 0.97}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "Monte\nMonte\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "9INE\n9INE\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "TEAM NEXT LEVEL\nTNL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Wild Lotus\nWL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Alliance\nAlliance\n\n\n\n\n\n\n\n\n\n\n                    All TNL Encounters\n                \n\n\n\n\nBetclic\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "58\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "41\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "06\n\n\n\n\n\n\nECSTATIC\nECSTATIC", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "34\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "00\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 8}, "team2_recent_form": {"wins": 6, "losses": 12}, "team1_opponents": ["Mon", "TEA", "Wil", "All", "CYB", "FaZ", "Bet", "Ast", "Bet", "Bet", "All", "Mon", "TEA", "Wil", "All", "CYB", "FaZ", "Bet", "Ast", "Bet"], "team2_opponents": ["TEA", "TEA", "ECS", "TEA", "Bet", "Bet", "<PERSON><PERSON>", "The"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"cairne": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 127.0}, "Flierax": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "nifee": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "onic": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Dawy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}}, "team2_form_trends": {"jcobbb": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "hypex": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "demho": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "hfah": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 85.0}, "Prism": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 78.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Dawy", "player1_kd": 0.99, "player2": "Prism", "player2_kd": 0.78, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON> vs Prism"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"cairne": {"team": "Team Next Level TNL", "kd_ratio": 1.27, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Team Next Level TNL to win match", "confidence": 65.81, "reasoning": ["Team strength difference: 10.5", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Apogee Apogee +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 69.46000000000001, "reasoning": ["Based on team strength difference: 10.5"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 10.5 difference"]}, "CORRECT_SCORE": {"prediction": "Team Next Level TNL 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Team Next Level TNL first map", "confidence": 62.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "cairne most kills vs jcobbb", "confidence": 74.00000000000001, "reasoning": ["K/D comparison: 1.27 vs 1.13"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1365015-team-next-level-tnl-vs-apogee-apogee-epg-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:47:07.444258"}}, "page_content": ""}, {"team1": {"name": "BIG BIG", "ranking": 44, "ensi_score": 1692, "winrate_10": 50.0, "winrate_30": 47.0, "current_shape": 103.0, "avg_kd": 1.05, "players": [{"name": "hyped", "nationality": "", "kd_ratio": 1.24}, {"name": "Krimbo", "nationality": "", "kd_ratio": 1.12}, {"name": "tabseN", "nationality": "", "kd_ratio": 1.0}, {"name": "JDC", "nationality": "", "kd_ratio": 0.97}, {"name": "k<PERSON><PERSON>i", "nationality": "", "kd_ratio": 0.91}]}, "team2": {"name": "Bcgame Esports BCG", "ranking": 58, "ensi_score": 1611, "winrate_10": 20.0, "winrate_30": 40.0, "current_shape": 80.0, "avg_kd": 1.06, "players": [{"name": "pr1metapz", "nationality": "", "kd_ratio": 1.26}, {"name": "CacaNito", "nationality": "", "kd_ratio": 1.07}, {"name": "nawwk", "nationality": "Sweden", "kd_ratio": 1.04}, {"name": "neXa", "nationality": "", "kd_ratio": 0.99}, {"name": "CYPHER", "nationality": "", "kd_ratio": 0.95}]}, "h2h_record": "BIG: 1 - Draws: 0 - BC.G: 2 (33% vs 67%)", "prediction": "BIG BIG", "confidence": 63.04241666666667, "betting_advice": "🟢 BEST BET: CORRECT_SCORE - BIG BIG 2-1 (70% confidence) | Alternative: MAP_HANDICAP (68.97172222222221%)", "key_factors": ["🆚 H2H record: Bcgame Esports BCG leads (2-1) 🎯 (Similar rosters - high relevance)", "🏆 Ranking advantage: BIG BIG (#44 vs #58)", "📈 ENSI advantage: BIG BIG (1692 vs 1611)", "⚡ Better current shape: BIG BIG (103.0% vs 80.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 3, "h2h_record": "BIG: 1 - Draws: 0 - BC.G: 2 (33% vs 67%)", "team1_wins": 1, "team2_wins": 2, "draws": 0, "recent_matches": [": BC.Game Esports 0:1 BC.G", ": BIG 0:2 BIG", ": BIG 1:2 BIG"], "team1_name": "BIG", "team2_name": "BC.G", "team1_win_percentage": 33, "team2_win_percentage": 67, "competitive_encounters": 3, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 BLAST.tv Austin Major", "h2h_history": [{"score": "0:1", "context": "recent_match"}, {"score": "0:1", "context": "recent_match"}, {"score": "0:1", "context": "recent_match"}, {"score": "0:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.24, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "hyped", "nationality": "", "kd_ratio": 1.24}, {"name": "Krimbo", "nationality": "", "kd_ratio": 1.12}, {"name": "tabseN", "nationality": "", "kd_ratio": 1.0}, {"name": "JDC", "nationality": "", "kd_ratio": 0.97}, {"name": "k<PERSON><PERSON>i", "nationality": "", "kd_ratio": 0.91}], "team2_players": [{"name": "pr1metapz", "nationality": "", "kd_ratio": 1.26}, {"name": "CacaNito", "nationality": "", "kd_ratio": 1.07}, {"name": "nawwk", "nationality": "Sweden", "kd_ratio": 1.04}, {"name": "neXa", "nationality": "", "kd_ratio": 0.99}, {"name": "CYPHER", "nationality": "", "kd_ratio": 0.95}], "team1_avg_kd": 1.05, "team2_avg_kd": 1.06}, "recent_performance": {"team1_recent_matches": [{"score": "0:1", "result": "L", "opponent": "BIG \nBIG \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "BC", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "BC", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Zero Tenacity\nZ10\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "PARIVISION\nPARIVISION\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:0", "result": "W", "opponent": "G", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "54\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "56\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "56\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "21\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 9, "losses": 13}, "team2_recent_form": {"wins": 8, "losses": 10}, "team1_opponents": ["BIG", "<PERSON><PERSON>", "PAR", "TEA", "Ast", "CYB", "Par", "Pas", "FaZ", "BIG", "CYB", "<PERSON><PERSON>", "PAR", "TEA", "Ast", "CYB", "Par", "Pas", "FaZ"], "team2_opponents": ["BIG", "BIG", "BIG", "BIG", "MOU", "Par"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["BC.Game Esports", "BC.G", "BIG", "Heroic", "Zero Tenacity", "Z10", "PARIVISION", "TEAM NEXT LEVEL", "TNL", "Astrum", "CYBERSHOKE Esports", "Partizan Esports", "Partizan", "Passion UA", "FaZe Clan", "FaZe", "MOUZ"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"hyped": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 124.0}, "Krimbo": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "tabseN": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "JDC": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "kyuubii": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 91.0}}, "team2_form_trends": {"pr1metapz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 126.0}, "CacaNito": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "nawwk": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "neXa": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "CYPHER": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "hyped", "player1_kd": 1.24, "player2": "pr1metapz", "player2_kd": 1.26, "impact": "VERY_HIGH", "description": "Battle of star players: hyped vs pr1metapz"}, {"type": "IGL_BATTLE", "player1": "k<PERSON><PERSON>i", "player1_kd": 0.91, "player2": "CYPHER", "player2_kd": 0.95, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs CYPHER"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"hyped": {"team": "BIG BIG", "kd_ratio": 1.24, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "pr1metapz": {"team": "Bcgame Esports BCG", "kd_ratio": 1.26, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "BIG BIG to win match", "confidence": 66.54241666666667, "reasoning": ["Team strength difference: 11.0", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Bcgame Esports BCG +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 68.97172222222221, "reasoning": ["Based on team strength difference: 11.0"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 11.0 difference"]}, "CORRECT_SCORE": {"prediction": "BIG BIG 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "BIG BIG first map", "confidence": 66.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: hyped vs pr1metapz", "confidence": 55, "reasoning": ["K/D comparison: 1.24 vs 1.26"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1365014-big-big-vs-bcgame-esports-bcg-epg-21-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 17:47:29.098597"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 2450.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 100, "recommendations": [{"match": "Iberian Soul IS vs LEO Team", "bet_type": "Moneyline", "recommendation": "Iberian Soul IS to win match", "confidence": 81.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 20.8", "Primary betting market"]}, {"match": "EX Astralis Talent EX Astra vs EnergyUltra", "bet_type": "Player Props", "recommendation": "wallen most kills vs Zanto", "confidence": 80, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.07 vs 1.41"]}, {"match": "Iberian Soul IS vs LEO Team", "bet_type": "Map Handicap", "recommendation": "Iberian Soul IS -1.5 maps (2-0 win)", "confidence": 80, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 20.8"]}, {"match": "Team Next Level TNL vs Ninjas IN Pyjamas NIP CCT EU 21 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 77.2605, "value_rating": 0.54521, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 0.7 difference"]}, {"match": "EX Astralis Talent EX Astra vs Rebels Gaming", "bet_type": "Map Handicap", "recommendation": "Rebels Gaming -1.5 maps (2-0 win)", "confidence": 76.72422222222221, "value_rating": 0.5344844444444441, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 16.7"]}, {"match": "MIBR Academy MIBR Acad vs Elevate ELV CCT SA 20 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 76.38688888888889, "value_rating": 0.5277377777777779, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 1.6 difference"]}, {"match": "Genone Gone vs XI Esport", "bet_type": "Player Props", "recommendation": "<PERSON><PERSON> most kills vs Skejs", "confidence": 76.0, "value_rating": 0.52, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.16 vs 1.00"]}, {"match": "EX Astralis Talent EX Astra vs Rebels Gaming", "bet_type": "Moneyline", "recommendation": "Rebels Gaming to win match", "confidence": 75.08633333333333, "value_rating": 0.5017266666666667, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 16.7", "Primary betting market"]}, {"match": "MIBR Academy MIBR Acad vs Elevate ELV CCT SA 20 06 25", "bet_type": "Map Handicap", "recommendation": "MIBR Academy MIBR Acad +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 1.6"]}, {"match": "Team Next Level TNL vs Ninjas IN Pyjamas NIP CCT EU 21 06 25", "bet_type": "Map Handicap", "recommendation": "Team Next Level TNL +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 0.7"]}, {"match": "EX Astralis Talent EX Astra vs EnergyUltra", "bet_type": "Map Handicap", "recommendation": "EnergyUltra +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 3.4"]}, {"match": "Shinden Shinden vs Bounty Hunters BH CCT SA 21 06 25", "bet_type": "Map Handicap", "recommendation": "Shinden Shinden +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 4.8"]}, {"match": "BIG BIG vs Gun5 Esports Gun5 CCT EU 21 06 25", "bet_type": "Map Handicap", "recommendation": "Gun5 Esports Gun5 CCT EU 21 06 25 +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 4.8"]}, {"match": "KS Esports vs Volt Volt", "bet_type": "Map Handicap", "recommendation": "Volt Volt +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 3.5"]}, {"match": "Iberian Soul IS vs LEO Team", "bet_type": "Correct Score", "recommendation": "Iberian Soul IS 2-0", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength and historical patterns"]}, {"match": "BIG BIG vs Gun5 Esports Gun5 CCT EU 21 06 25", "bet_type": "Player Props", "recommendation": "hyped most kills vs fineshine52", "confidence": 74.99999999999999, "value_rating": 0.4999999999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.24 vs 1.09"]}, {"match": "EX Astralis Talent EX Astra vs Rebels Gaming", "bet_type": "Player Props", "recommendation": "<PERSON><PERSON><PERSON> most kills vs Zanto", "confidence": 74.99999999999999, "value_rating": 0.4999999999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.07 vs 1.22"]}, {"match": "EX Astralis Talent EX Astra vs EnergyUltra", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 74.56694444444445, "value_rating": 0.491338888888889, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 3.4 difference"]}, {"match": "KS Esports vs Volt Volt", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 74.53766666666667, "value_rating": 0.4907533333333334, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 3.5 difference"]}, {"match": "<PERSON>us <PERSON>re Junior Navi JR vs Iberian Soul IS CCT EU 21 06 25", "bet_type": "Map Handicap", "recommendation": "Iberian Soul IS CCT EU 21 06 25 +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 74.2285, "value_rating": 0.48456999999999995, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 5.8"]}, {"match": "Team Next Level TNL vs Apogee Apogee", "bet_type": "Player Props", "recommendation": "cairne most kills vs jcobbb", "confidence": 74.00000000000001, "value_rating": 0.4800000000000002, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.27 vs 1.13"]}, {"match": "Shinden Shinden vs Bounty Hunters BH CCT SA 21 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 73.20033333333333, "value_rating": 0.4640066666666667, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 4.8 difference"]}, {"match": "BIG BIG vs Gun5 Esports Gun5 CCT EU 21 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 73.18711111111111, "value_rating": 0.46374222222222206, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 4.8 difference"]}, {"match": "<PERSON>us <PERSON>re Junior Navi JR vs Iberian Soul IS CCT EU 21 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 72.2285, "value_rating": 0.4445699999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 5.8 difference"]}, {"match": "Gun5 Esports vs Nexus Gaming", "bet_type": "Player Props", "recommendation": "XELLOW most kills vs fineshine52", "confidence": 71.99999999999999, "value_rating": 0.4399999999999997, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.09 vs 1.21"]}, {"match": "FaZe Clan vs THE Mongolz Mongolz", "bet_type": "Map Handicap", "recommendation": "FaZe Clan +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 71.92500000000001, "value_rating": 0.43850000000000033, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 8.1"]}, {"match": "Team Next Level TNL vs Favbet Team", "bet_type": "Map Handicap", "recommendation": "Favbet Team +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 71.20533333333333, "value_rating": 0.42410666666666663, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 8.8"]}, {"match": "Genone Gone vs XI Esport", "bet_type": "Moneyline", "recommendation": "Genone Gone to win match", "confidence": 71.18199999999999, "value_rating": 0.4236399999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 14.1", "Primary betting market"]}, {"match": "<PERSON>us <PERSON>re Junior Navi JR vs Iberian Soul IS CCT EU 21 06 25", "bet_type": "Player Props", "recommendation": "Dem0N most kills vs sausol", "confidence": 70.99999999999999, "value_rating": 0.4199999999999997, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.13 vs 1.02"]}, {"match": "EX Astralis Talent EX Astra vs EnergyUltra", "bet_type": "Total Rounds", "recommendation": "EnergyUltra first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "EX Astralis Talent EX Astra vs Rebels Gaming", "bet_type": "Total Rounds", "recommendation": "Rebels Gaming first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Team Next Level TNL vs Apogee Apogee", "bet_type": "Map Handicap", "recommendation": "Apogee Apogee +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 69.46000000000001, "value_rating": 0.3892000000000002, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 10.5"]}, {"match": "Gun5 Esports vs Nexus Gaming", "bet_type": "Moneyline", "recommendation": "Gun5 Esports to win match", "confidence": 69.39133333333335, "value_rating": 0.387826666666667, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 12.9", "Primary betting market"]}, {"match": "BIG BIG vs Bcgame Esports BCG", "bet_type": "Map Handicap", "recommendation": "Bcgame Esports BCG +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 68.97172222222221, "value_rating": 0.3794344444444442, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 11.0"]}, {"match": "BIG BIG vs Bcgame Esports BCG", "bet_type": "Moneyline", "recommendation": "BIG BIG to win match", "confidence": 66.54241666666667, "value_rating": 0.3308483333333334, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 11.0", "Primary betting market"]}, {"match": "Iberian Soul IS vs LEO Team", "bet_type": "Total Rounds", "recommendation": "Iberian Soul IS first map", "confidence": 66.5, "value_rating": 0.33000000000000007, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "BIG BIG vs Bcgame Esports BCG", "bet_type": "Total Rounds", "recommendation": "BIG BIG first map", "confidence": 66.0, "value_rating": 0.32000000000000006, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Genone Gone vs XI Esport", "bet_type": "Map Handicap", "recommendation": "XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 65.87866666666667, "value_rating": 0.3175733333333335, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 14.1"]}, {"match": "Team Next Level TNL vs Apogee Apogee", "bet_type": "Moneyline", "recommendation": "Team Next Level TNL to win match", "confidence": 65.81, "value_rating": 0.31620000000000004, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 10.5", "Primary betting market"]}, {"match": "Iberian Soul IS vs LEO Team", "bet_type": "Total Maps", "recommendation": "UNDER 2.5 maps (2-0 likely)", "confidence": 65.75794444444445, "value_rating": 0.315158888888889, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 20.8 difference"]}, {"match": "Shinden Shinden vs Bounty Hunters BH CCT SA 21 06 25", "bet_type": "Total Rounds", "recommendation": "Bounty Hunters BH CCT SA 21 06 25 first map", "confidence": 63.5, "value_rating": 0.27, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Gun5 Esports vs Nexus Gaming", "bet_type": "Total Rounds", "recommendation": "Gun5 Esports first map", "confidence": 62.0, "value_rating": 0.24, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Team Next Level TNL vs Apogee Apogee", "bet_type": "Total Rounds", "recommendation": "Team Next Level TNL first map", "confidence": 62.0, "value_rating": 0.24, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "FaZe Clan vs THE Mongolz Mongolz", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 8.1 difference"]}, {"match": "Genone Gone vs XI Esport", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 14.1 difference"]}, {"match": "Team Next Level TNL vs Favbet Team", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 8.8 difference"]}, {"match": "EX Astralis Talent EX Astra vs Rebels Gaming", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 16.7 difference"]}, {"match": "Team Next Level TNL vs Apogee Apogee", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 10.5 difference"]}, {"match": "BIG BIG vs Bcgame Esports BCG", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 11.0 difference"]}]}, "enhancement_stats": {"total_predictions": 15, "average_confidence": 62.13010555555555, "premium_bets": 0, "strong_bets": 1, "good_bets": 3, "lean_bets": 2, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 6.666666666666667, "good_pct": 20.0, "lean_pct": 13.333333333333334}}}