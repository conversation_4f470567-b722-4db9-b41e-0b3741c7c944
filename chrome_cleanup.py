#!/usr/bin/env python3
"""
🧹 CHROME CLEANUP UTILITY
Standalone script to kill any leftover Chrome processes from scraping operations
"""

import psutil
import subprocess
import os
import sys

def kill_chrome_processes():
    """Kill all Chrome processes that might be leftover from scraping"""
    print("🧹 CHROME CLEANUP UTILITY")
    print("=" * 50)
    
    try:
        chrome_process_names = [
            'chrome.exe', 'chromium.exe', 'chromedriver.exe',
            'Google Chrome', 'Google Chrome Helper'
        ]
        
        killed_count = 0
        found_processes = []
        
        # First, find all Chrome-related processes
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_name = proc.info['name'].lower() if proc.info['name'] else ''
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                
                # Check if it's a Chrome process
                if any(chrome_name.lower() in proc_name for chrome_name in chrome_process_names):
                    # Additional check to make sure it's related to scraping/automation
                    if ('--remote-debugging-port' in cmdline or 
                        '--headless' in cmdline or 
                        'chromedriver' in cmdline or
                        '--user-data-dir' in cmdline or
                        '--disable-dev-shm-usage' in cmdline or
                        '--no-sandbox' in cmdline):
                        
                        found_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                        })
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
            except Exception:
                continue
        
        if not found_processes:
            print("✅ No scraping-related Chrome processes found")
            return
        
        print(f"🔍 Found {len(found_processes)} Chrome processes related to scraping:")
        for i, proc in enumerate(found_processes, 1):
            print(f"   {i}. {proc['name']} (PID: {proc['pid']})")
            print(f"      Command: {proc['cmdline']}")
        
        # Ask for confirmation
        response = input(f"\n❓ Kill all {len(found_processes)} processes? (y/N): ").lower()
        if response not in ['y', 'yes']:
            print("❌ Cleanup cancelled")
            return
        
        # Kill the processes
        print("\n🔪 Terminating processes...")
        for proc_info in found_processes:
            try:
                proc = psutil.Process(proc_info['pid'])
                print(f"   🔪 Killing: {proc_info['name']} (PID: {proc_info['pid']})")
                proc.terminate()
                killed_count += 1
                
                # Give it a moment to terminate gracefully
                try:
                    proc.wait(timeout=2)
                except psutil.TimeoutExpired:
                    # Force kill if it doesn't terminate
                    proc.kill()
                    print(f"      ⚡ Force killed: {proc_info['name']}")
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                print(f"      ⚠️ Could not kill: {proc_info['name']} (already dead or access denied)")
                continue
            except Exception as e:
                print(f"      ❌ Error killing {proc_info['name']}: {e}")
                continue
        
        print(f"\n✅ Cleanup completed! Killed {killed_count} processes")
        
    except Exception as e:
        print(f"❌ Error during Chrome cleanup: {e}")
        # Fallback: try to kill using system commands
        fallback_chrome_cleanup()

def fallback_chrome_cleanup():
    """Fallback cleanup using system commands"""
    print("\n🔄 Trying fallback cleanup methods...")
    try:
        if os.name == 'nt':  # Windows
            print("   🪟 Using Windows taskkill...")
            result1 = subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], 
                         capture_output=True, text=True)
            result2 = subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], 
                         capture_output=True, text=True)
            
            if "SUCCESS" in result1.stdout or "SUCCESS" in result2.stdout:
                print("   ✅ Fallback cleanup successful")
            else:
                print("   ⚠️ No processes found to kill")
                
        else:  # Unix/Linux
            print("   🐧 Using Unix pkill...")
            subprocess.run(['pkill', '-f', 'chrome'], capture_output=True)
            subprocess.run(['pkill', '-f', 'chromedriver'], capture_output=True)
            print("   ✅ Fallback cleanup executed")
            
    except Exception as e:
        print(f"   ❌ Fallback cleanup failed: {e}")

def show_chrome_processes():
    """Show all running Chrome processes (for diagnostics)"""
    print("🔍 CHROME PROCESS DIAGNOSTICS")
    print("=" * 50)
    
    chrome_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
        try:
            proc_name = proc.info['name'].lower() if proc.info['name'] else ''
            
            if 'chrome' in proc_name or 'chromium' in proc_name:
                memory_mb = proc.info['memory_info'].rss / 1024 / 1024 if proc.info['memory_info'] else 0
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                
                chrome_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'memory_mb': memory_mb,
                    'cmdline': cmdline[:150] + '...' if len(cmdline) > 150 else cmdline
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    if not chrome_processes:
        print("✅ No Chrome processes found")
        return
    
    print(f"Found {len(chrome_processes)} Chrome processes:")
    for i, proc in enumerate(chrome_processes, 1):
        print(f"\n{i}. {proc['name']} (PID: {proc['pid']}) - {proc['memory_mb']:.1f} MB")
        print(f"   Command: {proc['cmdline']}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--show':
        show_chrome_processes()
    else:
        kill_chrome_processes() 