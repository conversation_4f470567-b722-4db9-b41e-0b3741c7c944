#!/usr/bin/env python3
"""
MACHINE LEARNING SYSTEM DEMONSTRATION
====================================
Shows how the ML calibration system learns from match results
and improves prediction accuracy over time.
"""

from prediction_calibration import PredictionCalibrator, PredictionResult
from match_result_collector import MatchResultCollector
from datetime import datetime, timedelta
import json

def demonstrate_ml_learning():
    """Demonstrate how the ML system learns and improves"""
    
    print("🧠 MACHINE LEARNING CALIBRATION SYSTEM DEMO")
    print("=" * 60)
    
    # Initialize the ML system
    calibrator = PredictionCalibrator()
    collector = MatchResultCollector(calibrator)
    
    print("✅ ML system initialized")
    print(f"📊 Historical predictions loaded: {len(calibrator.prediction_history)}")
    
    # Simulate some historical results to show learning
    print("\n🎯 SIMULATING HISTORICAL MATCH RESULTS...")
    
    # Add some sample historical results
    sample_results = [
        # Match 1: We predicted Team A to win with 75% confidence - CORRECT
        PredictionResult(
            match_id="demo_match_1",
            prediction="Team A to win",
            confidence=75.0,
            actual_result="Team A won 2-1",
            correct=True,
            timestamp=datetime.now() - timedelta(days=10),
            market_type="match_winner",
            tournament_tier="Tier-1",
            team1_rank=5,
            team2_rank=8
        ),
        
        # Match 2: We predicted Team B to win with 80% confidence - WRONG
        PredictionResult(
            match_id="demo_match_2",
            prediction="Team B to win",
            confidence=80.0,
            actual_result="Team C won 2-0",
            correct=False,
            timestamp=datetime.now() - timedelta(days=8),
            market_type="match_winner",
            tournament_tier="Tier-1",
            team1_rank=3,
            team2_rank=12
        ),
        
        # Match 3: We predicted OVER 2.5 maps with 70% confidence - CORRECT
        PredictionResult(
            match_id="demo_match_3",
            prediction="OVER 2.5 maps",
            confidence=70.0,
            actual_result="3 maps played (2-1)",
            correct=True,
            timestamp=datetime.now() - timedelta(days=5),
            market_type="total_maps",
            tournament_tier="Tier-1",
            team1_rank=7,
            team2_rank=9
        ),
        
        # Match 4: We predicted Team D +1.5 maps with 65% confidence - CORRECT
        PredictionResult(
            match_id="demo_match_4",
            prediction="Team D +1.5 maps",
            confidence=65.0,
            actual_result="Team D lost 1-2 (covered +1.5)",
            correct=True,
            timestamp=datetime.now() - timedelta(days=3),
            market_type="map_handicap",
            tournament_tier="Tier-2",
            team1_rank=15,
            team2_rank=4
        ),
        
        # Match 5: We predicted UNDER 21.5 rounds with 85% confidence - WRONG
        PredictionResult(
            match_id="demo_match_5",
            prediction="UNDER 21.5 rounds",
            confidence=85.0,
            actual_result="24 rounds played (went to overtime)",
            correct=False,
            timestamp=datetime.now() - timedelta(days=1),
            market_type="total_rounds",
            tournament_tier="Tier-1",
            team1_rank=2,
            team2_rank=6
        )
    ]
    
    # Add these results to the ML system
    for result in sample_results:
        calibrator.save_prediction_result(result)
        print(f"   📝 Added: {result.prediction} ({result.confidence}%) - {'✅ CORRECT' if result.correct else '❌ WRONG'}")
    
    print(f"\n📊 Total historical data: {len(calibrator.prediction_history)} predictions")
    
    # Generate accuracy report
    print("\n📈 CURRENT ACCURACY REPORT:")
    print("-" * 40)
    
    report = calibrator.get_accuracy_report()
    
    print(f"Overall Accuracy: {report['overall_accuracy']*100:.1f}%")
    print(f"Recent Accuracy (30 days): {report['recent_accuracy']*100:.1f}%")
    print(f"Calibration Quality: {report['calibration_quality']}")
    
    print("\n📊 Accuracy by Market Type:")
    for market, accuracy in report['market_accuracy'].items():
        print(f"   {market}: {accuracy*100:.1f}%")
    
    print("\n📊 Accuracy by Confidence Range:")
    for range_str, accuracy in report['confidence_accuracy'].items():
        print(f"   {range_str}: {accuracy*100:.1f}%")
    
    # Demonstrate confidence calibration
    print("\n🎯 CONFIDENCE CALIBRATION DEMONSTRATION:")
    print("-" * 50)
    
    # Test scenarios
    test_scenarios = [
        {"confidence": 75, "market": "match_winner", "tier": "Tier-1", "rank_diff": 3},
        {"confidence": 80, "market": "total_maps", "tier": "Tier-1", "rank_diff": 5},
        {"confidence": 65, "market": "map_handicap", "tier": "Tier-2", "rank_diff": 11}
    ]
    
    for scenario in test_scenarios:
        raw_confidence = scenario["confidence"]
        calibrated = calibrator.calibrate_confidence(
            raw_confidence, 
            scenario["market"], 
            scenario["tier"], 
            scenario["rank_diff"]
        )
        
        adjustment = calibrated - raw_confidence
        direction = "↑" if adjustment > 0 else "↓" if adjustment < 0 else "→"
        
        print(f"   {scenario['market']} ({scenario['tier']}):")
        print(f"     Raw: {raw_confidence}% → Calibrated: {calibrated:.1f}% {direction}")
        print(f"     Adjustment: {adjustment:+.1f}% (based on historical accuracy)")
    
    # Show how the system tracks pending predictions
    print("\n⏳ PENDING PREDICTIONS TRACKING:")
    print("-" * 40)
    
    # Simulate saving a new prediction for tracking
    new_prediction = {
        'prediction': 'Navi to win',
        'confidence': 67.4,
        'market_type': 'match_winner',
        'tournament_tier': 'Tier-1',
        'team1_name': 'Natus Vincere',
        'team2_name': 'Team Vitality',
        'team1_rank': 4,
        'team2_rank': 1
    }
    
    match_url = "https://ensigame.com/matches/cs-2/1369180-natus-vincere-navi-vs-team-vitality-vitality-blast-19-06-25"
    
    collector.save_prediction_for_tracking(new_prediction, match_url)
    
    print(f"✅ Saved prediction for tracking: {new_prediction['prediction']} ({new_prediction['confidence']}%)")
    print(f"🔍 System will automatically check result after match completion")
    print(f"📊 When result is found, ML system will learn and adjust future predictions")
    
    # Show the learning cycle
    print("\n🔄 MACHINE LEARNING CYCLE:")
    print("-" * 30)
    print("1. 🎯 Make Prediction → Save for tracking")
    print("2. ⏳ Wait for Match → Auto-check results every 6 hours")
    print("3. 📊 Compare Result → Update accuracy statistics")
    print("4. 🧠 Learn Pattern → Adjust confidence calibration")
    print("5. 🎯 Next Prediction → Apply learned adjustments")
    
    print("\n✅ MACHINE LEARNING DEMO COMPLETE!")
    print("🚀 Your system will continuously improve accuracy over time!")
    
    # Cleanup
    collector.cleanup()

def show_ml_workflow():
    """Show the complete ML workflow"""
    
    print("\n" + "="*60)
    print("🔄 COMPLETE MACHINE LEARNING WORKFLOW")
    print("="*60)
    
    print("""
🎯 STEP 1: PREDICTION PHASE
   • System analyzes match data
   • Generates predictions with confidence levels
   • Saves predictions to pending_predictions.json
   • Continues with betting recommendations

⏳ STEP 2: WAITING PHASE
   • Background process monitors pending predictions
   • Checks match results every 6 hours
   • Only checks matches older than 4 hours (completed)

🔍 STEP 3: RESULT COLLECTION
   • Scrapes actual match results from Ensigame
   • Extracts final scores, winners, map counts
   • Compares with our predictions

📊 STEP 4: ACCURACY EVALUATION
   • Determines if predictions were correct
   • Updates prediction_results.json with outcomes
   • Calculates accuracy by market type and confidence range

🧠 STEP 5: LEARNING & CALIBRATION
   • Analyzes prediction accuracy patterns
   • Adjusts confidence levels for similar future matches
   • Improves calibration for different market types

🎯 STEP 6: IMPROVED PREDICTIONS
   • Next predictions use learned adjustments
   • Higher accuracy over time
   • Better confidence calibration

📈 CONTINUOUS IMPROVEMENT:
   • System gets smarter with each match
   • Confidence levels become more accurate
   • Better value bet identification
   • Reduced overconfidence in difficult markets
    """)

if __name__ == "__main__":
    demonstrate_ml_learning()
    show_ml_workflow()
