{"timestamp": "20250620_002606", "total_matches": 1, "successful_scrapes": 1, "failed_urls": [], "success_rate": 100.0, "processing_time": 20.**************, "min_confidence": 60.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "<PERSON><PERSON>", "ranking": 4, "ensi_score": 1956, "winrate_10": 60.0, "winrate_30": 63.0, "current_shape": 97.0, "avg_kd": 1.08, "players": [{"name": "B1t", "nationality": "", "kd_ratio": 1.3}, {"name": "w0nderful", "nationality": "", "kd_ratio": 1.13}, {"name": "iM", "nationality": "", "kd_ratio": 1.1}, {"name": "jL", "nationality": "", "kd_ratio": 1.07}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.81}]}, "team2": {"name": "Team Vitality Vitality", "ranking": 1, "ensi_score": 2211, "winrate_10": 90.0, "winrate_30": 97.0, "current_shape": 93.0, "avg_kd": 1.14, "players": [{"name": "ZywOo", "nationality": "", "kd_ratio": 1.6}, {"name": "ropz", "nationality": "", "kd_ratio": 1.28}, {"name": "mezii", "nationality": "", "kd_ratio": 1.11}, {"name": "flameZ", "nationality": "", "kd_ratio": 0.93}, {"name": "apEX", "nationality": "", "kd_ratio": 0.77}]}, "h2h_record": "Na'Vi: 11 - Draws: 0 - Vitality: 6 (65% vs 35%)", "prediction": "Team Vitality Vitality", "confidence": 62.42900000000002, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - ZywOo most kills vs B1t (80% confidence) | Alternative: FIRST_MAP_ROUNDS (75%)", "key_factors": ["🆚 H2H advantage: <PERSON><PERSON> (11-6 record)", "📈 ENSI advantage: Team Vitality Vitality (1956 vs 2211)"], "additional_factors": {"h2h_data": {"previous_encounters": 17, "h2h_record": "Na'Vi: 11 - Draws: 0 - Vitality: 6 (65% vs 35%)", "team1_wins": 11, "team2_wins": 6, "draws": 0, "recent_matches": [": Nat<PERSON> Vince<PERSON> 1:3 Na'Vi", ": Team Vitality 2:0 Vitality", ": Team Vitality 2:0 Vitality", ": Natus Vincere 0:2 Na'Vi"], "team1_name": "<PERSON><PERSON><PERSON><PERSON>", "team2_name": "Vitality", "team1_win_percentage": 65, "team2_win_percentage": 35, "competitive_encounters": 17, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-1", "tournament": "2024 Intel Extreme Masters Cologne", "h2h_history": [{"score": "1:3", "context": "recent_match"}, {"score": "1:3", "context": "recent_match"}, {"score": "1:3", "context": "recent_match"}, {"score": "1:3", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.13, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "B1t", "nationality": "", "kd_ratio": 1.3}, {"name": "w0nderful", "nationality": "", "kd_ratio": 1.13}, {"name": "iM", "nationality": "", "kd_ratio": 1.1}, {"name": "jL", "nationality": "", "kd_ratio": 1.07}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.81}], "team2_players": [{"name": "ZywOo", "nationality": "", "kd_ratio": 1.6}, {"name": "ropz", "nationality": "", "kd_ratio": 1.28}, {"name": "mezii", "nationality": "", "kd_ratio": 1.11}, {"name": "flameZ", "nationality": "", "kd_ratio": 0.93}, {"name": "apEX", "nationality": "", "kd_ratio": 0.77}], "team1_avg_kd": 1.08, "team2_avg_kd": 1.14}, "recent_performance": {"team1_recent_matches": [{"score": "1:3", "result": "L", "opponent": "Team Vitality\nVitality\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Natus <PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Team Vitality\nVitality\n\n\n\n\n\n\n\n\n\n\n\n            All Na", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "G2 Esports\nG2\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "Natus <PERSON>", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "3:1", "result": "W", "opponent": "Vi", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "22\n\n\n\n\n\n\nTeam Vitality\nVitality", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Vi", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Vi", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "33\n\n\n\n\n\n\nTeam Spirit\nSpirit", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 8, "losses": 12}, "team2_recent_form": {"wins": 9, "losses": 10}, "team1_opponents": ["Tea", "Nat", "Tea", "Nat", "Nem", "Ast", "Tea", "Tea", "Leg", "MOU", "Tea", "Tea", "MOU", "Nem", "Ast", "Tea", "Leg", "MOU"], "team2_opponents": ["Pro", "Tea", "Tea", "Tea", "Tea", "Tea"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 18, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Team Vitality", "Vitality", "Team Falcons", "Falcons", "The Mongolz", "Mongolz", "Team Spirit", "Spirit", "3DMAX", "Nemiga Gaming", "Nemiga", "G2 Esports", "<PERSON><PERSON><PERSON>", "Virtus.Pro", "Legacy", "MOUZ"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"B1t": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 130.0}, "w0nderful": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "iM": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 110.00000000000001}, "jL": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "Aleksib": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 81.0}}, "team2_form_trends": {"ZywOo": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 160.0}, "ropz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 128.0}, "mezii": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "flameZ": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "apEX": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 77.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "B1t", "player1_kd": 1.3, "player2": "ZywOo", "player2_kd": 1.6, "impact": "VERY_HIGH", "description": "Battle of star players: B1t vs ZywOo"}, {"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON><PERSON>", "player1_kd": 0.81, "player2": "apEX", "player2_kd": 0.77, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs apEX"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"B1t": {"team": "<PERSON><PERSON>", "kd_ratio": 1.3, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "ZywOo": {"team": "Team Vitality Vitality", "kd_ratio": 1.6, "impact_level": "HIGH", "recent_form": "IMPROVING"}, "ropz": {"team": "Team Vitality Vitality", "kd_ratio": 1.28, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Team Vitality Vitality to win match", "confidence": 67.42900000000003, "reasoning": ["Team strength difference: 8.3", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Natus Vincere Navi +1.5 maps (avoid 0-2 loss)", "confidence": 71.71399999999998, "reasoning": ["Based on team strength difference: 8.3"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 8.3 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "OVER 22.5 rounds (balanced teams on mirage)", "confidence": 75, "reasoning": ["Map-specific analysis: mirage", "Team map win rates: 50% vs 50%", "Expected rounds: 23.5"]}, "PLAYER_PROPS": {"prediction": "ZywOo most kills vs B1t", "confidence": 80, "reasoning": ["K/D comparison: 1.30 vs 1.60"]}, "MATCH_DURATION": {"prediction": "Normal duration", "confidence": 60, "reasoning": ["Based on expected competitiveness"]}}, "original_url": "https://ensigame.com/matches/cs-2/1369180-natus-vincere-navi-vs-team-vitality-vitality-blast-19-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:26:06.681568"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 300.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 90, "recommendations": [{"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Total Rounds", "recommendation": "ZywOo most kills vs B1t", "confidence": 80, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.30 vs 1.60"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Total Rounds", "recommendation": "OVER 22.5 rounds (balanced teams on mirage)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Map-specific analysis: mirage", "Team map win rates: 50% vs 50%", "Expected rounds: 23.5"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Map Handicap", "recommendation": "Natus Vincere Navi +1.5 maps (avoid 0-2 loss)", "confidence": 71.71399999999998, "value_rating": 0.4342799999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 8.3"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Total Rounds", "recommendation": "Team Vitality Vitality to win match", "confidence": 67.42900000000003, "value_rating": 0.34858000000000056, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 8.3", "Primary betting market"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 8.3 difference"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Total Rounds", "recommendation": "Normal duration", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on expected competitiveness"]}]}, "enhancement_stats": {"total_predictions": 1, "average_confidence": 62.42900000000002, "premium_bets": 0, "strong_bets": 0, "good_bets": 0, "lean_bets": 0, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 0.0, "good_pct": 0.0, "lean_pct": 0.0}}}