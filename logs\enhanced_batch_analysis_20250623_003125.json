{"timestamp": "20250623_003125", "total_matches": 15, "successful_scrapes": 14, "failed_urls": [], "success_rate": 93.**************, "processing_time": 331.**************, "min_confidence": 70.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "9z Team", "ranking": 54, "ensi_score": 1646, "winrate_10": 60.0, "winrate_30": 50.0, "current_shape": 110.0, "avg_kd": 1.02, "players": [{"name": "MartinezSa", "nationality": "", "kd_ratio": 1.19}, {"name": "adamS-", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.1}, {"name": "HUASOPEEK", "nationality": "", "kd_ratio": 0.86}, {"name": "max", "nationality": "", "kd_ratio": 0.83}]}, "team2": {"name": "Dusty Roots DR CCT SA 24 06 25", "ranking": 168, "ensi_score": 1420, "winrate_10": 40.0, "winrate_30": 47.0, "current_shape": 93.0, "avg_kd": 0.86, "players": [{"name": "maxxkor", "nationality": "", "kd_ratio": 1.01}, {"name": "tom1jed", "nationality": "", "kd_ratio": 0.9}, {"name": "FraGuTy", "nationality": "", "kd_ratio": 0.88}, {"name": "toto-", "nationality": "", "kd_ratio": 0.84}, {"name": "1962", "nationality": "", "kd_ratio": 0.66}]}, "h2h_record": "9z: 1 - Draws: 0 - DR: 0 (100% vs 0%)", "prediction": "9z Team", "confidence": 92.35091666666665, "betting_advice": "🟢 BEST BET: MATCH_WINNER - 9z Team to win match (81.85091666666665% confidence) | Alternative: MAP_HANDICAP (80%)", "key_factors": ["🆚 H2H record: 9z Team leads (1-0) 🎯 (Similar rosters - high relevance)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: 9z Team (#54 vs #168)", "📈 ENSI advantage: 9z Team (1646 vs 1420)", "⚡ Better current shape: 9z Team (110.0% vs 93.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "9z: 1 - Draws: 0 - DR: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": <PERSON> 1:2 DR"], "team1_name": "9z", "team2_name": "DR", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 FiReLEAGUE Buenos Aires", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.19, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "MartinezSa", "nationality": "", "kd_ratio": 1.19}, {"name": "adamS-", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.1}, {"name": "HUASOPEEK", "nationality": "", "kd_ratio": 0.86}, {"name": "max", "nationality": "", "kd_ratio": 0.83}], "team2_players": [{"name": "maxxkor", "nationality": "", "kd_ratio": 1.01}, {"name": "tom1jed", "nationality": "", "kd_ratio": 0.9}, {"name": "FraGuTy", "nationality": "", "kd_ratio": 0.88}, {"name": "toto-", "nationality": "", "kd_ratio": 0.84}, {"name": "1962", "nationality": "", "kd_ratio": 0.66}], "team1_avg_kd": 1.02, "team2_avg_kd": 0.86}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n            All 9z and DR Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "KRU Esport\nKRU\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "02\n\n\n\n\n\n\nDusty Roots\nDR", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "31\n\n\n\n\n\n\nImperial Esports\nImperial", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "10\n\n\n\n\n\n\nBESTIA\nBESTIA", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "01\n\n\n\n\n\n\n9z Team\n9z", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "44\n\n\n\n\n\n\n9z Team\n9z", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 13, "losses": 10}, "team2_recent_form": {"wins": 6, "losses": 10}, "team1_opponents": ["KRU", "<PERSON><PERSON>", "Yaw", "<PERSON><PERSON>", "LaC", "Ele", "Ele", "<PERSON><PERSON>", "KRU", "<PERSON><PERSON>", "Yaw", "<PERSON><PERSON>", "LaC", "Ele"], "team2_opponents": ["<PERSON><PERSON>", "Imp", "BES", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"MartinezSa": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 119.0}, "adamS-": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "Luken": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 110.00000000000001}, "HUASOPEEK": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 86.0}, "max": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 83.0}}, "team2_form_trends": {"maxxkor": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "tom1jed": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "FraGuTy": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "toto-": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}, "1962": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 66.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "max", "player1_kd": 0.83, "player2": "1962", "player2_kd": 0.66, "impact": "MEDIUM", "description": "Tactical battle: max vs 1962"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "9z Team to win match", "confidence": 81.85091666666665, "reasoning": ["Team strength difference: 21.2", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "9z Team -1.5 maps (2-0 win)", "confidence": 80, "reasoning": ["Based on team strength difference: 21.2"]}, "TOTAL_MAPS": {"prediction": "UNDER 2.5 maps (2-0 likely)", "confidence": 66.23394444444443, "reasoning": ["Team strength analysis: 21.2 difference"]}, "CORRECT_SCORE": {"prediction": "9z Team 2-0", "confidence": 75, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "9z Team first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "MartinezSa most kills vs maxxkor", "confidence": 78.0, "reasoning": ["K/D comparison: 1.19 vs 1.01"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387731-9z-team-9z-vs-dusty-roots-dr-cct-sa-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:26:12.512691"}}, "page_content": ""}, {"team1": {"name": "XI Esport", "ranking": 323, "ensi_score": 1295, "winrate_10": 40.0, "winrate_30": 27.0, "current_shape": 113.0, "avg_kd": 0.87, "players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "Few", "nationality": "Denmark", "kd_ratio": 0.79}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.64}, {"name": "Sin<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}]}, "team2": {"name": "EX Astralis Talent EX Astra", "ranking": 118, "ensi_score": 1466, "winrate_10": 50.0, "winrate_30": 61.0, "current_shape": 89.0, "avg_kd": 0.92, "players": [{"name": "Zanto", "nationality": "Denmark", "kd_ratio": 1.07}, {"name": "suma", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "kiR", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "thamlike", "nationality": "Denmark", "kd_ratio": 0.9}, {"name": "Skodo", "nationality": "Denmark", "kd_ratio": 0.72}]}, "h2h_record": "XI: 0 - Draws: 0 - Ex-Astra: 2 (0% vs 100%)", "prediction": "EX Astralis Talent EX Astra", "confidence": 63.23416666666667, "betting_advice": "🟢 BEST BET: TOTAL_MAPS - OVER 2.5 maps (3 maps likely) (76.17722222222223% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🆚 H2H record: EX Astralis Talent EX Astra leads (2-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: XI Esport (13W-2L vs 2W-12L)", "🏆 Ranking advantage: EX Astralis Talent EX Astra (#323 vs #118)", "📈 ENSI advantage: EX Astralis Talent EX Astra (1295 vs 1466)", "⚡ Better current shape: XI Esport (113.0% vs 89.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 2, "h2h_record": "XI: 0 - Draws: 0 - Ex-Astra: 2 (0% vs 100%)", "team1_wins": 0, "team2_wins": 2, "draws": 0, "recent_matches": [": Ex-Astralis Talent 2:0 Ex-Astra", ": Ex-Astralis Talent 2:0 Ex-Astra"], "team1_name": "XI", "team2_name": "Ex-<PERSON><PERSON>", "team1_win_percentage": 0, "team2_win_percentage": 100, "competitive_encounters": 2, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "Few", "nationality": "Denmark", "kd_ratio": 0.79}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.64}, {"name": "Sin<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}], "team2_players": [{"name": "Zanto", "nationality": "Denmark", "kd_ratio": 1.07}, {"name": "suma", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "kiR", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "thamlike", "nationality": "Denmark", "kd_ratio": 0.9}, {"name": "Skodo", "nationality": "Denmark", "kd_ratio": 0.72}], "team1_avg_kd": 0.87, "team2_avg_kd": 0.92}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n            All XI and Ex", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n                    All XI Encounters\n                \n\n\n\n\nEx", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "Astra", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "57\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "33\n\n\n\n\n\n\nXI Esport\nXI", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "A", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "55\n\n\n\n\n\n\nPrestige\nPrestige", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 13, "losses": 2}, "team2_recent_form": {"wins": 2, "losses": 12}, "team1_opponents": ["Vol", "Vol", "ENE", "Pre", "Vol", "Vol", "Vol", "ENE", "Pre"], "team2_opponents": ["Ast", "Gen", "Pre", "Fis", "Gen"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 18, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Ex-<PERSON><PERSON><PERSON>", "Ex-<PERSON><PERSON>", "XI Esport", "Volt", "Prestige", "Young Ninjas", "Bru<PERSON>", "WOPA Esport", "WOPA", "GenOne", "GOne", "HEROIC Academy", "Hero.A", "ENERGYULTRA", "ENY", "Rebels Gaming", "REBELS", "Fish123"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Skejs": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Stesso": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "Few": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}, "Kragh": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 64.0}, "Sinzey": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"Zanto": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "suma": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "kiR": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "thamlike": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "Skodo": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 72.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON>", "player1_kd": 0.64, "player2": "Skodo", "player2_kd": 0.72, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "EX Astralis Talent EX Astra to win match", "confidence": 52.73416666666667, "reasoning": ["Team strength difference: 1.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 1.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 76.17722222222223, "reasoning": ["Team strength analysis: 1.8 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "XI Esport first map", "confidence": 64.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON><PERSON> vs Zanto", "confidence": 55, "reasoning": ["K/D comparison: 1.00 vs 1.07"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375302-xi-esport-xi-vs-ex-astralis-talent-ex-astra-epl-s28-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:26:58.798679"}}, "page_content": ""}, {"team1": {"name": "FURIA Esports Female Furiafe", "ranking": 35, "ensi_score": 1732, "winrate_10": 80.0, "winrate_30": 80.0, "current_shape": 100.0, "avg_kd": 1.28, "players": [{"name": "kaahSENSEI", "nationality": "", "kd_ratio": 1.43}, {"name": "luli<PERSON>z", "nationality": "", "kd_ratio": 1.42}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.27}, {"name": "gabs", "nationality": "", "kd_ratio": 1.16}, {"name": "izaa", "nationality": "", "kd_ratio": 1.13}]}, "team2": {"name": "Flamengo Esports Flamengo CCT SA 24 06 25", "ranking": 96, "ensi_score": 1513, "winrate_10": 60.0, "winrate_30": 60.0, "current_shape": 100.0, "avg_kd": 1.08, "players": [{"name": "Misfit", "nationality": "", "kd_ratio": 1.28}, {"name": "vsm", "nationality": "", "kd_ratio": 1.23}, {"name": "delboNi", "nationality": "", "kd_ratio": 1.12}, {"name": "Danoco", "nationality": "", "kd_ratio": 0.99}, {"name": "CutzMeretz", "nationality": "", "kd_ratio": 0.79}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "FURIA Esports Female Furiafe", "confidence": 88.99119999999999, "betting_advice": "🟢 BEST BET: MATCH_WINNER - FURIA Esports Female Furiafe to win match (85% confidence) | Alternative: PLAYER_PROPS (74.99999999999999%)", "key_factors": ["📈 Better recent form: FURIA Esports Female Furiafe (15W-5L vs 3W-8L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: FURIA Esports Female Furiafe (#35 vs #96)", "📈 ENSI advantage: FURIA Esports Female Furiafe (1732 vs 1513)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO1", "tier": "Tier-3", "tournament": "2025 Esl Impact League Season 7", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 5}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.43, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "Unknown Tournament", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "kaahSENSEI", "nationality": "", "kd_ratio": 1.43}, {"name": "luli<PERSON>z", "nationality": "", "kd_ratio": 1.42}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.27}, {"name": "gabs", "nationality": "", "kd_ratio": 1.16}, {"name": "izaa", "nationality": "", "kd_ratio": 1.13}], "team2_players": [{"name": "Misfit", "nationality": "", "kd_ratio": 1.28}, {"name": "vsm", "nationality": "", "kd_ratio": 1.23}, {"name": "delboNi", "nationality": "", "kd_ratio": 1.12}, {"name": "Danoco", "nationality": "", "kd_ratio": 0.99}, {"name": "CutzMeretz", "nationality": "", "kd_ratio": 0.79}], "team1_avg_kd": 1.28, "team2_avg_kd": 1.08}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Supernova Comets\nSComets\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Imperial Valkyries\nImp VK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "Supernova Comets\nSComets\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "DMS\nDMS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "MIBR Female\nMIBR", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "fe", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "fe", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "fe", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "38\n\n\n\n\n\n\nFlamengo Esports\nFlamengo", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "08\n\n\n\n\n\n\nFlamengo Esports\nFlamengo", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 15, "losses": 5}, "team2_recent_form": {"wins": 3, "losses": 8}, "team1_opponents": ["<PERSON><PERSON>", "Imp", "<PERSON><PERSON>", "DMS", "MIB", "Yaw", "KRU", "Ele", "LaC", "W7M", "W7M", "<PERSON><PERSON>", "Imp", "<PERSON><PERSON>", "DMS", "Yaw", "KRU", "Ele", "LaC", "W7M"], "team2_opponents": ["Fla", "Fla", "Fla"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"kaahSENSEI": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 143.0}, "lulitenz": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 142.0}, "bizinha": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 127.0}, "gabs": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "izaa": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}}, "team2_form_trends": {"Misfit": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 128.0}, "vsm": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 123.0}, "delboNi": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "Danoco": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "CutzMeretz": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "kaahSENSEI", "player1_kd": 1.43, "player2": "Misfit", "player2_kd": 1.28, "impact": "VERY_HIGH", "description": "Battle of star players: kaahSENSEI vs Misfit"}, {"type": "IGL_BATTLE", "player1": "izaa", "player1_kd": 1.13, "player2": "CutzMeretz", "player2_kd": 0.79, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs CutzMeretz"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"kaahSENSEI": {"team": "FURIA Esports Female Furiafe", "kd_ratio": 1.43, "impact_level": "HIGH", "recent_form": "IMPROVING"}, "lulitenz": {"team": "FURIA Esports Female Furiafe", "kd_ratio": 1.42, "impact_level": "HIGH", "recent_form": "IMPROVING"}, "bizinha": {"team": "FURIA Esports Female Furiafe", "kd_ratio": 1.27, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "Misfit": {"team": "Flamengo Esports Flamengo CCT SA 24 06 25", "kd_ratio": 1.28, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "vsm": {"team": "Flamengo Esports Flamengo CCT SA 24 06 25", "kd_ratio": 1.23, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "FURIA Esports Female Furiafe to win match", "confidence": 85, "reasoning": ["Team strength difference: 26.0", "Primary betting market"]}, "FIRST_MAP_WINNER": {"prediction": "FURIA Esports Female Furiafe first map", "confidence": 65.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "kaahSENSEI most kills vs Misfit", "confidence": 74.99999999999999, "reasoning": ["K/D comparison: 1.43 vs 1.28"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387734-furia-esports-female-furiafe-vs-flamengo-esports-flamengo-cct-sa-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:27:21.271904"}}, "page_content": ""}, {"team1": {"name": "Keyd Stars", "ranking": 132, "ensi_score": 1400, "winrate_10": 50.0, "winrate_30": 47.0, "current_shape": 103.0, "avg_kd": 0.94, "players": [{"name": "desh", "nationality": "", "kd_ratio": 1.32}, {"name": "vinaabEAST", "nationality": "", "kd_ratio": 0.99}, {"name": "leo_drk", "nationality": "", "kd_ratio": 0.93}, {"name": "ninjaZ", "nationality": "", "kd_ratio": 0.78}, {"name": "flash", "nationality": "", "kd_ratio": 0.67}]}, "team2": {"name": "KRU Esport KRU CCT SA 24 06 25", "ranking": 3, "ensi_score": 1451, "winrate_10": 50.0, "winrate_30": 33.0, "current_shape": 117.0, "avg_kd": 0.96, "players": [{"name": "righi", "nationality": "", "kd_ratio": 1.06}, {"name": "ataraXia", "nationality": "", "kd_ratio": 1.02}, {"name": "deco", "nationality": "", "kd_ratio": 0.98}, {"name": "laser", "nationality": "", "kd_ratio": 0.92}, {"name": "reversive", "nationality": "", "kd_ratio": 0.84}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "KRU Esport KRU CCT SA 24 06 25", "confidence": 95, "betting_advice": "🟢 BEST BET: MATCH_WINNER - KRU Esport KRU CCT SA 24 06 25 to win match (85% confidence) | Alternative: PLAYER_PROPS (80%)", "key_factors": ["📈 Better recent form: KRU Esport KRU CCT SA 24 06 25 (12W-8L vs 7W-11L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: KRU Esport KRU CCT SA 24 06 25 (#132 vs #3)", "📈 ENSI advantage: KRU Esport KRU CCT SA 24 06 25 (1400 vs 1451)", "⚡ Better current shape: KRU Esport KRU CCT SA 24 06 25 (103.0% vs 117.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO1", "tier": "Tier-3", "tournament": "2021 BLAST Rising LATAM", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.32, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "Unknown Tournament", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "desh", "nationality": "", "kd_ratio": 1.32}, {"name": "vinaabEAST", "nationality": "", "kd_ratio": 0.99}, {"name": "leo_drk", "nationality": "", "kd_ratio": 0.93}, {"name": "ninjaZ", "nationality": "", "kd_ratio": 0.78}, {"name": "flash", "nationality": "", "kd_ratio": 0.67}], "team2_players": [{"name": "righi", "nationality": "", "kd_ratio": 1.06}, {"name": "ataraXia", "nationality": "", "kd_ratio": 1.02}, {"name": "deco", "nationality": "", "kd_ratio": 0.98}, {"name": "laser", "nationality": "", "kd_ratio": 0.92}, {"name": "reversive", "nationality": "", "kd_ratio": 0.84}], "team1_avg_kd": 0.94, "team2_avg_kd": 0.96}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Keyd Stars\nKeyd\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Keyd Stars\nKeyd\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "SWS Gaming\nSWS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Keyd Stars\nKeyd\n\n\n\n\n\n\n\n\n\n\n                    All Keyd Encounters\n                \n\n\n\n\nKRU Esport\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "KRU Esport\nKRU\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "01\n\n\n\n\n\n\nRiver Plate Gaming\nRPG", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "00\n\n\n\n\n\n\nBoca Juniors\nBoca Jr", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "10\n\n\n\n\n\n\nSharks Esports\nSharks", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "52\n\n\n\n\n\n\nKeyd Stars\nKeyd", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "25\n\n\n\n\n\n\n9z Team\n9z", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 7, "losses": 11}, "team2_recent_form": {"wins": 12, "losses": 8}, "team1_opponents": ["Key", "Key", "SWS", "Key", "KRU", "KRU", "Gam", "ODD", "ODD", "Key", "Key", "Key", "SWS", "Key", "KRU", "KRU", "Gam", "ODD"], "team2_opponents": ["Riv", "Bo<PERSON>", "<PERSON><PERSON>", "Key", "Fla", "Shi", "KRU", "KRU"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"desh": {"trend": "STABLE_HIGH", "confidence": 75, "recent_matches": 5, "performance_rating": 132.0}, "vinaabEAST": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "leo_drk": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "ninjaZ": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 78.0}, "flash": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 67.0}}, "team2_form_trends": {"righi": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 106.0}, "ataraXia": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "deco": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "laser": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "reversive": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "flash", "player1_kd": 0.67, "player2": "reversive", "player2_kd": 0.84, "impact": "MEDIUM", "description": "Tactical battle: flash vs reversive"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"desh": {"team": "Keyd Stars", "kd_ratio": 1.32, "impact_level": "MEDIUM", "recent_form": "STABLE_HIGH"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "KRU Esport KRU CCT SA 24 06 25 to win match", "confidence": 85, "reasoning": ["Team strength difference: 32.3", "Primary betting market"]}, "FIRST_MAP_WINNER": {"prediction": "KRU Esport KRU CCT SA 24 06 25 first map", "confidence": 63.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "desh most kills vs righi", "confidence": 80, "reasoning": ["K/D comparison: 1.32 vs 1.06"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387732-keyd-stars-keyd-vs-kru-esport-kru-cct-sa-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:27:43.330215"}}, "page_content": ""}, {"team1": {"name": "ESC Gaming", "ranking": 80, "ensi_score": 1559, "winrate_10": 80.0, "winrate_30": 67.0, "current_shape": 113.0, "avg_kd": 0.98, "players": [{"name": "reiko", "nationality": "", "kd_ratio": 1.14}, {"name": "bajmi", "nationality": "", "kd_ratio": 1.07}, {"name": "SaMeY", "nationality": "", "kd_ratio": 1.04}, {"name": "olimp", "nationality": "", "kd_ratio": 0.88}, {"name": "moonwalk", "nationality": "", "kd_ratio": 0.76}]}, "team2": {"name": "HEROIC Academy", "ranking": 167, "ensi_score": 1421, "winrate_10": 60.0, "winrate_30": 48.0, "current_shape": 112.0, "avg_kd": 0.87, "players": [{"name": "fnl", "nationality": "", "kd_ratio": 0.99}, {"name": "Scr0b", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "anber", "nationality": "Denmark", "kd_ratio": 0.89}, {"name": "St0m4k", "nationality": "", "kd_ratio": 0.8}, {"name": "Dengzoe", "nationality": "Denmark", "kd_ratio": 0.71}]}, "h2h_record": "ESC: 1 - Draws: 0 - Hero.A: 0 (100% vs 0%)", "prediction": "ESC Gaming", "confidence": 82.38325, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - reiko most kills vs fnl (74.99999999999999% confidence) | Alternative: MATCH_WINNER (71.88325%)", "key_factors": ["🆚 H2H record: ESC Gaming leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: ESC Gaming (14W-3L vs 4W-13L)", "🏆 Ranking advantage: ESC Gaming (#80 vs #167)", "📈 ENSI advantage: ESC Gaming (1559 vs 1421)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "ESC: 1 - Draws: 0 - Hero.A: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": ESC Gaming 2:0 ESC"], "team1_name": "ESC", "team2_name": "Hero.A", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 31", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.14, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "reiko", "nationality": "", "kd_ratio": 1.14}, {"name": "bajmi", "nationality": "", "kd_ratio": 1.07}, {"name": "SaMeY", "nationality": "", "kd_ratio": 1.04}, {"name": "olimp", "nationality": "", "kd_ratio": 0.88}, {"name": "moonwalk", "nationality": "", "kd_ratio": 0.76}], "team2_players": [{"name": "fnl", "nationality": "", "kd_ratio": 0.99}, {"name": "Scr0b", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "anber", "nationality": "Denmark", "kd_ratio": 0.89}, {"name": "St0m4k", "nationality": "", "kd_ratio": 0.8}, {"name": "Dengzoe", "nationality": "Denmark", "kd_ratio": 0.71}], "team1_avg_kd": 0.98, "team2_avg_kd": 0.87}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "HEROIC Academy\nHero", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Prestige\nPrestige\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "ESC Gaming\nESC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Kronjyllands esports\nKronjy\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "ESC Gaming\nESC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "08\n\n\n\n\n\n\nESC Gaming\nESC", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "17\n\n\n\n\n\n\nESC Gaming\nESC", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "08\n\n\n\n\n\n\nENERGYULTRA\nENY", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "50\n\n\n\n\n\n\nESC Gaming\nESC", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "03\n\n\n\n\n\n\nBad News Eagles\nBNE", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 3}, "team2_recent_form": {"wins": 4, "losses": 13}, "team1_opponents": ["HER", "Pre", "ESC", "<PERSON><PERSON>", "ESC", "<PERSON><PERSON>", "HER", "<PERSON><PERSON>", "Pre", "ESC", "<PERSON><PERSON>", "ESC", "<PERSON><PERSON>"], "team2_opponents": ["ESC", "ENE", "Bad", "Nex", "Fis", "Par"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 27, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["ESC Gaming", "ESC", "HEROIC Academy", "Hero.A", "Kubix Esports", "kubix", "Volt", "Copenhagen Wolves", "CPHW", "Leo Team", "<PERSON>", "Prestige", "ENERGYULTRA", "ENY", "Kronjyllands esports", "<PERSON><PERSON><PERSON><PERSON>", "Bad News Eagles", "BNE", "Mousquetaires", "MSQ", "KS Esports", "XI Esport", "Nexus Gaming", "Nexus", "Fisher College", "Partizan Esports", "Partizan"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"reiko": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 113.99999999999999}, "bajmi": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "SaMeY": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "olimp": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "moonwalk": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 76.0}}, "team2_form_trends": {"fnl": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "Scr0b": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "anber": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}, "St0m4k": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}, "Dengzoe": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 71.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "moonwalk", "player1_kd": 0.76, "player2": "Dengzoe", "player2_kd": 0.71, "impact": "MEDIUM", "description": "Tactical battle: moonwalk vs <PERSON><PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "ESC Gaming to win match", "confidence": 71.88325, "reasoning": ["Team strength difference: 14.6", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "HEROIC Academy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 65.41116666666666, "reasoning": ["Based on team strength difference: 14.6"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 14.6 difference"]}, "CORRECT_SCORE": {"prediction": "ESC Gaming 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "ESC Gaming first map", "confidence": 61.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "reiko most kills vs fnl", "confidence": 74.99999999999999, "reasoning": ["K/D comparison: 1.14 vs 0.99"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375303-esc-gaming-esc-vs-heroic-academy-heroa-epl-s28-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:28:05.815720"}}, "page_content": ""}, {"team1": {"name": "Fish123 Fish123", "ranking": 149, "ensi_score": 1433, "winrate_10": 67.0, "winrate_30": 67.0, "current_shape": 100.0, "avg_kd": 0.9, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.02}, {"name": "stressarN", "nationality": "", "kd_ratio": 1.0}, {"name": "robiin", "nationality": "Sweden", "kd_ratio": 0.9}, {"name": "juho", "nationality": "", "kd_ratio": 0.8}, {"name": "MAGILA", "nationality": "", "kd_ratio": 0.8}]}, "team2": {"name": "Kronjyllands Esports Kronjy", "ranking": 247, "ensi_score": 1379, "winrate_10": 40.0, "winrate_30": 33.0, "current_shape": 107.0, "avg_kd": 0.83, "players": [{"name": "Avou", "nationality": "Denmark", "kd_ratio": 0.73}, {"name": "emili0", "nationality": "Denmark", "kd_ratio": 0.73}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.7}, {"name": "Egelund", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "N4xx1s", "nationality": "Denmark", "kd_ratio": 1.0}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Fish123 Fish123", "confidence": 61.36025000000001, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Kronjyllands Esports Kronjy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (72.4265% confidence) | Alternative: TOTAL_MAPS (70.4265%)", "key_factors": ["📈 Better recent form: Fish123 Fish123 (16W-3L vs 2W-15L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: Fish123 Fish123 (#149 vs #247)", "📈 ENSI advantage: Fish123 Fish123 (1433 vs 1379)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.02, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.02}, {"name": "stressarN", "nationality": "", "kd_ratio": 1.0}, {"name": "robiin", "nationality": "Sweden", "kd_ratio": 0.9}, {"name": "juho", "nationality": "", "kd_ratio": 0.8}, {"name": "MAGILA", "nationality": "", "kd_ratio": 0.8}], "team2_players": [{"name": "Avou", "nationality": "Denmark", "kd_ratio": 0.73}, {"name": "emili0", "nationality": "Denmark", "kd_ratio": 0.73}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.7}, {"name": "Egelund", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "N4xx1s", "nationality": "Denmark", "kd_ratio": 1.0}], "team1_avg_kd": 0.9, "team2_avg_kd": 0.83}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "los kogutos\nLK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Fish123\nFish123\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Fish123\nFish123\n\n\n\n\n\n\n\n\n\n\n                    All Fish123 Encounters\n                \n\n\n\n\nKronjyllands esports\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "35\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "10\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "29\n\n\n\n\n\n\nKubix Esports\nkubix", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "45\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "11\n\n\n\n\n\n\nlos kogutos\nLK", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 16, "losses": 3}, "team2_recent_form": {"wins": 2, "losses": 15}, "team1_opponents": ["los", "Fis", "Vol", "Fis", "Gen", "<PERSON><PERSON>", "QMI", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fis", "los", "Fis", "Vol", "Fis", "Gen", "<PERSON><PERSON>", "QMI", "<PERSON><PERSON>"], "team2_opponents": ["Fis", "<PERSON><PERSON>", "Fis", "los", "<PERSON><PERSON>", "ENE", "<PERSON><PERSON>", "Nor"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Smooya": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "stressarN": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "robiin": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "juho": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}, "MAGILA": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}}, "team2_form_trends": {"Avou": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 73.0}, "emili0": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 73.0}, "Jiace": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 70.0}, "Egelund": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "N4xx1s": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "juho", "player1_kd": 0.8, "player2": "<PERSON><PERSON>", "player2_kd": 0.7, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs <PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Fish123 Fish123 to win match", "confidence": 61.36025000000001, "reasoning": ["Team strength difference: 7.6", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Kronjyllands Esports Kronjy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 72.4265, "reasoning": ["Based on team strength difference: 7.6"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 70.4265, "reasoning": ["Team strength analysis: 7.6 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON> vs Egelund", "confidence": 55, "reasoning": ["K/D comparison: 1.02 vs 1.00"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384811-fish123-fish123-vs-kronjyllands-esports-kronjy-untd21-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:28:28.580077"}}, "page_content": ""}, {"team1": {"name": "Copenhagen Wolves", "ranking": 114, "ensi_score": 1472, "winrate_10": 60.0, "winrate_30": 50.0, "current_shape": 110.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.15}, {"name": "Bielany", "nationality": "", "kd_ratio": 1.07}, {"name": "n1xen", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "Tapewaare", "nationality": "Norway", "kd_ratio": 0.87}, {"name": "matheos", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "Parivision", "ranking": 45, "ensi_score": 1688, "winrate_10": 60.0, "winrate_30": 63.0, "current_shape": 97.0, "avg_kd": 1.04, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.35}, {"name": "nota", "nationality": "", "kd_ratio": 1.02}, {"name": "BELCHONOKK", "nationality": "", "kd_ratio": 0.97}, {"name": "Qikert", "nationality": "", "kd_ratio": 0.96}, {"name": "XiELO", "nationality": "", "kd_ratio": 0.92}]}, "h2h_record": "CPHW: 0 - Draws: 0 - PARIVISION: 1 (0% vs 100%)", "prediction": "Parivision", "confidence": 78.19558333333333, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - <PERSON><PERSON> most kills vs <PERSON><PERSON><PERSON> (80% confidence) | Alternative: CORRECT_SCORE (70%)", "key_factors": ["🆚 H2H record: Parivision leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: Copenhagen Wolves (15W-7L vs 8W-13L)", "🏆 Ranking advantage: Parivision (#114 vs #45)", "📈 ENSI advantage: Parivision (1472 vs 1688)", "⚡ Better current shape: Copenhagen Wolves (110.0% vs 97.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "CPHW: 0 - Draws: 0 - PARIVISION: 1 (0% vs 100%)", "team1_wins": 0, "team2_wins": 1, "draws": 0, "recent_matches": [": Copenhagen Wolves 0:2 CPHW"], "team1_name": "CPHW", "team2_name": "PARIVISION", "team1_win_percentage": 0, "team2_win_percentage": 100, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 ESL Challenger League Season 49: Europe", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.15, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "1378180-copen", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.15}, {"name": "Bielany", "nationality": "", "kd_ratio": 1.07}, {"name": "n1xen", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "Tapewaare", "nationality": "Norway", "kd_ratio": 0.87}, {"name": "matheos", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.35}, {"name": "nota", "nationality": "", "kd_ratio": 1.02}, {"name": "BELCHONOKK", "nationality": "", "kd_ratio": 0.97}, {"name": "Qikert", "nationality": "", "kd_ratio": 0.96}, {"name": "XiELO", "nationality": "", "kd_ratio": 0.92}], "team1_avg_kd": 1.01, "team2_avg_kd": 1.04}, "recent_performance": {"team1_recent_matches": [{"score": "0:2", "result": "L", "opponent": "PARIVISION\nPARIVISION\n\n\n\n\n\n\n\n\n\n\n\n            All CPHW and PARIVISION Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Copenhagen Wolves\nCPHW\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "ENCE Academy\nENCE", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Copenhagen Wolves\nCPHW\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:0", "result": "W", "opponent": "38\n\n\n\n\n\n\nCopenhagen Wolves\nCPHW", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "55\n\n\n\n\n\n\nCopenhagen Wolves\nCPHW", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "52\n\n\n\n\n\n\nBetclic Apogee Esports\nBetclic", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "06\n\n\n\n\n\n\nCopenhagen Wolves\nCPHW", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "19\n\n\n\n\n\n\nRebels Gaming\nREBELS", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 15, "losses": 7}, "team2_recent_form": {"wins": 8, "losses": 13}, "team1_opponents": ["PAR", "Ast", "<PERSON><PERSON>", "ENC", "<PERSON><PERSON>", "<PERSON><PERSON>", "PAR", "Rol", "PAR", "PAR", "<PERSON><PERSON>", "PAR", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ast", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "PAR", "Rol", "PAR", "<PERSON><PERSON>"], "team2_opponents": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Bet", "<PERSON><PERSON>", "Ast", "<PERSON><PERSON>", "PAR", "BIG", "Sas", "PAR"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 19, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Copenhagen Wolves", "CPHW", "PARIVISION", "Astrum", "Partizan Esports", "Partizan", "Sashi Esport", "<PERSON><PERSON>", "Rebels Gaming", "REBELS", "AMKAL", "Betclic Apogee Esports", "Betclic", "ENCE Academy", "ENCE.A", "Roler <PERSON>", "BIG", "Dynamo Eclot", "Eclot"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Jackinho": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "Bielany": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "n1xen": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "Tapewaare": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "matheos": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"Jame": {"trend": "STABLE_HIGH", "confidence": 75, "recent_matches": 5, "performance_rating": 135.0}, "nota": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "BELCHONOKK": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "Qikert": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "XiELO": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Tapewaare", "player1_kd": 0.87, "player2": "XiELO", "player2_kd": 0.92, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs XiELO"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"Jame": {"team": "Parivision", "kd_ratio": 1.35, "impact_level": "MEDIUM", "recent_form": "STABLE_HIGH"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Parivision to win match", "confidence": 67.69558333333333, "reasoning": ["Team strength difference: 11.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Copenhagen Wolves +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 68.20294444444446, "reasoning": ["Based on team strength difference: 11.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 11.8 difference"]}, "CORRECT_SCORE": {"prediction": "Parivision 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "<PERSON><PERSON> most kills vs <PERSON><PERSON><PERSON>", "confidence": 80, "reasoning": ["K/D comparison: 1.15 vs 1.35"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378180-copenhagen-wolves-cphw-vs-parivision-parivision-gb-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:28:51.081598"}}, "page_content": ""}, {"team1": {"name": "LOS Kogutos LK", "ranking": 238, "ensi_score": 1383, "winrate_10": 40.0, "winrate_30": 40.0, "current_shape": 100.0, "avg_kd": 0.84, "players": [{"name": "aimy", "nationality": "", "kd_ratio": 0.99}, {"name": "swiz", "nationality": "", "kd_ratio": 0.95}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "next1me", "nationality": "", "kd_ratio": 0.4}, {"name": "elem", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "KS Esports", "ranking": 153, "ensi_score": 1427, "winrate_10": 40.0, "winrate_30": 54.0, "current_shape": 86.0, "avg_kd": 0.95, "players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}]}, "h2h_record": "LK: 0 - Draws: 0 - KS: 1 (0% vs 100%)", "prediction": "KS Esports", "confidence": 60.97033333333333, "betting_advice": "🟢 BEST BET: TOTAL_MAPS - OVER 2.5 maps (3 maps likely) (77.68644444444445% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🆚 H2H record: KS Esports leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: LOS Kogutos LK (10W-7L vs 4W-11L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: KS Esports (#238 vs #153)", "⚡ Better current shape: LOS Kogutos LK (100.0% vs 86.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "LK: 0 - Draws: 0 - KS: 1 (0% vs 100%)", "team1_wins": 0, "team2_wins": 1, "draws": 0, "recent_matches": [": los kogutos 1:2 LK"], "team1_name": "LK", "team2_name": "KS", "team1_win_percentage": 0, "team2_win_percentage": 100, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 European Pro League Season 28: Division 2", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "aimy", "nationality": "", "kd_ratio": 0.99}, {"name": "swiz", "nationality": "", "kd_ratio": 0.95}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "next1me", "nationality": "", "kd_ratio": 0.4}, {"name": "elem", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}], "team1_avg_kd": 0.84, "team2_avg_kd": 0.95}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "KS Esports\nKS\n\n\n\n\n\n\n\n\n\n\n\n            All LK and KS Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "los kogutos\nLK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "los kogutos\nLK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "KS Esports\nKS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Northern Lights\nNL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "30\n\n\n\n\n\n\nlos kogutos\nLK", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "35\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "48\n\n\n\n\n\n\nRebels Gaming\nREBELS", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "13\n\n\n\n\n\n\nlos kogutos\nLK", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "34\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 10, "losses": 7}, "team2_recent_form": {"wins": 4, "losses": 11}, "team1_opponents": ["los", "los", "Nor", "los", "Vol", "los", "los", "los", "Nor", "Vol"], "team2_opponents": ["los", "Fis", "<PERSON><PERSON>", "los", "Gen", "Pre"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"aimy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "swiz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "Nami": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "next1me": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 40.0}, "elem": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"tripey": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "BledarD": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "ammar": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Caleyy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "gejmzilla": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 67.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "next1me", "player1_kd": 0.4, "player2": "gejmzilla", "player2_kd": 0.67, "impact": "MEDIUM", "description": "Tactical battle: next1me vs gejmzilla"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "KS Esports to win match", "confidence": 50.47033333333333, "reasoning": ["Team strength difference: 0.3", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "LOS Kogutos LK +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 0.3"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 77.68644444444445, "reasoning": ["Team strength analysis: 0.3 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "tripey most kills vs elem", "confidence": 71.00000000000001, "reasoning": ["K/D comparison: 1.00 vs 1.11"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384807-los-kogutos-lk-vs-ks-esports-ks-untd21-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:29:13.235948"}}, "page_content": ""}, {"team1": {"name": "Fire Flux Esports FF", "ranking": 121, "ensi_score": 1463, "winrate_10": 40.0, "winrate_30": 37.0, "current_shape": 103.0, "avg_kd": 0.99, "players": [{"name": "Soulfly", "nationality": "", "kd_ratio": 1.26}, {"name": "paz", "nationality": "", "kd_ratio": 0.98}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.97}, {"name": "ScrunK", "nationality": "", "kd_ratio": 0.88}, {"name": "duggy", "nationality": "", "kd_ratio": 0.87}]}, "team2": {"name": "<PERSON><PERSON> 23 06 25", "ranking": 257, "ensi_score": 1374, "winrate_10": 10.0, "winrate_30": 38.0, "current_shape": 73.0, "avg_kd": 0.93, "players": [{"name": "<PERSON><PERSON>z", "nationality": "", "kd_ratio": 0.92}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.89}, {"name": "Muk0s", "nationality": "", "kd_ratio": 0.83}, {"name": "frontales", "nationality": "", "kd_ratio": 1.0}, {"name": "slaxejezzz", "nationality": "", "kd_ratio": 1.0}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Fire Flux Esports FF", "confidence": 72.91033333333334, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - Soulfly most kills vs frontales (80% confidence) | Alternative: MAP_HANDICAP (75.27355555555556%)", "key_factors": ["🏆 Ranking advantage: Fire Flux Esports FF (#121 vs #257)", "📈 ENSI advantage: Fire Flux Esports FF (1463 vs 1374)", "⚡ Better current shape: Fire Flux Esports FF (103.0% vs 73.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 BtcTurk GameFest", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.26, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "<PERSON><PERSON>\"", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "Soulfly", "nationality": "", "kd_ratio": 1.26}, {"name": "paz", "nationality": "", "kd_ratio": 0.98}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.97}, {"name": "ScrunK", "nationality": "", "kd_ratio": 0.88}, {"name": "duggy", "nationality": "", "kd_ratio": 0.87}], "team2_players": [{"name": "<PERSON><PERSON>z", "nationality": "", "kd_ratio": 0.92}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.89}, {"name": "Muk0s", "nationality": "", "kd_ratio": 0.83}, {"name": "frontales", "nationality": "", "kd_ratio": 1.0}, {"name": "slaxejezzz", "nationality": "", "kd_ratio": 1.0}], "team1_avg_kd": 0.99, "team2_avg_kd": 0.93}, "recent_performance": {"team1_recent_matches": [{"score": "0:2", "result": "L", "opponent": "Bushido Wildcats\nBW\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Fire Flux Esports\nFF\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Fire Flux Esports\nFF\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "<PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "RUBY\nRUBY\n\n\n\n\n\n\n\n\n\n\n                    All FF Encounters\n                \n\n\n\n\nHesta\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:0", "result": "W", "opponent": "24\n\n\n\n\n\n\nFire Flux Esports\nFF", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "18\n\n\n\n\n\n\nEternal Fire\nEF", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "38\n\n\n\n\n\n\nBushido Wildcats\nBW", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "04\n\n\n\n\n\n\nFire Flux Esports\nFF", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "45\n\n\n\n\n\n\nFire Flux Esports\nFF", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 10, "losses": 11}, "team2_recent_form": {"wins": 9, "losses": 9}, "team1_opponents": ["Bus", "Fir", "Fir", "Mar", "RUB", "<PERSON>s", "Und", "<PERSON>s", "inp", "<PERSON>s", "<PERSON>s", "RUB", "Bus", "Fir", "Fir", "Mar", "RUB", "<PERSON>s", "Und", "<PERSON>s", "inp"], "team2_opponents": ["Fir", "Ete", "Bus", "Fir", "FAV", "<PERSON>s", "RAG", "<PERSON>s"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 14, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["CYBERSHOKE Esports", "FAVBET Team", "FAVBET", "Sangal Esports", "<PERSON><PERSON>", "Fire Flux Esports", "Bushido Wildcats", "Eternal Fire", "<PERSON>", "RUBY", "<PERSON><PERSON>", "Underrated", "RAGE", "inputprayers"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Soulfly": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 126.0}, "paz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "Banjo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "ScrunK": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "duggy": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}}, "team2_form_trends": {"anttzz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "abiraju": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}, "Muk0s": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 83.0}, "frontales": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "slaxejezzz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "duggy", "player1_kd": 0.87, "player2": "Muk0s", "player2_kd": 0.83, "impact": "MEDIUM", "description": "Tactical battle: duggy vs Muk<PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"Soulfly": {"team": "Fire Flux Esports FF", "kd_ratio": 1.26, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Fire Flux Esports FF to win match", "confidence": 72.91033333333334, "reasoning": ["Team strength difference: 15.3", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Fire Flux Esports FF -1.5 maps (2-0 win)", "confidence": 75.27355555555556, "reasoning": ["Based on team strength difference: 15.3"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 15.3 difference"]}, "CORRECT_SCORE": {"prediction": "Fire Flux Esports FF 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Fire Flux Esports FF first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Soulfly most kills vs frontales", "confidence": 80, "reasoning": ["K/D comparison: 1.26 vs 1.00"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1365022-fire-flux-esports-ff-vs-he<PERSON>-he<PERSON>-win-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:29:34.045729"}}, "page_content": ""}, {"team1": {"name": "Nexus Gaming", "ranking": 59, "ensi_score": 1610, "winrate_10": 70.0, "winrate_30": 57.0, "current_shape": 113.0, "avg_kd": 1.03, "players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}]}, "team2": {"name": "K27 K27 EXO 23 06 25", "ranking": 98, "ensi_score": 1507, "winrate_10": 70.0, "winrate_30": 57.0, "current_shape": 113.0, "avg_kd": 0.95, "players": [{"name": "kashl1d", "nationality": "", "kd_ratio": 1.13}, {"name": "relaxxie", "nationality": "", "kd_ratio": 0.96}, {"name": "qw1nk1", "nationality": "", "kd_ratio": 0.95}, {"name": "X5G7V", "nationality": "", "kd_ratio": 0.86}, {"name": "xeedo", "nationality": "", "kd_ratio": 0.85}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Nexus Gaming", "confidence": 65.22916666666666, "betting_advice": "🟢 BEST BET: CORRECT_SCORE - Nexus Gaming 2-1 (70% confidence) | Alternative: MAP_HANDICAP (69.84722222222223%)", "key_factors": ["📈 Better recent form: Nexus Gaming (14W-8L vs 8W-11L)", "🏆 Ranking advantage: Nexus Gaming (#59 vs #98)", "📈 ENSI advantage: Nexus Gaming (1610 vs 1507)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 Galaxy Battle Phase 3", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.21, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}], "team2_players": [{"name": "kashl1d", "nationality": "", "kd_ratio": 1.13}, {"name": "relaxxie", "nationality": "", "kd_ratio": 0.96}, {"name": "qw1nk1", "nationality": "", "kd_ratio": 0.95}, {"name": "X5G7V", "nationality": "", "kd_ratio": 0.86}, {"name": "xeedo", "nationality": "", "kd_ratio": 0.85}], "team1_avg_kd": 1.03, "team2_avg_kd": 0.95}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "TEAM NEXT LEVEL\nTNL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "ALGO Esports\nALGO\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Nexus Gaming\nNexus\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "<PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Nexus Gaming\nNexus\n\n\n\n\n\n\n\n\n\n\n                    All Nexus Encounters\n                \n\n\n\n\nK27\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "05\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "21\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "38\n\n\n\n\n\n\nGUN5 Esports\nGUN5", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "52\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "33\n\n\n\n\n\n\nAstrum\nAstrum", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 8}, "team2_recent_form": {"wins": 8, "losses": 11}, "team1_opponents": ["TEA", "ALG", "Nex", "Mar", "Nex", "K27", "VOD", "Bus", "U<PERSON>", "K27", "K27", "Nex", "TEA", "ALG", "Nex", "Mar", "Nex", "K27", "VOD", "Bus", "U<PERSON>", "K27"], "team2_opponents": ["Nex", "Nex", "GUN", "Nex", "Ast", "SPA", "K27", "K27", "K27"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 16, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Astrum", "Nexus Gaming", "Nexus", "TEAM NEXT LEVEL", "TNL", "ALGO Esports", "ALGO", "GUN5 Esports", "GUN5", "<PERSON>", "SPARTA", "K27", "VODKA TEAM", "VODKA", "Bushido Wildcats", "Ursa"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"XELLOW": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "ragga": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "lauNX": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "s0und": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "BTN": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 75.0}}, "team2_form_trends": {"kashl1d": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "relaxxie": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "qw1nk1": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "X5G7V": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 86.0}, "xeedo": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 85.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "BTN", "player1_kd": 0.75, "player2": "xeedo", "player2_kd": 0.85, "impact": "MEDIUM", "description": "Tactical battle: BTN vs xeedo"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"XELLOW": {"team": "Nexus Gaming", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Nexus Gaming to win match", "confidence": 65.22916666666666, "reasoning": ["Team strength difference: 10.2", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "K27 K27 EXO 23 06 25 +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 69.84722222222223, "reasoning": ["Based on team strength difference: 10.2"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 10.2 difference"]}, "CORRECT_SCORE": {"prediction": "Nexus Gaming 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: XELLOW vs kashl1d", "confidence": 55, "reasoning": ["K/D comparison: 1.21 vs 1.13"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1363317-nexus-gaming-nexus-vs-k27-k27-exo-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:29:56.995141"}}, "page_content": ""}, {"team1": {"name": "Genone Gone", "ranking": 102, "ensi_score": 1496, "winrate_10": 60.0, "winrate_30": 60.0, "current_shape": 100.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "Rebels Gaming", "ranking": 92, "ensi_score": 1531, "winrate_10": 60.0, "winrate_30": 47.0, "current_shape": 113.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.22}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "Sobol", "nationality": "", "kd_ratio": 0.98}, {"name": "innocent", "nationality": "", "kd_ratio": 0.96}, {"name": "Qlocu<PERSON>", "nationality": "", "kd_ratio": 0.81}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Rebels Gaming", "confidence": 55.43008333333334, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Genone Gone +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (75% confidence) | Alternative: TOTAL_MAPS (74.37994444444445%)", "key_factors": ["🏆 Ranking advantage: Rebels Gaming (#102 vs #92)", "⚡ Better current shape: Rebels Gaming (100.0% vs 113.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.16, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.22}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "Sobol", "nationality": "", "kd_ratio": 0.98}, {"name": "innocent", "nationality": "", "kd_ratio": 0.96}, {"name": "Qlocu<PERSON>", "nationality": "", "kd_ratio": 0.81}], "team1_avg_kd": 1.01, "team2_avg_kd": 1.01}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "GenOne\nGOne\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "ENERGYULTRA\nENY\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "los kogutos\nLK\n\n\n\n\n\n\n\n\n\n\n                    All GOne Encounters\n                \n\n\n\n\nRebels Gaming\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "25\n\n\n\n\n\n\nKronjyllands esports\nKronjy", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "09\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "57\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "28\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "34\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 12, "losses": 8}, "team2_recent_form": {"wins": 7, "losses": 9}, "team1_opponents": ["Gen", "ENE", "Vol", "los", "PAR", "<PERSON><PERSON>", "los", "<PERSON><PERSON>", "<PERSON><PERSON>", "los", "Gen", "ENE", "Vol", "los", "PAR", "<PERSON><PERSON>", "los", "<PERSON><PERSON>"], "team2_opponents": ["<PERSON><PERSON>", "Gen", "Gen", "Gen", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 19, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Kronjyllands esports", "<PERSON><PERSON><PERSON><PERSON>", "los kogutos", "Ex-<PERSON><PERSON><PERSON>", "Ex-<PERSON><PERSON>", "kONO.ECF", "kONO", "GenOne", "GOne", "ENERGYULTRA", "ENY", "XI Esport", "Volt", "Rebels Gaming", "REBELS", "PARIVISION", "9INE", "Copenhagen Wolves", "CPHW"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Chucky": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "Brooxsy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Tarkky": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "cHeuuuuk": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "SLIE9000": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"Flayy": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 122.0}, "kisserek": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "Sobol": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "innocent": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "Qlocuu": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 81.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "player1_kd": 0.87, "player2": "Qlocu<PERSON>", "player2_kd": 0.81, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"Flayy": {"team": "Rebels Gaming", "kd_ratio": 1.22, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Rebels Gaming to win match", "confidence": 55.43008333333334, "reasoning": ["Team strength difference: 3.6", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Genone Gone +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 3.6"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 74.37994444444445, "reasoning": ["Team strength analysis: 3.6 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Rebels Gaming first map", "confidence": 61.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "confidence": 55, "reasoning": ["K/D comparison: 1.16 vs 1.22"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384806-genone-gone-vs-rebels-gaming-rebels-untd21-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:30:17.963449"}}, "page_content": ""}, {"team1": {"name": "Rustec Rustec", "ranking": 221, "ensi_score": 1386, "winrate_10": 33.0, "winrate_30": 33.0, "current_shape": 100.0, "avg_kd": 0.98, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.34}, {"name": "y<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.92}, {"name": "swiftsteel", "nationality": "", "kd_ratio": 0.85}, {"name": "Brilliance", "nationality": "", "kd_ratio": 0.81}, {"name": "fl1nt", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "Eternal Fire EF EXO 23 06 25", "ranking": 7, "ensi_score": 1885, "winrate_10": 80.0, "winrate_30": 67.0, "current_shape": 113.0, "avg_kd": 0.96, "players": [{"name": "lugseN", "nationality": "", "kd_ratio": 1.11}, {"name": "Calyx", "nationality": "", "kd_ratio": 1.08}, {"name": "imoRR", "nationality": "", "kd_ratio": 1.0}, {"name": "emstar", "nationality": "", "kd_ratio": 0.88}, {"name": "j<PERSON>y", "nationality": "", "kd_ratio": 0.73}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Eternal Fire EF EXO 23 06 25", "confidence": 95, "betting_advice": "🟢 BEST BET: MATCH_WINNER - Eternal Fire EF EXO 23 06 25 to win match (85% confidence) | Alternative: PLAYER_PROPS (80%)", "key_factors": ["🏆 Ranking advantage: Eternal Fire EF EXO 23 06 25 (#221 vs #7)", "📈 ENSI advantage: Eternal Fire EF EXO 23 06 25 (1386 vs 1885)", "⚡ Better current shape: Eternal Fire EF EXO 23 06 25 (100.0% vs 113.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO1", "tier": "Tier-3", "tournament": "2025 Exort Series #12", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.34, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "Unknown Tournament", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.34}, {"name": "y<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.92}, {"name": "swiftsteel", "nationality": "", "kd_ratio": 0.85}, {"name": "Brilliance", "nationality": "", "kd_ratio": 0.81}, {"name": "fl1nt", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "lugseN", "nationality": "", "kd_ratio": 1.11}, {"name": "Calyx", "nationality": "", "kd_ratio": 1.08}, {"name": "imoRR", "nationality": "", "kd_ratio": 1.0}, {"name": "emstar", "nationality": "", "kd_ratio": 0.88}, {"name": "j<PERSON>y", "nationality": "", "kd_ratio": 0.73}], "team1_avg_kd": 0.98, "team2_avg_kd": 0.96}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "RUSTEC\nRUSTEC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Bushido Wildcats\nBW\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "RUSTEC\nRUSTEC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "<PERSON><PERSON><PERSON> Gangsters\nFG\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "Nemiga Gaming\nNemiga\n\n\n\n\n\n\n\n\n\n\n                    All RUSTEC Encounters\n                \n\n\n\n\nEternal Fire\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "44\n\n\n\n\n\n\nTPuDCATb TPu\nTPu", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "12\n\n\n\n\n\n\nRUSTEC\nRUSTEC", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "50\n\n\n\n\n\n\nTEAM NEXT LEVEL\nTNL", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "35\n\n\n\n\n\n\nRUSTEC\nRUSTEC", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "48\n\n\n\n\n\n\nRUSTEC\nRUSTEC", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 9, "losses": 12}, "team2_recent_form": {"wins": 9, "losses": 9}, "team1_opponents": ["RUS", "Bus", "RUS", "Flu", "Nem", "Ete", "BAS", "Fir", "San", "TEA", "TEA", "Nem", "RUS", "Bus", "RUS", "Flu", "Nem", "Ete", "BAS", "Fir", "TEA"], "team2_opponents": ["<PERSON><PERSON>", "TPu", "RUS", "TEA", "RUS", "RUS", "Ete", "Ete", "Ete"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Bushido Wildcats", "TPuDCATb TPu", "TPu", "RUSTEC", "TEAM NEXT LEVEL", "TNL", "<PERSON><PERSON><PERSON>sters", "Nemiga Gaming", "Nemiga", "FORZE Reload", "<PERSON><PERSON>", "Eternal Fire", "BASEMENT BOYS", "BSM", "Fire Flux Esports", "Sangal Academy", "Sangal.A"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Wadeshot": {"trend": "STABLE_HIGH", "confidence": 75, "recent_matches": 5, "performance_rating": 134.0}, "yiksrezo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "swiftsteel": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 85.0}, "Brilliance": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 81.0}, "fl1nt": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"lugseN": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "Calyx": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "imoRR": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "emstar": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "jresy": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 73.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Brilliance", "player1_kd": 0.81, "player2": "j<PERSON>y", "player2_kd": 0.73, "impact": "MEDIUM", "description": "Tactical battle: Brilliance vs jresy"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"Wadeshot": {"team": "Rustec Rustec", "kd_ratio": 1.34, "impact_level": "MEDIUM", "recent_form": "STABLE_HIGH"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Eternal Fire EF EXO 23 06 25 to win match", "confidence": 85, "reasoning": ["Team strength difference: 42.8", "Primary betting market"]}, "FIRST_MAP_WINNER": {"prediction": "Eternal Fire EF EXO 23 06 25 first map", "confidence": 60.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "<PERSON>shot most kills vs lugseN", "confidence": 80, "reasoning": ["K/D comparison: 1.34 vs 1.11"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1363318-rustec-rustec-vs-eternal-fire-ef-exo-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:30:39.800981"}}, "page_content": ""}, {"team1": {"name": "Iberian Soul IS", "ranking": 44, "ensi_score": 1689, "winrate_10": 80.0, "winrate_30": 60.0, "current_shape": 120.0, "avg_kd": 0.94, "players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}]}, "team2": {"name": "Nexus Gaming", "ranking": 59, "ensi_score": 1610, "winrate_10": 70.0, "winrate_30": 57.0, "current_shape": 113.0, "avg_kd": 1.03, "players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Iberian Soul IS", "confidence": 61.839083333333335, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - XELLOW most kills vs sausol (79.0% confidence) | Alternative: MAP_HANDICAP (72.10727777777777%)", "key_factors": ["📈 Better recent form: Iberian Soul IS (12W-7L vs 6W-11L)", "🏆 Ranking advantage: Iberian Soul IS (#44 vs #59)", "📈 ENSI advantage: Iberian Soul IS (1689 vs 1610)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 Galaxy Battle Phase 3", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.02, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}], "team2_players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}], "team1_avg_kd": 0.94, "team2_avg_kd": 1.03}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "BC", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Leo Team\nLeo\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n                    All IS Encounters\n                \n\n\n\n\nNexus Gaming\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "TEAM NEXT LEVEL\nTNL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "50\n\n\n\n\n\n\nIberian Soul\nIS", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "20\n\n\n\n\n\n\nIberian Soul\nIS", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "57\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "56\n\n\n\n\n\n\n500\n500", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "05\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 12, "losses": 7}, "team2_recent_form": {"wins": 6, "losses": 11}, "team1_opponents": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "TEA", "ALG", "Nex", "Mar", "Nex", "Nex", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "TEA", "ALG", "Nex", "Mar", "Nex"], "team2_opponents": ["<PERSON><PERSON>", "<PERSON>n", "Nex", "Nex", "GUN", "Nex", "Ast"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 26, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["CYBERSHOKE Esports", "Sashi Esport", "<PERSON><PERSON>", "Monte", "FAVBET Team", "FAVBET", "Astrum", "Iberian Soul", "BC.Game Esports", "BC.G", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>.", "Leo Team", "<PERSON>", "Ninjas in Pyjamas", "NiP", "500", "Nexus Gaming", "Nexus", "TEAM NEXT LEVEL", "TNL", "ALGO Esports", "ALGO", "GUN5 Esports", "GUN5", "<PERSON>"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"sausol": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "stadodo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "mopoz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "ALEX": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "dav1g": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "team2_form_trends": {"XELLOW": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "ragga": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "lauNX": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "s0und": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "BTN": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 75.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "dav1g", "player1_kd": 0.79, "player2": "BTN", "player2_kd": 0.75, "impact": "MEDIUM", "description": "Tactical battle: dav1g vs BTN"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"XELLOW": {"team": "Nexus Gaming", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Iberian Soul IS to win match", "confidence": 61.839083333333335, "reasoning": ["Team strength difference: 7.9", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Nexus Gaming +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 72.10727777777777, "reasoning": ["Based on team strength difference: 7.9"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 70.10727777777777, "reasoning": ["Team strength analysis: 7.9 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "XELLOW most kills vs sausol", "confidence": 79.0, "reasoning": ["K/D comparison: 1.02 vs 1.21"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378181-iberian-soul-is-vs-nexus-gaming-nexus-gb-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:31:02.927890"}}, "page_content": ""}, {"team1": {"name": "Amkal", "ranking": 72, "ensi_score": 1573, "winrate_10": 70.0, "winrate_30": 57.0, "current_shape": 113.0, "avg_kd": 0.97, "players": [{"name": "tommy171", "nationality": "", "kd_ratio": 1.15}, {"name": "AW", "nationality": "", "kd_ratio": 1.09}, {"name": "kAlash", "nationality": "", "kd_ratio": 0.94}, {"name": "sstiNiX", "nationality": "", "kd_ratio": 0.92}, {"name": "sFade8", "nationality": "", "kd_ratio": 0.77}]}, "team2": {"name": "Unity Esports Unity WIN 23 06 25", "ranking": 301, "ensi_score": 1337, "winrate_10": 10.0, "winrate_30": 30.0, "current_shape": 80.0, "avg_kd": 0.87, "players": [{"name": "neofrag", "nationality": "", "kd_ratio": 1.01}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "woozzzi", "nationality": "", "kd_ratio": 0.86}, {"name": "K1-FiDa", "nationality": "", "kd_ratio": 0.8}, {"name": "PerdY", "nationality": "", "kd_ratio": 0.8}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Amkal", "confidence": 95, "betting_advice": "🟢 BEST BET: MATCH_WINNER - Amkal to win match (85% confidence) | Alternative: MAP_HANDICAP (80%)", "key_factors": ["📈 Better recent form: Unity Esports Unity WIN 23 06 25 (12W-6L vs 6W-11L)", "🏆 Ranking advantage: <PERSON><PERSON> (#72 vs #301)", "📈 ENSI advantage: <PERSON><PERSON> (1573 vs 1337)", "⚡ Better current shape: Amkal (113.0% vs 80.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 3 European Series #3", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.15, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "tommy171", "nationality": "", "kd_ratio": 1.15}, {"name": "AW", "nationality": "", "kd_ratio": 1.09}, {"name": "kAlash", "nationality": "", "kd_ratio": 0.94}, {"name": "sstiNiX", "nationality": "", "kd_ratio": 0.92}, {"name": "sFade8", "nationality": "", "kd_ratio": 0.77}], "team2_players": [{"name": "neofrag", "nationality": "", "kd_ratio": 1.01}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "woozzzi", "nationality": "", "kd_ratio": 0.86}, {"name": "K1-FiDa", "nationality": "", "kd_ratio": 0.8}, {"name": "PerdY", "nationality": "", "kd_ratio": 0.8}], "team1_avg_kd": 0.97, "team2_avg_kd": 0.87}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "AMKAL\nAMKAL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "AMKAL\nAMKAL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "AMKAL\nAMKAL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "AMKAL\nAMKAL\n\n\n\n\n\n\n\n\n\n\n                    All AMKAL Encounters\n                \n\n\n\n\nUNiTY Esports\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "UNiTY Esports\nUNiTY\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "40\n\n\n\n\n\n\n500\n500", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "34\n\n\n\n\n\n\nPartizan Esports\nPartizan", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "08\n\n\n\n\n\n\nRUBY\nRUBY", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "21\n\n\n\n\n\n\nFisher College\nFC", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "52\n\n\n\n\n\n\nDynamo Eclot\nEclot", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 6, "losses": 11}, "team2_recent_form": {"wins": 12, "losses": 6}, "team1_opponents": ["AMK", "AMK", "AMK", "AMK", "UNi", "Nov", "<PERSON><PERSON>", "UNi", "UNi", "AMK", "AMK", "AMK", "AMK", "UNi", "Nov", "<PERSON><PERSON>", "UNi"], "team2_opponents": ["Par", "RUB", "Fis", "<PERSON><PERSON>", "FAV", "UNi", "UNi", "mod"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 20, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Zero Tenacity", "Z10", "RUBY", "Fisher College", "FAVBET Team", "FAVBET", "JANO Esports", "JANO", "500", "AMKAL", "Partizan Esports", "Partizan", "Dynamo Eclot", "Eclot", "UNiTY Esports", "UNiTY", "Novaq", "Copenhagen Wolves", "CPHW", "modeame"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"tommy171": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "AW": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "kAlash": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "sstiNiX": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "sFade8": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 77.0}}, "team2_form_trends": {"neofrag": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "Darber": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "woozzzi": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 86.0}, "K1-FiDa": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}, "PerdY": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "sFade8", "player1_kd": 0.77, "player2": "K1-FiDa", "player2_kd": 0.8, "impact": "MEDIUM", "description": "Tactical battle: s<PERSON>ade8 vs K1-FiDa"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Amkal to win match", "confidence": 85, "reasoning": ["Team strength difference: 31.0", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Amkal -1.5 maps (2-0 win)", "confidence": 80, "reasoning": ["Based on team strength difference: 31.0"]}, "TOTAL_MAPS": {"prediction": "UNDER 2.5 maps (2-0 likely)", "confidence": 76.0183888888889, "reasoning": ["Team strength analysis: 31.0 difference"]}, "CORRECT_SCORE": {"prediction": "Amkal 2-0", "confidence": 75, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Amkal first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "tommy171 most kills vs neofrag", "confidence": 73.99999999999999, "reasoning": ["K/D comparison: 1.15 vs 1.01"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1365021-amkal-amkal-vs-unity-esports-unity-win-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 00:31:25.485308"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 2400.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 100, "recommendations": [{"match": "FURIA Esports Female Furiafe vs Flamengo Esports Flamengo CCT SA 24 06 25", "bet_type": "Moneyline", "recommendation": "FURIA Esports Female Furiafe to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 26.0", "Primary betting market"]}, {"match": "Keyd Stars vs KRU Esport KRU CCT SA 24 06 25", "bet_type": "Moneyline", "recommendation": "KRU Esport KRU CCT SA 24 06 25 to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 32.3", "Primary betting market"]}, {"match": "Rustec Rustec vs Eternal Fire EF EXO 23 06 25", "bet_type": "Moneyline", "recommendation": "Eternal Fire EF EXO 23 06 25 to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 42.8", "Primary betting market"]}, {"match": "Amkal vs Unity Esports Unity WIN 23 06 25", "bet_type": "Moneyline", "recommendation": "Amkal to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 31.0", "Primary betting market"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Moneyline", "recommendation": "9z Team to win match", "confidence": 81.85091666666665, "value_rating": 0.637018333333333, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 21.2", "Primary betting market"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Map Handicap", "recommendation": "9z Team -1.5 maps (2-0 win)", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 21.2"]}, {"match": "Keyd Stars vs KRU Esport KRU CCT SA 24 06 25", "bet_type": "Player Props", "recommendation": "desh most kills vs righi", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.32 vs 1.06"]}, {"match": "Copenhagen Wolves vs Parivision", "bet_type": "Player Props", "recommendation": "<PERSON><PERSON> most kills vs <PERSON><PERSON><PERSON>", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.15 vs 1.35"]}, {"match": "Fire Flux Esports FF vs Hesta Hesta WIN 23 06 25", "bet_type": "Player Props", "recommendation": "Soulfly most kills vs frontales", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.26 vs 1.00"]}, {"match": "Rustec Rustec vs Eternal Fire EF EXO 23 06 25", "bet_type": "Player Props", "recommendation": "<PERSON>shot most kills vs lugseN", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.34 vs 1.11"]}, {"match": "Amkal vs Unity Esports Unity WIN 23 06 25", "bet_type": "Map Handicap", "recommendation": "Amkal -1.5 maps (2-0 win)", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 31.0"]}, {"match": "Iberian Soul IS vs Nexus Gaming", "bet_type": "Player Props", "recommendation": "XELLOW most kills vs sausol", "confidence": 79.0, "value_rating": 0.5800000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.02 vs 1.21"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Player Props", "recommendation": "MartinezSa most kills vs maxxkor", "confidence": 78.0, "value_rating": 0.56, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.19 vs 1.01"]}, {"match": "LOS Kogutos LK vs KS Esports", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 77.68644444444445, "value_rating": 0.5537288888888889, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 0.3 difference"]}, {"match": "XI Esport vs EX Astralis Talent EX Astra", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 76.17722222222223, "value_rating": 0.5235444444444446, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 1.8 difference"]}, {"match": "Amkal vs Unity Esports Unity WIN 23 06 25", "bet_type": "Total Maps", "recommendation": "UNDER 2.5 maps (2-0 likely)", "confidence": 76.0183888888889, "value_rating": 0.5203677777777778, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 31.0 difference"]}, {"match": "Fire Flux Esports FF vs Hesta Hesta WIN 23 06 25", "bet_type": "Map Handicap", "recommendation": "Fire Flux Esports FF -1.5 maps (2-0 win)", "confidence": 75.27355555555556, "value_rating": 0.5054711111111112, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 15.3"]}, {"match": "XI Esport vs EX Astralis Talent EX Astra", "bet_type": "Map Handicap", "recommendation": "XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 1.8"]}, {"match": "LOS Kogutos LK vs KS Esports", "bet_type": "Map Handicap", "recommendation": "LOS Kogutos LK +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 0.3"]}, {"match": "Genone Gone vs Rebels Gaming", "bet_type": "Map Handicap", "recommendation": "Genone Gone +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 3.6"]}, {"match": "FURIA Esports Female Furiafe vs Flamengo Esports Flamengo CCT SA 24 06 25", "bet_type": "Player Props", "recommendation": "kaahSENSEI most kills vs Misfit", "confidence": 74.99999999999999, "value_rating": 0.4999999999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.43 vs 1.28"]}, {"match": "ESC Gaming vs HEROIC Academy", "bet_type": "Player Props", "recommendation": "reiko most kills vs fnl", "confidence": 74.99999999999999, "value_rating": 0.4999999999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.14 vs 0.99"]}, {"match": "Genone Gone vs Rebels Gaming", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 74.37994444444445, "value_rating": 0.4875988888888889, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 3.6 difference"]}, {"match": "Amkal vs Unity Esports Unity WIN 23 06 25", "bet_type": "Player Props", "recommendation": "tommy171 most kills vs neofrag", "confidence": 73.99999999999999, "value_rating": 0.47999999999999976, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.15 vs 1.01"]}, {"match": "Fire Flux Esports FF vs Hesta Hesta WIN 23 06 25", "bet_type": "Moneyline", "recommendation": "Fire Flux Esports FF to win match", "confidence": 72.91033333333334, "value_rating": 0.4582066666666669, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 15.3", "Primary betting market"]}, {"match": "Fish123 Fish123 vs Kronjyllands Esports Kronjy", "bet_type": "Map Handicap", "recommendation": "Kronjyllands Esports Kronjy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 72.4265, "value_rating": 0.4485300000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 7.6"]}, {"match": "Iberian Soul IS vs Nexus Gaming", "bet_type": "Map Handicap", "recommendation": "Nexus Gaming +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 72.10727777777777, "value_rating": 0.44214555555555535, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 7.9"]}, {"match": "ESC Gaming vs HEROIC Academy", "bet_type": "Moneyline", "recommendation": "ESC Gaming to win match", "confidence": 71.88325, "value_rating": 0.43766499999999997, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 14.6", "Primary betting market"]}, {"match": "LOS Kogutos LK vs KS Esports", "bet_type": "Player Props", "recommendation": "tripey most kills vs elem", "confidence": 71.00000000000001, "value_rating": 0.4200000000000004, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.00 vs 1.11"]}, {"match": "Fish123 Fish123 vs Kronjyllands Esports Kronjy", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 70.4265, "value_rating": 0.40853000000000006, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 7.6 difference"]}, {"match": "Iberian Soul IS vs Nexus Gaming", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 70.10727777777777, "value_rating": 0.4021455555555553, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 7.9 difference"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Total Rounds", "recommendation": "9z Team first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Fire Flux Esports FF vs Hesta Hesta WIN 23 06 25", "bet_type": "Total Rounds", "recommendation": "Fire Flux Esports FF first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Amkal vs Unity Esports Unity WIN 23 06 25", "bet_type": "Total Rounds", "recommendation": "Amkal first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Nexus Gaming vs K27 K27 EXO 23 06 25", "bet_type": "Map Handicap", "recommendation": "K27 K27 EXO 23 06 25 +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 69.84722222222223, "value_rating": 0.39694444444444454, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 10.2"]}, {"match": "Copenhagen Wolves vs Parivision", "bet_type": "Map Handicap", "recommendation": "Copenhagen Wolves +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 68.20294444444446, "value_rating": 0.36405888888888915, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 11.8"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Total Maps", "recommendation": "UNDER 2.5 maps (2-0 likely)", "confidence": 66.23394444444443, "value_rating": 0.32467888888888874, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 21.2 difference"]}, {"match": "ESC Gaming vs HEROIC Academy", "bet_type": "Map Handicap", "recommendation": "HEROIC Academy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 65.41116666666666, "value_rating": 0.3082233333333333, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 14.6"]}, {"match": "FURIA Esports Female Furiafe vs Flamengo Esports Flamengo CCT SA 24 06 25", "bet_type": "Total Rounds", "recommendation": "FURIA Esports Female Furiafe first map", "confidence": 65.0, "value_rating": 0.30000000000000004, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "XI Esport vs EX Astralis Talent EX Astra", "bet_type": "Total Rounds", "recommendation": "XI Esport first map", "confidence": 64.5, "value_rating": 0.29000000000000004, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Keyd Stars vs KRU Esport KRU CCT SA 24 06 25", "bet_type": "Total Rounds", "recommendation": "KRU Esport KRU CCT SA 24 06 25 first map", "confidence": 63.0, "value_rating": 0.26, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Genone Gone vs Rebels Gaming", "bet_type": "Total Rounds", "recommendation": "Rebels Gaming first map", "confidence": 61.5, "value_rating": 0.22999999999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "ESC Gaming vs HEROIC Academy", "bet_type": "Total Rounds", "recommendation": "ESC Gaming first map", "confidence": 61.0, "value_rating": 0.21999999999999997, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Rustec Rustec vs Eternal Fire EF EXO 23 06 25", "bet_type": "Total Rounds", "recommendation": "Eternal Fire EF EXO 23 06 25 first map", "confidence": 60.5, "value_rating": 0.20999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "ESC Gaming vs HEROIC Academy", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 14.6 difference"]}, {"match": "Copenhagen Wolves vs Parivision", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 11.8 difference"]}, {"match": "Fire Flux Esports FF vs Hesta Hesta WIN 23 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 15.3 difference"]}, {"match": "Nexus Gaming vs K27 K27 EXO 23 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 10.2 difference"]}]}, "enhancement_stats": {"total_predictions": 14, "average_confidence": 76.27816904761903, "premium_bets": 5, "strong_bets": 2, "good_bets": 1, "lean_bets": 1, "confidence_distribution": {"premium_pct": 35.714285714285715, "strong_pct": 14.285714285714285, "good_pct": 7.142857142857142, "lean_pct": 7.142857142857142}}}