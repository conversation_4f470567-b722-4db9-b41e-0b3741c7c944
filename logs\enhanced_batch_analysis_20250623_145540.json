{"timestamp": "20250623_145540", "total_matches": 15, "successful_scrapes": 15, "failed_urls": [], "success_rate": 100.0, "processing_time": 324.**************, "min_confidence": 70.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "Keyd Stars", "ranking": 131, "ensi_score": 1401, "winrate_10": 50.0, "winrate_30": 47.0, "current_shape": 103.0, "avg_kd": 0.94, "players": [{"name": "desh", "nationality": "", "kd_ratio": 1.32}, {"name": "vinaabEAST", "nationality": "", "kd_ratio": 0.99}, {"name": "leo_drk", "nationality": "", "kd_ratio": 0.93}, {"name": "ninjaZ", "nationality": "", "kd_ratio": 0.78}, {"name": "flash", "nationality": "", "kd_ratio": 0.67}]}, "team2": {"name": "KRU Esport KRU CCT SA 24 06 25", "ranking": 3, "ensi_score": 1451, "winrate_10": 50.0, "winrate_30": 33.0, "current_shape": 117.0, "avg_kd": 0.96, "players": [{"name": "righi", "nationality": "", "kd_ratio": 1.06}, {"name": "ataraXia", "nationality": "", "kd_ratio": 1.02}, {"name": "deco", "nationality": "", "kd_ratio": 0.98}, {"name": "laser", "nationality": "", "kd_ratio": 0.92}, {"name": "reversive", "nationality": "", "kd_ratio": 0.84}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "KRU Esport KRU CCT SA 24 06 25", "confidence": 95, "betting_advice": "🟢 BEST BET: MATCH_WINNER - KRU Esport KRU CCT SA 24 06 25 to win match (85% confidence) | Alternative: PLAYER_PROPS (80%)", "key_factors": ["📈 Better recent form: KRU Esport KRU CCT SA 24 06 25 (15W-8L vs 7W-14L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: KRU Esport KRU CCT SA 24 06 25 (#131 vs #3)", "📈 ENSI advantage: KRU Esport KRU CCT SA 24 06 25 (1401 vs 1451)", "⚡ Better current shape: KRU Esport KRU CCT SA 24 06 25 (103.0% vs 117.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO1", "tier": "Tier-3", "tournament": "2021 BLAST Rising LATAM", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.32, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "Unknown Tournament", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "desh", "nationality": "", "kd_ratio": 1.32}, {"name": "vinaabEAST", "nationality": "", "kd_ratio": 0.99}, {"name": "leo_drk", "nationality": "", "kd_ratio": 0.93}, {"name": "ninjaZ", "nationality": "", "kd_ratio": 0.78}, {"name": "flash", "nationality": "", "kd_ratio": 0.67}], "team2_players": [{"name": "righi", "nationality": "", "kd_ratio": 1.06}, {"name": "ataraXia", "nationality": "", "kd_ratio": 1.02}, {"name": "deco", "nationality": "", "kd_ratio": 0.98}, {"name": "laser", "nationality": "", "kd_ratio": 0.92}, {"name": "reversive", "nationality": "", "kd_ratio": 0.84}], "team1_avg_kd": 0.94, "team2_avg_kd": 0.96}, "recent_performance": {"team1_recent_matches": [{"score": "0:1", "result": "L", "opponent": "LIVE\n        \n\n\n\n\n\n\n\nKRU\n\n\n\n\n\n\n\nreversive\n\n<PERSON>\n\n\n\n\ndeco\n\n<PERSON>\n\n\n\n\nlaser\n\n<PERSON>\n\n\n\n\nataraXia\n\nJoaquin <PERSON>i\n\n\n\n\nrighi\n\nSebastian <PERSON><PERSON>i\n\n\n\n\n\n                    Create Prediction\n                \n\n                Betting Tips", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Keyd Stars\nKeyd\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Keyd Stars\nKeyd\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "SWS Gaming\nSWS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Keyd Stars\nKeyd\n\n\n\n\n\n\n\n\n\n\n                    All Keyd Encounters\n                \n\n\n\n\nKRU Esport\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:0", "result": "W", "opponent": "3 \n\n2nd Map\n\n\n\n\n\n\n\nleo_drk\n\n<PERSON>h\n\nEmerson  Henrique\n\n\n\n\nninjaZ\n\nIcaro Bastos Cavalari\n\n\n\n\nvinaabEAST\n\nVinicius Santos\n\n\n\n\nflash\n\n<PERSON>d", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "01\n\n\n\n\n\n\nRiver Plate Gaming\nRPG", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "00\n\n\n\n\n\n\nBoca Juniors\nBoca Jr", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "10\n\n\n\n\n\n\nSharks Esports\nSharks", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "52\n\n\n\n\n\n\nKeyd Stars\nKeyd", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 7, "losses": 14}, "team2_recent_form": {"wins": 15, "losses": 8}, "team1_opponents": ["LIV", "Key", "Key", "SWS", "Key", "KRU", "KRU", "Gam", "ODD", "LIV", "LIV", "ODD", "Key", "Key", "Key", "SWS", "Key", "KRU", "KRU", "Gam", "ODD"], "team2_opponents": ["leo", "Key", "Riv", "Bo<PERSON>", "<PERSON><PERSON>", "Key", "Fla", "Shi", "KRU", "KRU"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"desh": {"trend": "STABLE_HIGH", "confidence": 75, "recent_matches": 5, "performance_rating": 132.0}, "vinaabEAST": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "leo_drk": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "ninjaZ": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 78.0}, "flash": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 67.0}}, "team2_form_trends": {"righi": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 106.0}, "ataraXia": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "deco": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "laser": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "reversive": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "flash", "player1_kd": 0.67, "player2": "reversive", "player2_kd": 0.84, "impact": "MEDIUM", "description": "Tactical battle: flash vs reversive"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"desh": {"team": "Keyd Stars", "kd_ratio": 1.32, "impact_level": "MEDIUM", "recent_form": "STABLE_HIGH"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "KRU Esport KRU CCT SA 24 06 25 to win match", "confidence": 85, "reasoning": ["Team strength difference: 32.2", "Primary betting market"]}, "FIRST_MAP_WINNER": {"prediction": "KRU Esport KRU CCT SA 24 06 25 first map", "confidence": 63.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "desh most kills vs righi", "confidence": 80, "reasoning": ["K/D comparison: 1.32 vs 1.06"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387732-keyd-stars-keyd-vs-kru-esport-kru-cct-sa-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:50:33.700002"}}, "page_content": ""}, {"team1": {"name": "HEROIC Academy", "ranking": 180, "ensi_score": 1411, "winrate_10": 60.0, "winrate_30": 46.0, "current_shape": 114.0, "avg_kd": 0.87, "players": [{"name": "fnl", "nationality": "", "kd_ratio": 0.99}, {"name": "Scr0b", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "anber", "nationality": "Denmark", "kd_ratio": 0.89}, {"name": "St0m4k", "nationality": "", "kd_ratio": 0.8}, {"name": "Dengzoe", "nationality": "Denmark", "kd_ratio": 0.71}]}, "team2": {"name": "Genone Gone", "ranking": 95, "ensi_score": 1514, "winrate_10": 70.0, "winrate_30": 63.0, "current_shape": 107.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Genone Gone", "confidence": 58.68841666666667, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - <PERSON><PERSON> most kills vs fnl (77.0% confidence) | Alternative: MAP_HANDICAP (74.20772222222223%)", "key_factors": ["📈 Better recent form: HEROIC Academy (15W-3L vs 4W-13L)", "🏆 Ranking advantage: <PERSON><PERSON> (#180 vs #95)", "📈 ENSI advantage: Genone Gone (1411 vs 1514)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 European Pro League Season 28: Division 2", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "fnl", "nationality": "", "kd_ratio": 0.99}, {"name": "Scr0b", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "anber", "nationality": "Denmark", "kd_ratio": 0.89}, {"name": "St0m4k", "nationality": "", "kd_ratio": 0.8}, {"name": "Dengzoe", "nationality": "Denmark", "kd_ratio": 0.71}], "team2_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}], "team1_avg_kd": 0.87, "team2_avg_kd": 1.01}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "HEROIC Academy\nHero", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "KS Esports\nKS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "HEROIC Academy\nHero", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "HEROIC Academy\nHero", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "55\n\n\n\n\n\n\nESC Gaming\nESC", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "A", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "A", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "03\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "29\n\n\n\n\n\n\nFisher College\nFC", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 15, "losses": 3}, "team2_recent_form": {"wins": 4, "losses": 13}, "team1_opponents": ["HER", "HER", "HER", "<PERSON><PERSON>", "Gen", "ENE", "Vol", "Vol", "<PERSON><PERSON>", "Gen", "ENE", "Vol"], "team2_opponents": ["ESC", "Nex", "Fis", "Gen", "<PERSON><PERSON>", "Gen", "Gen"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 20, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Kubix Esports", "kubix", "Volt", "XI Esport", "ESC Gaming", "ESC", "HEROIC Academy", "Hero.A", "KS Esports", "Nexus Gaming", "Nexus", "Fisher College", "GenOne", "GOne", "Rebels Gaming", "REBELS", "Kronjyllands esports", "<PERSON><PERSON><PERSON><PERSON>", "ENERGYULTRA", "ENY"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"fnl": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "Scr0b": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "anber": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}, "St0m4k": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}, "Dengzoe": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 71.0}}, "team2_form_trends": {"Chucky": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "Brooxsy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Tarkky": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "cHeuuuuk": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "SLIE9000": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Dengzoe", "player1_kd": 0.71, "player2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "player2_kd": 0.87, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Genone Gone to win match", "confidence": 58.68841666666667, "reasoning": ["Team strength difference: 5.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "HEROIC Academy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 74.20772222222223, "reasoning": ["Based on team strength difference: 5.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 72.20772222222223, "reasoning": ["Team strength analysis: 5.8 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "<PERSON><PERSON> most kills vs fnl", "confidence": 77.0, "reasoning": ["K/D comparison: 0.99 vs 1.16"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375304-heroic-academy-heroa-vs-genone-gone-epl-s28-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:50:54.606211"}}, "page_content": ""}, {"team1": {"name": "Bestia Bestia", "ranking": 46, "ensi_score": 1676, "winrate_10": 70.0, "winrate_30": 57.0, "current_shape": 113.0, "avg_kd": 1.14, "players": [{"name": "cass1n", "nationality": "", "kd_ratio": 1.45}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.18}, {"name": "Noktse", "nationality": "", "kd_ratio": 1.05}, {"name": "luchov", "nationality": "", "kd_ratio": 1.04}, {"name": "timo", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "Shinden Shinden CCT SA 24 06 25", "ranking": 126, "ensi_score": 1455, "winrate_10": 40.0, "winrate_30": 47.0, "current_shape": 93.0, "avg_kd": 0.97, "players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.21}, {"name": "abizz", "nationality": "", "kd_ratio": 1.02}, {"name": "nacho", "nationality": "", "kd_ratio": 0.89}, {"name": "roy", "nationality": "", "kd_ratio": 0.87}, {"name": "BK1", "nationality": "", "kd_ratio": 0.86}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Bestia Bestia", "confidence": 88.62966666666667, "betting_advice": "🟢 BEST BET: MATCH_WINNER - Bestia Bestia to win match (85% confidence) | Alternative: MAP_HANDICAP (80%)", "key_factors": ["🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: <PERSON><PERSON> Bestia (#46 vs #126)", "📈 ENSI advantage: Bestia Bestia (1676 vs 1455)", "⚡ Better current shape: Bestia Bestia (113.0% vs 93.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 FiReLEAGUE Buenos Aires", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.45, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "cass1n", "nationality": "", "kd_ratio": 1.45}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.18}, {"name": "Noktse", "nationality": "", "kd_ratio": 1.05}, {"name": "luchov", "nationality": "", "kd_ratio": 1.04}, {"name": "timo", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.21}, {"name": "abizz", "nationality": "", "kd_ratio": 1.02}, {"name": "nacho", "nationality": "", "kd_ratio": 0.89}, {"name": "roy", "nationality": "", "kd_ratio": 0.87}, {"name": "BK1", "nationality": "", "kd_ratio": 0.86}], "team1_avg_kd": 1.14, "team2_avg_kd": 0.97}, "recent_performance": {"team1_recent_matches": [{"score": "0:2", "result": "L", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Legacy\nLegacy\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "BESTIA\nBESTIA\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Red Canids\nRC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "BESTIA\nBESTIA\n\n\n\n\n\n\n\n\n\n\n                    All BESTIA Encounters\n                \n\n\n\n\nShindeN\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:0", "result": "W", "opponent": "10\n\n\n\n\n\n\nBESTIA\nBESTIA", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "47\n\n\n\n\n\n\nBESTIA\nBESTIA", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "00\n\n\n\n\n\n\nSharks Esports\nSharks", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "13\n\n\n\n\n\n\nBESTIA\nBESTIA", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "00\n\n\n\n\n\n\nFluxo\nFluxo", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 8}, "team2_recent_form": {"wins": 8, "losses": 10}, "team1_opponents": ["Leg", "BES", "Red", "BES", "Ele", "<PERSON><PERSON>", "KRU", "Yaw", "Yaw", "BES", "Leg", "BES", "Red", "BES", "Ele", "<PERSON><PERSON>", "KRU", "Yaw"], "team2_opponents": ["BES", "BES", "<PERSON><PERSON>", "Flu", "Shi", "Shi", "Shi", "Shi"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"cass1n": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 145.0}, "tomaszin": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 118.0}, "Noktse": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 105.0}, "luchov": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "timo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"ivanzinho": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "abizz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "nacho": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}, "roy": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "BK1": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 86.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "cass1n", "player1_kd": 1.45, "player2": "<PERSON><PERSON><PERSON><PERSON>", "player2_kd": 1.21, "impact": "VERY_HIGH", "description": "Battle of star players: cass1n vs <PERSON><PERSON><PERSON><PERSON>"}, {"type": "IGL_BATTLE", "player1": "timo", "player1_kd": 1.0, "player2": "BK1", "player2_kd": 0.86, "impact": "MEDIUM", "description": "Tactical battle: timo vs BK1"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"cass1n": {"team": "Bestia Bestia", "kd_ratio": 1.45, "impact_level": "HIGH", "recent_form": "IMPROVING"}, "ivanzinho": {"team": "Shinden Shinden CCT SA 24 06 25", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Bestia Bestia to win match", "confidence": 85, "reasoning": ["Team strength difference: 25.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Bestia Bestia -1.5 maps (2-0 win)", "confidence": 80, "reasoning": ["Based on team strength difference: 25.8"]}, "TOTAL_MAPS": {"prediction": "UNDER 2.5 maps (2-0 likely)", "confidence": 70.75311111111111, "reasoning": ["Team strength analysis: 25.8 difference"]}, "CORRECT_SCORE": {"prediction": "Bestia Bestia 2-0", "confidence": 75, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Bestia Bestia first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "cass1n most kills vs i<PERSON><PERSON><PERSON>", "confidence": 80, "reasoning": ["K/D comparison: 1.45 vs 1.21"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387733-bestia-bestia-vs-shinden-shinden-cct-sa-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:51:15.649557"}}, "page_content": ""}, {"team1": {"name": "9z Team", "ranking": 54, "ensi_score": 1646, "winrate_10": 60.0, "winrate_30": 50.0, "current_shape": 110.0, "avg_kd": 1.02, "players": [{"name": "MartinezSa", "nationality": "", "kd_ratio": 1.19}, {"name": "adamS-", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.1}, {"name": "HUASOPEEK", "nationality": "", "kd_ratio": 0.86}, {"name": "max", "nationality": "", "kd_ratio": 0.83}]}, "team2": {"name": "Dusty Roots DR CCT SA 24 06 25", "ranking": 167, "ensi_score": 1420, "winrate_10": 40.0, "winrate_30": 47.0, "current_shape": 93.0, "avg_kd": 0.86, "players": [{"name": "maxxkor", "nationality": "", "kd_ratio": 1.01}, {"name": "tom1jed", "nationality": "", "kd_ratio": 0.9}, {"name": "FraGuTy", "nationality": "", "kd_ratio": 0.88}, {"name": "toto-", "nationality": "", "kd_ratio": 0.84}, {"name": "1962", "nationality": "", "kd_ratio": 0.66}]}, "h2h_record": "9z: 1 - Draws: 0 - DR: 0 (100% vs 0%)", "prediction": "9z Team", "confidence": 92.35091666666665, "betting_advice": "🟢 BEST BET: MATCH_WINNER - 9z Team to win match (81.85091666666665% confidence) | Alternative: MAP_HANDICAP (80%)", "key_factors": ["🆚 H2H record: 9z Team leads (1-0) 🎯 (Similar rosters - high relevance)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: 9z Team (#54 vs #167)", "📈 ENSI advantage: 9z Team (1646 vs 1420)", "⚡ Better current shape: 9z Team (110.0% vs 93.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "9z: 1 - Draws: 0 - DR: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": <PERSON> 1:2 DR"], "team1_name": "9z", "team2_name": "DR", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 FiReLEAGUE Buenos Aires", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.19, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "MartinezSa", "nationality": "", "kd_ratio": 1.19}, {"name": "adamS-", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.1}, {"name": "HUASOPEEK", "nationality": "", "kd_ratio": 0.86}, {"name": "max", "nationality": "", "kd_ratio": 0.83}], "team2_players": [{"name": "maxxkor", "nationality": "", "kd_ratio": 1.01}, {"name": "tom1jed", "nationality": "", "kd_ratio": 0.9}, {"name": "FraGuTy", "nationality": "", "kd_ratio": 0.88}, {"name": "toto-", "nationality": "", "kd_ratio": 0.84}, {"name": "1962", "nationality": "", "kd_ratio": 0.66}], "team1_avg_kd": 1.02, "team2_avg_kd": 0.86}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n            All 9z and DR Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "KRU Esport\nKRU\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "9z Team\n9z\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "02\n\n\n\n\n\n\nDusty Roots\nDR", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "31\n\n\n\n\n\n\nImperial Esports\nImperial", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "10\n\n\n\n\n\n\nBESTIA\nBESTIA", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "01\n\n\n\n\n\n\n9z Team\n9z", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "44\n\n\n\n\n\n\n9z Team\n9z", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 13, "losses": 10}, "team2_recent_form": {"wins": 6, "losses": 10}, "team1_opponents": ["KRU", "<PERSON><PERSON>", "Yaw", "<PERSON><PERSON>", "LaC", "Ele", "Ele", "<PERSON><PERSON>", "KRU", "<PERSON><PERSON>", "Yaw", "<PERSON><PERSON>", "LaC", "Ele"], "team2_opponents": ["<PERSON><PERSON>", "Imp", "BES", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"MartinezSa": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 119.0}, "adamS-": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "Luken": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 110.00000000000001}, "HUASOPEEK": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 86.0}, "max": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 83.0}}, "team2_form_trends": {"maxxkor": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "tom1jed": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "FraGuTy": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "toto-": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}, "1962": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 66.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "max", "player1_kd": 0.83, "player2": "1962", "player2_kd": 0.66, "impact": "MEDIUM", "description": "Tactical battle: max vs 1962"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "9z Team to win match", "confidence": 81.85091666666665, "reasoning": ["Team strength difference: 21.2", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "9z Team -1.5 maps (2-0 win)", "confidence": 80, "reasoning": ["Based on team strength difference: 21.2"]}, "TOTAL_MAPS": {"prediction": "UNDER 2.5 maps (2-0 likely)", "confidence": 66.23394444444443, "reasoning": ["Team strength analysis: 21.2 difference"]}, "CORRECT_SCORE": {"prediction": "9z Team 2-0", "confidence": 75, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "9z Team first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "MartinezSa most kills vs maxxkor", "confidence": 78.0, "reasoning": ["K/D comparison: 1.19 vs 1.01"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387731-9z-team-9z-vs-dusty-roots-dr-cct-sa-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:51:37.686488"}}, "page_content": ""}, {"team1": {"name": "XI Esport", "ranking": 325, "ensi_score": 1287, "winrate_10": 40.0, "winrate_30": 27.0, "current_shape": 113.0, "avg_kd": 0.87, "players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "Few", "nationality": "Denmark", "kd_ratio": 0.79}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.64}, {"name": "Sin<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}]}, "team2": {"name": "Prestige Prestige", "ranking": 153, "ensi_score": 1428, "winrate_10": 60.0, "winrate_30": 58.0, "current_shape": 102.0, "avg_kd": 0.9, "players": [{"name": "Mol011", "nationality": "Denmark", "kd_ratio": 1.04}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "NickyB", "nationality": "Denmark", "kd_ratio": 0.48}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "GA1De", "nationality": "Denmark", "kd_ratio": 1.0}]}, "h2h_record": "XI: 0 - Draws: 0 - Prestige: 1 (0% vs 100%)", "prediction": "Prestige Prestige", "confidence": 68.54808333333334, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (74.63461111111111% confidence) | Alternative: TOTAL_MAPS (72.63461111111111%)", "key_factors": ["🆚 H2H record: Prestige Prestige leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: XI Esport (13W-5L vs 6W-10L)", "🏆 Ranking advantage: Prestige Prestige (#325 vs #153)", "📈 ENSI advantage: Prestige Prestige (1287 vs 1428)", "⚡ Better current shape: XI Esport (113.0% vs 102.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "XI: 0 - Draws: 0 - Prestige: 1 (0% vs 100%)", "team1_wins": 0, "team2_wins": 1, "draws": 0, "recent_matches": [": Prestige 2:0 Prestige"], "team1_name": "XI", "team2_name": "Prestige", "team1_win_percentage": 0, "team2_win_percentage": 100, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "Few", "nationality": "Denmark", "kd_ratio": 0.79}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.64}, {"name": "Sin<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}], "team2_players": [{"name": "Mol011", "nationality": "Denmark", "kd_ratio": 1.04}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "NickyB", "nationality": "Denmark", "kd_ratio": 0.48}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "GA1De", "nationality": "Denmark", "kd_ratio": 1.0}], "team1_avg_kd": 0.87, "team2_avg_kd": 0.9}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n            All XI and Prestige Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "55\n\n\n\n\n\n\nPrestige\nPrestige", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "03\n\n\n\n\n\n\nXI Esport\nXI", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "57\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "33\n\n\n\n\n\n\nXI Esport\nXI", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "A", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 13, "losses": 5}, "team2_recent_form": {"wins": 6, "losses": 10}, "team1_opponents": ["Vol", "Pre", "Pre", "Vol", "Pre", "Pre"], "team2_opponents": ["Ast", "Pre", "Gen", "Pre", "ESC", "Bru"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 13, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Prestige", "XI Esport", "Ex-<PERSON><PERSON><PERSON>", "Ex-<PERSON><PERSON>", "Volt", "Bru<PERSON>", "GenOne", "GOne", "HEROIC Academy", "Hero.A", "KS Esports", "ESC Gaming", "ESC"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Skejs": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Stesso": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "Few": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}, "Kragh": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 64.0}, "Sinzey": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"Mol011": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "Mizi": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "NickyB": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 48.0}, "Folke": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "GA1De": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON>", "player1_kd": 0.64, "player2": "NickyB", "player2_kd": 0.48, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs <PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Prestige Prestige to win match", "confidence": 58.04808333333334, "reasoning": ["Team strength difference: 5.4", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 74.63461111111111, "reasoning": ["Based on team strength difference: 5.4"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 72.63461111111111, "reasoning": ["Team strength analysis: 5.4 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON><PERSON> vs Mol011", "confidence": 55, "reasoning": ["K/D comparison: 1.00 vs 1.04"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375305-xi-esport-xi-vs-prestige-prestige-epl-s28-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:51:59.568786"}}, "page_content": ""}, {"team1": {"name": "<PERSON><PERSON><PERSON>", "ranking": 110, "ensi_score": 1537, "winrate_10": 70.0, "winrate_30": 70.0, "current_shape": 100.0, "avg_kd": 1.07, "players": [{"name": "gbb", "nationality": "", "kd_ratio": 1.19}, {"name": "DeStiNy", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.11}, {"name": "slashzz", "nationality": "", "kd_ratio": 0.99}, {"name": "nython", "nationality": "", "kd_ratio": 0.94}]}, "team2": {"name": "2game 2game CCT SA 23 06 25", "ranking": 3, "ensi_score": 1480, "winrate_10": 60.0, "winrate_30": 48.0, "current_shape": 112.0, "avg_kd": 1.03, "players": [{"name": "<PERSON><PERSON>o", "nationality": "", "kd_ratio": 1.15}, {"name": "detr0ittJ", "nationality": "", "kd_ratio": 1.15}, {"name": "lukiz", "nationality": "", "kd_ratio": 1.11}, {"name": "xureba", "nationality": "", "kd_ratio": 0.96}, {"name": "PKL", "nationality": "", "kd_ratio": 0.8}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "2game 2game CCT SA 23 06 25", "confidence": 91.2675, "betting_advice": "🟢 BEST BET: MATCH_WINNER - 2game 2game CCT SA 23 06 25 to win match (85% confidence) | Alternative: MAP_HANDICAP (80%)", "key_factors": ["📈 Better recent form: <PERSON><PERSON><PERSON> (15W-4L vs 4W-12L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: 2game 2game CCT SA 23 06 25 (#110 vs #3)", "📈 ENSI advantage: <PERSON><PERSON><PERSON> (1537 vs 1480)", "⚡ Better current shape: 2game 2game CCT SA 23 06 25 (100.0% vs 112.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2023 CCT South America Series #12", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 7}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.19, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "esley hard<PERSON>o <PERSON>\"", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "gbb", "nationality": "", "kd_ratio": 1.19}, {"name": "DeStiNy", "nationality": "", "kd_ratio": 1.11}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.11}, {"name": "slashzz", "nationality": "", "kd_ratio": 0.99}, {"name": "nython", "nationality": "", "kd_ratio": 0.94}], "team2_players": [{"name": "<PERSON><PERSON>o", "nationality": "", "kd_ratio": 1.15}, {"name": "detr0ittJ", "nationality": "", "kd_ratio": 1.15}, {"name": "lukiz", "nationality": "", "kd_ratio": 1.11}, {"name": "xureba", "nationality": "", "kd_ratio": 0.96}, {"name": "PKL", "nationality": "", "kd_ratio": 0.8}], "team1_avg_kd": 1.07, "team2_avg_kd": 1.03}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "BESTIA\nBESTIA\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "<PERSON><PERSON><PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Team Solid\nSolid\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Sharks Esports\nSharks\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "<PERSON><PERSON><PERSON>l<PERSON>\n\n\n\n\n\n\n\n\n\n\n                    All Selva Encounters\n                \n\n\n\n\n2GAME\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "48\n\n\n\n\n\n\n<PERSON><PERSON><PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "19\n\n\n\n\n\n\npaiN Gaming\npaiN", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "26\n\n\n\n\n\n\n<PERSON><PERSON><PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "53\n\n\n\n\n\n\n<PERSON><PERSON><PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "34\n\n\n\n\n\n\nO PLANO\nOP", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 15, "losses": 4}, "team2_recent_form": {"wins": 4, "losses": 12}, "team1_opponents": ["BES", "<PERSON>l", "Tea", "<PERSON><PERSON>", "<PERSON>l", "<PERSON><PERSON>", "Gam", "MIB", "MIB", "<PERSON>l", "BES", "<PERSON>l", "Tea", "<PERSON><PERSON>", "<PERSON><PERSON>", "Gam", "MIB"], "team2_opponents": ["<PERSON>l", "pai", "<PERSON>l", "O P", "Red"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"gbb": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 119.0}, "DeStiNy": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "Tomate": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "slashzz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "nython": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}}, "team2_form_trends": {"hardzao": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "detr0ittJ": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "lukiz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "xureba": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "PKL": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "nython", "player1_kd": 0.94, "player2": "PKL", "player2_kd": 0.8, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON>hon vs PKL"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "2game 2game CCT SA 23 06 25 to win match", "confidence": 85, "reasoning": ["Team strength difference: 27.5", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "2game 2game CCT SA 23 06 25 -1.5 maps (2-0 win)", "confidence": 80, "reasoning": ["Based on team strength difference: 27.5"]}, "TOTAL_MAPS": {"prediction": "UNDER 2.5 maps (2-0 likely)", "confidence": 72.51166666666666, "reasoning": ["Team strength analysis: 27.5 difference"]}, "CORRECT_SCORE": {"prediction": "2game 2game CCT SA 23 06 25 2-0", "confidence": 75, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: gbb vs hardzao", "confidence": 55, "reasoning": ["K/D comparison: 1.19 vs 1.15"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387728-selva-selva-vs-2game-2game-cct-sa-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:52:20.811140"}}, "page_content": ""}, {"team1": {"name": "Sharks Esports", "ranking": 43, "ensi_score": 1701, "winrate_10": 80.0, "winrate_30": 60.0, "current_shape": 120.0, "avg_kd": 1.16, "players": [{"name": "doc", "nationality": "", "kd_ratio": 1.19}, {"name": "koala", "nationality": "", "kd_ratio": 1.18}, {"name": "rdnzao-", "nationality": "", "kd_ratio": 1.18}, {"name": "a<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "G<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.08}]}, "team2": {"name": "LP LP CCT SA 24 06 25", "ranking": 170, "ensi_score": 1418, "winrate_10": 60.0, "winrate_30": 60.0, "current_shape": 100.0, "avg_kd": 1.08, "players": [{"name": "happ", "nationality": "", "kd_ratio": 1.22}, {"name": "realz1n", "nationality": "", "kd_ratio": 1.14}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "bsd", "nationality": "", "kd_ratio": 1.05}, {"name": "zmb", "nationality": "", "kd_ratio": 0.91}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Sharks Esports", "confidence": 89.63323333333332, "betting_advice": "🟢 BEST BET: MATCH_WINNER - Sharks Esports to win match (85% confidence) | Alternative: FIRST_MAP_WINNER (69.0%)", "key_factors": ["📈 Better recent form: LP LP CCT SA 24 06 25 (14W-6L vs 5W-11L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: Sharks Esports (#43 vs #170)", "📈 ENSI advantage: Sharks Esports (1701 vs 1418)", "⚡ Better current shape: Sharks Esports (120.0% vs 100.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO1", "tier": "Tier-3", "tournament": "2025 Odyssey Cup", "h2h_history": [{"score": "0:3", "context": "recent_match"}, {"score": "0:3", "context": "recent_match"}, {"score": "0:3", "context": "recent_match"}, {"score": "0:3", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 6}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.19, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "doc", "nationality": "", "kd_ratio": 1.19}, {"name": "koala", "nationality": "", "kd_ratio": 1.18}, {"name": "rdnzao-", "nationality": "", "kd_ratio": 1.18}, {"name": "a<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "G<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.08}], "team2_players": [{"name": "happ", "nationality": "", "kd_ratio": 1.22}, {"name": "realz1n", "nationality": "", "kd_ratio": 1.14}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "bsd", "nationality": "", "kd_ratio": 1.05}, {"name": "zmb", "nationality": "", "kd_ratio": 0.91}], "team1_avg_kd": 1.16, "team2_avg_kd": 1.08}, "recent_performance": {"team1_recent_matches": [{"score": "0:3", "result": "L", "opponent": "Sharks Esports\nSharks\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Sharks Esports\nSharks\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "W7M Esports\nW7M\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "ODDIK Academy\nODDIK", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "LP\nLP\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "3:0", "result": "W", "opponent": "34\n\n\n\n\n\n\nRed Canids\nRC", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "15\n\n\n\n\n\n\nW7M Esports\nW7M", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "02\n\n\n\n\n\n\nAmérica\nAme", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "19\n\n\n\n\n\n\nSharks Esports\nSharks", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "52\n\n\n\n\n\n\nSharks Esports\nSharks", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 5, "losses": 11}, "team2_recent_form": {"wins": 14, "losses": 6}, "team1_opponents": ["<PERSON><PERSON>", "<PERSON><PERSON>", "W7M", "ODD", "KRU", "KRU", "<PERSON><PERSON>", "<PERSON><PERSON>", "W7M", "KRU"], "team2_opponents": ["Red", "W7M", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pla", "JER", "Shi", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"doc": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 119.0}, "koala": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 118.0}, "rdnzao-": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 118.0}, "aNgelo": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "Gafolo": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}}, "team2_form_trends": {"happ": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 122.0}, "realz1n": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 113.99999999999999}, "Leomonster": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "bsd": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 105.0}, "zmb": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 91.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "G<PERSON><PERSON>", "player1_kd": 1.08, "player2": "zmb", "player2_kd": 0.91, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs zmb"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"happ": {"team": "LP LP CCT SA 24 06 25", "kd_ratio": 1.22, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Sharks Esports to win match", "confidence": 85, "reasoning": ["Team strength difference: 26.4", "Primary betting market"]}, "FIRST_MAP_WINNER": {"prediction": "Sharks Esports first map", "confidence": 69.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: doc vs happ", "confidence": 55, "reasoning": ["K/D comparison: 1.19 vs 1.22"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387727-sharks-esports-sharks-vs-lp-lp-cct-sa-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:52:42.679314"}}, "page_content": ""}, {"team1": {"name": "FURIA Esports Female Furiafe", "ranking": 35, "ensi_score": 1732, "winrate_10": 80.0, "winrate_30": 80.0, "current_shape": 100.0, "avg_kd": 1.28, "players": [{"name": "kaahSENSEI", "nationality": "", "kd_ratio": 1.43}, {"name": "luli<PERSON>z", "nationality": "", "kd_ratio": 1.42}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.27}, {"name": "gabs", "nationality": "", "kd_ratio": 1.16}, {"name": "izaa", "nationality": "", "kd_ratio": 1.13}]}, "team2": {"name": "Flamengo Esports Flamengo CCT SA 24 06 25", "ranking": 97, "ensi_score": 1513, "winrate_10": 60.0, "winrate_30": 60.0, "current_shape": 100.0, "avg_kd": 1.08, "players": [{"name": "Misfit", "nationality": "", "kd_ratio": 1.28}, {"name": "vsm", "nationality": "", "kd_ratio": 1.23}, {"name": "delboNi", "nationality": "", "kd_ratio": 1.12}, {"name": "Danoco", "nationality": "", "kd_ratio": 0.99}, {"name": "CutzMeretz", "nationality": "", "kd_ratio": 0.79}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "FURIA Esports Female Furiafe", "confidence": 89.2972, "betting_advice": "🟢 BEST BET: MATCH_WINNER - FURIA Esports Female Furiafe to win match (85% confidence) | Alternative: PLAYER_PROPS (74.99999999999999%)", "key_factors": ["📈 Better recent form: FURIA Esports Female Furiafe (15W-5L vs 3W-8L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: FURIA Esports Female Furiafe (#35 vs #97)", "📈 ENSI advantage: FURIA Esports Female Furiafe (1732 vs 1513)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO1", "tier": "Tier-3", "tournament": "2025 Esl Impact League Season 7", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 5}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.43, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "Unknown Tournament", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "kaahSENSEI", "nationality": "", "kd_ratio": 1.43}, {"name": "luli<PERSON>z", "nationality": "", "kd_ratio": 1.42}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.27}, {"name": "gabs", "nationality": "", "kd_ratio": 1.16}, {"name": "izaa", "nationality": "", "kd_ratio": 1.13}], "team2_players": [{"name": "Misfit", "nationality": "", "kd_ratio": 1.28}, {"name": "vsm", "nationality": "", "kd_ratio": 1.23}, {"name": "delboNi", "nationality": "", "kd_ratio": 1.12}, {"name": "Danoco", "nationality": "", "kd_ratio": 0.99}, {"name": "CutzMeretz", "nationality": "", "kd_ratio": 0.79}], "team1_avg_kd": 1.28, "team2_avg_kd": 1.08}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Supernova Comets\nSComets\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Imperial Valkyries\nImp VK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "Supernova Comets\nSComets\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "DMS\nDMS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "MIBR Female\nMIBR", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "fe", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "fe", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "fe", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "38\n\n\n\n\n\n\nFlamengo Esports\nFlamengo", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "08\n\n\n\n\n\n\nFlamengo Esports\nFlamengo", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 15, "losses": 5}, "team2_recent_form": {"wins": 3, "losses": 8}, "team1_opponents": ["<PERSON><PERSON>", "Imp", "<PERSON><PERSON>", "DMS", "MIB", "Yaw", "KRU", "Ele", "LaC", "W7M", "W7M", "<PERSON><PERSON>", "Imp", "<PERSON><PERSON>", "DMS", "Yaw", "KRU", "Ele", "LaC", "W7M"], "team2_opponents": ["Fla", "Fla", "Fla"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"kaahSENSEI": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 143.0}, "lulitenz": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 142.0}, "bizinha": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 127.0}, "gabs": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "izaa": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}}, "team2_form_trends": {"Misfit": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 128.0}, "vsm": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 123.0}, "delboNi": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "Danoco": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "CutzMeretz": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "kaahSENSEI", "player1_kd": 1.43, "player2": "Misfit", "player2_kd": 1.28, "impact": "VERY_HIGH", "description": "Battle of star players: kaahSENSEI vs Misfit"}, {"type": "IGL_BATTLE", "player1": "izaa", "player1_kd": 1.13, "player2": "CutzMeretz", "player2_kd": 0.79, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs CutzMeretz"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"kaahSENSEI": {"team": "FURIA Esports Female Furiafe", "kd_ratio": 1.43, "impact_level": "HIGH", "recent_form": "IMPROVING"}, "lulitenz": {"team": "FURIA Esports Female Furiafe", "kd_ratio": 1.42, "impact_level": "HIGH", "recent_form": "IMPROVING"}, "bizinha": {"team": "FURIA Esports Female Furiafe", "kd_ratio": 1.27, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "Misfit": {"team": "Flamengo Esports Flamengo CCT SA 24 06 25", "kd_ratio": 1.28, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "vsm": {"team": "Flamengo Esports Flamengo CCT SA 24 06 25", "kd_ratio": 1.23, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "FURIA Esports Female Furiafe to win match", "confidence": 85, "reasoning": ["Team strength difference: 26.2", "Primary betting market"]}, "FIRST_MAP_WINNER": {"prediction": "FURIA Esports Female Furiafe first map", "confidence": 65.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "kaahSENSEI most kills vs Misfit", "confidence": 74.99999999999999, "reasoning": ["K/D comparison: 1.43 vs 1.28"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387734-furia-esports-female-furiafe-vs-flamengo-esports-flamengo-cct-sa-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:53:04.332895"}}, "page_content": ""}, {"team1": {"name": "<PERSON><PERSON>", "ranking": 47, "ensi_score": 1674, "winrate_10": 50.0, "winrate_30": 60.0, "current_shape": 90.0, "avg_kd": 1.03, "players": [{"name": "WOOD7", "nationality": "", "kd_ratio": 1.15}, {"name": "matios", "nationality": "", "kd_ratio": 1.02}, {"name": "naitte", "nationality": "", "kd_ratio": 1.01}, {"name": "pancc", "nationality": "", "kd_ratio": 1.0}, {"name": "ksloks", "nationality": "", "kd_ratio": 0.97}]}, "team2": {"name": "Bounty Hunters BH CCT SA 23 06 25", "ranking": 116, "ensi_score": 1474, "winrate_10": 60.0, "winrate_30": 40.0, "current_shape": 120.0, "avg_kd": 0.98, "players": [{"name": "meyern", "nationality": "", "kd_ratio": 1.16}, {"name": "KAISER", "nationality": "", "kd_ratio": 1.06}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.91}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.89}, {"name": "zock", "nationality": "", "kd_ratio": 0.89}]}, "h2h_record": "ODDIK: 4 - Draws: 0 - BH: 0 (100% vs 0%)", "prediction": "<PERSON><PERSON>", "confidence": 90.06299999999999, "betting_advice": "🟢 BEST BET: MATCH_WINNER - <PERSON><PERSON> to win match (79.56299999999999% confidence) | Alternative: FIRST_MAP_WINNER (67.5%)", "key_factors": ["🆚 H2H record: <PERSON><PERSON> leads (4-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: Bounty Hunters BH CCT SA 23 06 25 (16W-10L vs 10W-15L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: <PERSON><PERSON> (#47 vs #116)", "📈 ENSI advantage: <PERSON><PERSON> (1674 vs 1474)", "⚡ Better current shape: Bounty Hunters BH CCT SA 23 06 25 (90.0% vs 120.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 4, "h2h_record": "ODDIK: 4 - Draws: 0 - BH: 0 (100% vs 0%)", "team1_wins": 4, "team2_wins": 0, "draws": 0, "recent_matches": [": Bounty Hunters 0:1 BH", ": ODDIK 1:0 ODDIK", ": Bounty Hunters 1:2 BH", ": ODDIK 2:1 ODDIK"], "team1_name": "ODDIK", "team2_name": "BH", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 4, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO1", "tier": "Tier-3", "tournament": "2024 ESL Challenger League Season 49: South America", "h2h_history": [{"score": "0:1", "context": "recent_match"}, {"score": "0:1", "context": "recent_match"}, {"score": "0:1", "context": "recent_match"}, {"score": "0:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.15, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "Unknown Tournament", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "WOOD7", "nationality": "", "kd_ratio": 1.15}, {"name": "matios", "nationality": "", "kd_ratio": 1.02}, {"name": "naitte", "nationality": "", "kd_ratio": 1.01}, {"name": "pancc", "nationality": "", "kd_ratio": 1.0}, {"name": "ksloks", "nationality": "", "kd_ratio": 0.97}], "team2_players": [{"name": "meyern", "nationality": "", "kd_ratio": 1.16}, {"name": "KAISER", "nationality": "", "kd_ratio": 1.06}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.91}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.89}, {"name": "zock", "nationality": "", "kd_ratio": 0.89}], "team1_avg_kd": 1.03, "team2_avg_kd": 0.98}, "recent_performance": {"team1_recent_matches": [{"score": "0:1", "result": "L", "opponent": "ODDIK\nODDIK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "Bounty Hunters\nBH\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "ODDIK\nODDIK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Bounty Hunters\nBH\n\n\n\n\n\n\n\n\n\n\n\n            All ODDIK and BH Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Aurora Gaming\nAurora\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:0", "result": "W", "opponent": "43\n\n\n\n\n\n\nBounty Hunters\nBH", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "38\n\n\n\n\n\n\nODDIK\nODDIK", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "53\n\n\n\n\n\n\nBounty Hunters\nBH", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "47\n\n\n\n\n\n\nODDIK\nODDIK", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "51\n\n\n\n\n\n\nODDIK\nODDIK", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 10, "losses": 15}, "team2_recent_form": {"wins": 16, "losses": 10}, "team1_opponents": ["ODD", "<PERSON><PERSON>", "ODD", "<PERSON><PERSON>", "<PERSON><PERSON>", "ODD", "ODD", "ODD", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ODD", "<PERSON><PERSON>", "ODD", "<PERSON><PERSON>", "ODD", "<PERSON><PERSON>", "ODD", "ODD", "ODD", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "team2_opponents": ["<PERSON><PERSON>", "ODD", "<PERSON><PERSON>", "ODD", "ODD", "FUR", "pai", "Ast", "Shi", "JER", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"WOOD7": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "matios": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "naitte": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "pancc": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "ksloks": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}}, "team2_form_trends": {"meyern": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "KAISER": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 106.0}, "Tuurtle": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 91.0}, "bruninho": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}, "zock": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "ksloks", "player1_kd": 0.97, "player2": "<PERSON><PERSON><PERSON><PERSON>", "player2_kd": 0.89, "impact": "MEDIUM", "description": "Tactical battle: k<PERSON><PERSON><PERSON> vs br<PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "<PERSON><PERSON> Oddik to win match", "confidence": 79.56299999999999, "reasoning": ["Team strength difference: 19.7", "Primary betting market"]}, "FIRST_MAP_WINNER": {"prediction": "Bounty Hunters BH CCT SA 23 06 25 first map", "confidence": 67.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: WOOD7 vs meyern", "confidence": 55, "reasoning": ["K/D comparison: 1.15 vs 1.16"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1387729-oddik-oddik-vs-bounty-hunters-bh-cct-sa-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:53:27.605336"}}, "page_content": ""}, {"team1": {"name": "Iberian Soul IS", "ranking": 44, "ensi_score": 1689, "winrate_10": 80.0, "winrate_30": 60.0, "current_shape": 120.0, "avg_kd": 0.94, "players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}]}, "team2": {"name": "Nexus Gaming", "ranking": 57, "ensi_score": 1622, "winrate_10": 70.0, "winrate_30": 57.0, "current_shape": 113.0, "avg_kd": 1.03, "players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Iberian Soul IS", "confidence": 61.05708333333334, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - XELLOW most kills vs sausol (79.0% confidence) | Alternative: MAP_HANDICAP (72.6286111111111%)", "key_factors": ["📈 Better recent form: Iberian Soul IS (13W-8L vs 6W-12L)", "🏆 Ranking advantage: Iberian Soul IS (#44 vs #57)", "📈 ENSI advantage: Iberian Soul IS (1689 vs 1622)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 Galaxy Battle Phase 3", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.02, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}], "team2_players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}], "team1_avg_kd": 0.94, "team2_avg_kd": 1.03}, "recent_performance": {"team1_recent_matches": [{"score": "1:0", "result": "W", "opponent": "LIVE\n        \n\n\n\n\n\n\n\nNexus\n\n\n\n\n\n\n\nBTN\n\nCatalin", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "BC", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Leo Team\nLeo\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n                    All IS Encounters\n                \n\n\n\n\nNexus Gaming\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:1", "result": "L", "opponent": "Quejo Cano\n\n\n\n\nstadodo\n\nRenato Gonçalves\n\n\n\n\nsausol\n\nPere Solsona Saumell\n\n\n\n\ndav1g\n\n<PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "50\n\n\n\n\n\n\nIberian Soul\nIS", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "20\n\n\n\n\n\n\nIberian Soul\nIS", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "57\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "56\n\n\n\n\n\n\n500\n500", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 13, "losses": 8}, "team2_recent_form": {"wins": 6, "losses": 12}, "team1_opponents": ["LIV", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "K27", "TEA", "ALG", "Nex", "Mar", "LIV", "Mar", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "K27", "TEA", "ALG", "Nex", "Mar"], "team2_opponents": ["Que", "<PERSON><PERSON>", "<PERSON>n", "Nex", "Nex", "GUN", "Nex"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 27, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["CYBERSHOKE Esports", "Sashi Esport", "<PERSON><PERSON>", "Monte", "FAVBET Team", "FAVBET", "Astrum", "Iberian Soul", "BC.Game Esports", "BC.G", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>.", "Leo Team", "<PERSON>", "Ninjas in Pyjamas", "NiP", "500", "Nexus Gaming", "Nexus", "K27", "TEAM NEXT LEVEL", "TNL", "ALGO Esports", "ALGO", "GUN5 Esports", "GUN5", "<PERSON>"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"sausol": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "stadodo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "mopoz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "ALEX": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "dav1g": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "team2_form_trends": {"XELLOW": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "ragga": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "lauNX": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "s0und": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "BTN": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 75.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "dav1g", "player1_kd": 0.79, "player2": "BTN", "player2_kd": 0.75, "impact": "MEDIUM", "description": "Tactical battle: dav1g vs BTN"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"XELLOW": {"team": "Nexus Gaming", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Iberian Soul IS to win match", "confidence": 61.05708333333334, "reasoning": ["Team strength difference: 7.4", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Nexus Gaming +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 72.6286111111111, "reasoning": ["Based on team strength difference: 7.4"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 70.6286111111111, "reasoning": ["Team strength analysis: 7.4 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "XELLOW most kills vs sausol", "confidence": 79.0, "reasoning": ["K/D comparison: 1.02 vs 1.21"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378181-iberian-soul-is-vs-nexus-gaming-nexus-gb-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:53:49.057284"}}, "page_content": ""}, {"team1": {"name": "Genone Gone", "ranking": 95, "ensi_score": 1514, "winrate_10": 70.0, "winrate_30": 63.0, "current_shape": 107.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "KS Esports", "ranking": 143, "ensi_score": 1441, "winrate_10": 50.0, "winrate_30": 55.0, "current_shape": 95.0, "avg_kd": 0.95, "players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Genone Gone", "confidence": 63.88616666666666, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - KS Esports +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (70.74255555555555% confidence) | Alternative: FIRST_MAP_WINNER (64.0%)", "key_factors": ["📈 Better recent form: Genone Gone (12W-4L vs 4W-12L)", "🏆 Ranking advantage: <PERSON><PERSON> (#95 vs #143)", "📈 ENSI advantage: Genone Gone (1514 vs 1441)", "⚡ Better current shape: Genone Gone (107.0% vs 95.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.16, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}], "team1_avg_kd": 1.01, "team2_avg_kd": 0.95}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Rebels Gaming\nREBELS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "GenOne\nGOne\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "ENERGYULTRA\nENY\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n                    All GOne Encounters\n                \n\n\n\n\nKS Esports\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "25\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "25\n\n\n\n\n\n\nKronjyllands esports\nKronjy", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "09\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "57\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "28\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 12, "losses": 4}, "team2_recent_form": {"wins": 4, "losses": 12}, "team1_opponents": ["<PERSON><PERSON>", "Gen", "ENE", "Vol", "Vol", "Vol", "<PERSON><PERSON>", "Gen", "ENE", "Vol"], "team2_opponents": ["Gen", "<PERSON><PERSON>", "Gen", "Gen", "los", "Pre"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 15, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["los kogutos", "Kronjyllands esports", "<PERSON><PERSON><PERSON><PERSON>", "Volt", "Prestige", "GenOne", "GOne", "Rebels Gaming", "REBELS", "ENERGYULTRA", "ENY", "XI Esport", "KS Esports", "HEROIC Academy", "Hero.A"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Chucky": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "Brooxsy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Tarkky": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "cHeuuuuk": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "SLIE9000": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"tripey": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "BledarD": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "ammar": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Caleyy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "gejmzilla": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 67.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "player1_kd": 0.87, "player2": "gejmzilla", "player2_kd": 0.67, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs gejmzilla"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Genone Gone to win match", "confidence": 63.88616666666666, "reasoning": ["Team strength difference: 9.3", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "KS Esports +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 70.74255555555555, "reasoning": ["Based on team strength difference: 9.3"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 9.3 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Genone Gone first map", "confidence": 64.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON> vs <PERSON><PERSON>", "confidence": 55, "reasoning": ["K/D comparison: 1.16 vs 1.11"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384810-genone-gone-vs-ks-esports-ks-untd21-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:54:11.372705"}}, "page_content": ""}, {"team1": {"name": "Tpudcatb TPU TPU", "ranking": 216, "ensi_score": 1387, "winrate_10": 33.0, "winrate_30": 33.0, "current_shape": 100.0, "avg_kd": 0.94, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.03}, {"name": "z1Nny", "nationality": "", "kd_ratio": 1.03}, {"name": "kin<PERSON>e", "nationality": "", "kd_ratio": 0.83}, {"name": "executorrr", "nationality": "", "kd_ratio": 0.83}, {"name": "Wierd_1k", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "KS Esports KS WIN 24 06 25", "ranking": 143, "ensi_score": 1441, "winrate_10": 50.0, "winrate_30": 55.0, "current_shape": 95.0, "avg_kd": 0.95, "players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "KS Esports KS WIN 24 06 25", "confidence": 56.54075000000001, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Tpudcatb TPU TPU +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (75% confidence) | Alternative: TOTAL_MAPS (73.6395%)", "key_factors": ["🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: KS Esports KS WIN 24 06 25 (#216 vs #143)", "📈 ENSI advantage: KS Esports KS WIN 24 06 25 (1387 vs 1441)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 Exort Series #12", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.03, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.03}, {"name": "z1Nny", "nationality": "", "kd_ratio": 1.03}, {"name": "kin<PERSON>e", "nationality": "", "kd_ratio": 0.83}, {"name": "executorrr", "nationality": "", "kd_ratio": 0.83}, {"name": "Wierd_1k", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}], "team1_avg_kd": 0.94, "team2_avg_kd": 0.95}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "RUSTEC\nRUSTEC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "3:0", "result": "W", "opponent": "Insilio\nInsilio\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Rebels Gaming\nREBELS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "ROSY\nROSY\n\n\n\n\n\n\n\n\n\n\n                    All TPu Encounters\n                \n\n\n\n\nKS Esports\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "44\n\n\n\n\n\n\nTPuDCATb TPu\nTPu", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "15\n\n\n\n\n\n\nTPuDCATb TPu\nTPu", "tournament": "Recent", "date": "Recent"}, {"score": "0:3", "result": "L", "opponent": "45\n\n\n\n\n\n\nTPuDCATb TPu\nTPu", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "12\n\n\n\n\n\n\nTPuDCATb TPu\nTPu", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "04\n\n\n\n\n\n\nTPuDCATb TPu\nTPu", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 9, "losses": 7}, "team2_recent_form": {"wins": 6, "losses": 9}, "team1_opponents": ["RUS", "Ins", "<PERSON><PERSON>", "ROS", "Vol", "ROS", "RUS", "Ins", "<PERSON><PERSON>", "ROS", "Vol"], "team2_opponents": ["TPu", "TPu", "TPu", "los", "Pre"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Kiro": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 103.0}, "z1Nny": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 103.0}, "kinqie": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 83.0}, "executorrr": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 83.0}, "Wierd_1k": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"tripey": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "BledarD": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "ammar": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Caleyy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "gejmzilla": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 67.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "kin<PERSON>e", "player1_kd": 0.83, "player2": "gejmzilla", "player2_kd": 0.67, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs gejm<PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "KS Esports KS WIN 24 06 25 to win match", "confidence": 56.54075000000001, "reasoning": ["Team strength difference: 4.4", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Tpudcatb TPU TPU +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "reasoning": ["Based on team strength difference: 4.4"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 73.6395, "reasoning": ["Team strength analysis: 4.4 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON> vs <PERSON><PERSON>", "confidence": 55, "reasoning": ["K/D comparison: 1.03 vs 1.11"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1365023-tpudcatb-tpu-tpu-vs-ks-esports-ks-win-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:54:33.292755"}}, "page_content": ""}, {"team1": {"name": "Akimbo Esports", "ranking": 263, "ensi_score": 1369, "winrate_10": 20.0, "winrate_30": 27.0, "current_shape": 93.0, "avg_kd": 0.95, "players": [{"name": "laxiee", "nationality": "", "kd_ratio": 1.07}, {"name": "zy", "nationality": "", "kd_ratio": 1.01}, {"name": "N2o", "nationality": "", "kd_ratio": 0.98}, {"name": "obi", "nationality": "", "kd_ratio": 0.9}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "United States", "kd_ratio": 0.78}]}, "team2": {"name": "Marca <PERSON>", "ranking": 140, "ensi_score": 1444, "winrate_10": 40.0, "winrate_30": 43.0, "current_shape": 97.0, "avg_kd": 0.85, "players": [{"name": "AMC", "nationality": "", "kd_ratio": 1.14}, {"name": "spamzzy", "nationality": "", "kd_ratio": 0.98}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.81}, {"name": "b1", "nationality": "", "kd_ratio": 0.73}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.6}]}, "h2h_record": "Akimbo: 0 - Draws: 1 - Marca: 0 (0% vs 0%)", "prediction": "Marca <PERSON>", "confidence": 59.477500000000006, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Akimbo Esports +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (73.68166666666667% confidence) | Alternative: TOTAL_MAPS (71.68166666666667%)", "key_factors": ["🆚 H2H record: Even (0-0) 🎯 (Similar rosters - high relevance)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: <PERSON><PERSON> (#263 vs #140)", "📈 ENSI advantage: <PERSON><PERSON> (1369 vs 1444)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "Akimbo: 0 - Draws: 1 - Marca: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 1, "recent_matches": [], "team1_name": "Akimbo", "team2_name": "<PERSON><PERSON>", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 PGL Astana", "h2h_history": [{"score": ":", "context": "recent_match"}, {"score": ":", "context": "recent_match"}, {"score": ":", "context": "recent_match"}, {"score": ":", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.07, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "laxiee", "nationality": "", "kd_ratio": 1.07}, {"name": "zy", "nationality": "", "kd_ratio": 1.01}, {"name": "N2o", "nationality": "", "kd_ratio": 0.98}, {"name": "obi", "nationality": "", "kd_ratio": 0.9}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "United States", "kd_ratio": 0.78}], "team2_players": [{"name": "AMC", "nationality": "", "kd_ratio": 1.14}, {"name": "spamzzy", "nationality": "", "kd_ratio": 0.98}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.81}, {"name": "b1", "nationality": "", "kd_ratio": 0.73}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.6}], "team1_avg_kd": 0.95, "team2_avg_kd": 0.85}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "Akimbo Esports\nAkimbo\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Akimbo Esports\nAkimbo\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Take Flyte\nTF\n\n\n\n\n\n\n\n\n\n\n                    All Akimbo Encounters\n                \n\n\n\n\nMarca Registrada\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Marca Registrada\nMarca\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Subtick\nSubtick\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "31\n\n\n\n\n\n\nParty Astronauts\nPA", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "46\n\n\n\n\n\n\nTeam Aether\nAether", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "48\n\n\n\n\n\n\nBOSS\nBOSS", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "25\n\n\n\n\n\n\nAkimbo Esports\nAkimbo", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "54\n\n\n\n\n\n\nMIGHT\nMIGHT", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 9, "losses": 9}, "team2_recent_form": {"wins": 10, "losses": 8}, "team1_opponents": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Tak", "Mar", "Sub", "Mar", "Mar", "Pro", "Pro", "Tak", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tak", "Mar", "Sub", "Mar", "Mar", "Pro"], "team2_opponents": ["Par", "Tea", "BOS", "<PERSON><PERSON>", "MIG", "Mar", "Get", "SUP", "Mar"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"laxiee": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "zy": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "N2o": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "obi": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "Zamgaa": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 78.0}}, "team2_form_trends": {"AMC": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 113.99999999999999}, "spamzzy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "Jardani": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 81.0}, "b1": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 73.0}, "Majesticzz": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 60.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON><PERSON>", "player1_kd": 0.78, "player2": "<PERSON><PERSON><PERSON><PERSON>", "player2_kd": 0.6, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Marca Registrada to win match", "confidence": 59.477500000000006, "reasoning": ["Team strength difference: 6.3", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Akimbo Esports +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 73.68166666666667, "reasoning": ["Based on team strength difference: 6.3"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 71.68166666666667, "reasoning": ["Team strength analysis: 6.3 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON> vs AMC", "confidence": 55, "reasoning": ["K/D comparison: 1.07 vs 1.14"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384794-akimbo-esports-akimbo-vs-marca-registrada-marca-d2us-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:54:56.088944"}}, "page_content": ""}, {"team1": {"name": "Fish123 Fish123", "ranking": 150, "ensi_score": 1433, "winrate_10": 67.0, "winrate_30": 67.0, "current_shape": 100.0, "avg_kd": 0.9, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.02}, {"name": "stressarN", "nationality": "", "kd_ratio": 1.0}, {"name": "robiin", "nationality": "Sweden", "kd_ratio": 0.9}, {"name": "juho", "nationality": "", "kd_ratio": 0.8}, {"name": "MAGILA", "nationality": "", "kd_ratio": 0.8}]}, "team2": {"name": "Kronjyllands Esports Kronjy", "ranking": 246, "ensi_score": 1379, "winrate_10": 40.0, "winrate_30": 33.0, "current_shape": 107.0, "avg_kd": 0.83, "players": [{"name": "Avou", "nationality": "Denmark", "kd_ratio": 0.73}, {"name": "emili0", "nationality": "Denmark", "kd_ratio": 0.73}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.7}, {"name": "Egelund", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "N4xx1s", "nationality": "Denmark", "kd_ratio": 1.0}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Fish123 Fish123", "confidence": 61.36025000000001, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Kronjyllands Esports Kronjy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (72.4265% confidence) | Alternative: TOTAL_MAPS (70.4265%)", "key_factors": ["📈 Better recent form: Fish123 Fish123 (16W-3L vs 2W-15L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: Fish123 Fish123 (#150 vs #246)", "📈 ENSI advantage: Fish123 Fish123 (1433 vs 1379)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.02, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.02}, {"name": "stressarN", "nationality": "", "kd_ratio": 1.0}, {"name": "robiin", "nationality": "Sweden", "kd_ratio": 0.9}, {"name": "juho", "nationality": "", "kd_ratio": 0.8}, {"name": "MAGILA", "nationality": "", "kd_ratio": 0.8}], "team2_players": [{"name": "Avou", "nationality": "Denmark", "kd_ratio": 0.73}, {"name": "emili0", "nationality": "Denmark", "kd_ratio": 0.73}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.7}, {"name": "Egelund", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "N4xx1s", "nationality": "Denmark", "kd_ratio": 1.0}], "team1_avg_kd": 0.9, "team2_avg_kd": 0.83}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "los kogutos\nLK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Fish123\nFish123\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Fish123\nFish123\n\n\n\n\n\n\n\n\n\n\n                    All Fish123 Encounters\n                \n\n\n\n\nKronjyllands esports\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "35\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "10\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "29\n\n\n\n\n\n\nKubix Esports\nkubix", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "45\n\n\n\n\n\n\nFish123\nFish123", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "11\n\n\n\n\n\n\nlos kogutos\nLK", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 16, "losses": 3}, "team2_recent_form": {"wins": 2, "losses": 15}, "team1_opponents": ["los", "Fis", "Vol", "Fis", "Gen", "<PERSON><PERSON>", "QMI", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fis", "los", "Fis", "Vol", "Fis", "Gen", "<PERSON><PERSON>", "QMI", "<PERSON><PERSON>"], "team2_opponents": ["Fis", "<PERSON><PERSON>", "Fis", "los", "<PERSON><PERSON>", "ENE", "<PERSON><PERSON>", "Nor"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Smooya": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "stressarN": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "robiin": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "juho": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}, "MAGILA": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}}, "team2_form_trends": {"Avou": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 73.0}, "emili0": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 73.0}, "Jiace": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 70.0}, "Egelund": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "N4xx1s": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "juho", "player1_kd": 0.8, "player2": "<PERSON><PERSON>", "player2_kd": 0.7, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs <PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Fish123 Fish123 to win match", "confidence": 61.36025000000001, "reasoning": ["Team strength difference: 7.6", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Kronjyllands Esports Kronjy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 72.4265, "reasoning": ["Based on team strength difference: 7.6"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 70.4265, "reasoning": ["Team strength analysis: 7.6 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON> vs Egelund", "confidence": 55, "reasoning": ["K/D comparison: 1.02 vs 1.00"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384811-fish123-fish123-vs-kronjyllands-esports-kronjy-untd21-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:55:17.865335"}}, "page_content": ""}, {"team1": {"name": "Nexus Gaming", "ranking": 57, "ensi_score": 1622, "winrate_10": 70.0, "winrate_30": 57.0, "current_shape": 113.0, "avg_kd": 1.03, "players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}]}, "team2": {"name": "Eternal Fire EF EXO 23 06 25", "ranking": 7, "ensi_score": 1887, "winrate_10": 80.0, "winrate_30": 70.0, "current_shape": 110.0, "avg_kd": 0.96, "players": [{"name": "lugseN", "nationality": "", "kd_ratio": 1.11}, {"name": "Calyx", "nationality": "", "kd_ratio": 1.08}, {"name": "imoRR", "nationality": "", "kd_ratio": 1.0}, {"name": "emstar", "nationality": "", "kd_ratio": 0.88}, {"name": "j<PERSON>y", "nationality": "", "kd_ratio": 0.73}]}, "h2h_record": "Nexus: 0 - Draws: 0 - EF: 1 (0% vs 100%)", "prediction": "Eternal Fire EF EXO 23 06 25", "confidence": 89.81508333333335, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Eternal Fire EF EXO 23 06 25 -1.5 maps (2-0 win) (79.5433888888889% confidence) | Alternative: MATCH_WINNER (79.31508333333335%)", "key_factors": ["🆚 H2H record: Eternal Fire EF EXO 23 06 25 leads (1-0) 🎯 (Similar rosters - high relevance)", "🏆 Ranking advantage: Eternal Fire EF EXO 23 06 25 (#57 vs #7)", "📈 ENSI advantage: Eternal Fire EF EXO 23 06 25 (1622 vs 1887)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "Nexus: 0 - Draws: 0 - EF: 1 (0% vs 100%)", "team1_wins": 0, "team2_wins": 1, "draws": 0, "recent_matches": [": Eternal Fire 2:0 EF"], "team1_name": "Nexus", "team2_name": "EF", "team1_win_percentage": 0, "team2_win_percentage": 100, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2021 Pinnacle Fall Series #2", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 4}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.21, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "XELLOW", "nationality": "", "kd_ratio": 1.21}, {"name": "ragga", "nationality": "", "kd_ratio": 1.08}, {"name": "lauNX", "nationality": "", "kd_ratio": 1.08}, {"name": "s0und", "nationality": "", "kd_ratio": 1.01}, {"name": "BTN", "nationality": "", "kd_ratio": 0.75}], "team2_players": [{"name": "lugseN", "nationality": "", "kd_ratio": 1.11}, {"name": "Calyx", "nationality": "", "kd_ratio": 1.08}, {"name": "imoRR", "nationality": "", "kd_ratio": 1.0}, {"name": "emstar", "nationality": "", "kd_ratio": 0.88}, {"name": "j<PERSON>y", "nationality": "", "kd_ratio": 0.73}], "team1_avg_kd": 1.03, "team2_avg_kd": 0.96}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "Nexus Gaming\nNexus\n\n\n\n\n\n\n\n\n\n\n\n            All Nexus and EF Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "K27\nK27\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "TEAM NEXT LEVEL\nTNL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "ALGO Esports\nALGO\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Nexus Gaming\nNexus\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "08\n\n\n\n\n\n\nEternal Fire\nEF", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "02\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "05\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "21\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "38\n\n\n\n\n\n\nGUN5 Esports\nGUN5", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 13, "losses": 9}, "team2_recent_form": {"wins": 7, "losses": 10}, "team1_opponents": ["Nex", "K27", "TEA", "ALG", "Nex", "Mar", "Ete", "Ete", "BAS", "Fir", "San", "Nex", "Mar", "K27", "TEA", "ALG", "Nex", "Mar", "Ete", "Ete", "BAS", "Fir"], "team2_opponents": ["<PERSON><PERSON>", "Ete", "Nex", "Nex", "GUN", "Nex", "RUS", "Ete"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 19, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Eternal Fire", "Nexus Gaming", "Nexus", "Fire Flux Esports", "TEAM NEXT LEVEL", "TNL", "K27", "ALGO Esports", "ALGO", "GUN5 Esports", "GUN5", "<PERSON>", "RUSTEC", "FORZE Reload", "<PERSON><PERSON>", "BASEMENT BOYS", "BSM", "Sangal Academy", "Sangal.A"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"XELLOW": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "ragga": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "lauNX": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "s0und": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "BTN": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 75.0}}, "team2_form_trends": {"lugseN": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "Calyx": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "imoRR": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "emstar": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "jresy": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 73.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "BTN", "player1_kd": 0.75, "player2": "j<PERSON>y", "player2_kd": 0.73, "impact": "MEDIUM", "description": "Tactical battle: BTN vs jresy"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"XELLOW": {"team": "Nexus Gaming", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Eternal Fire EF EXO 23 06 25 to win match", "confidence": 79.31508333333335, "reasoning": ["Team strength difference: 19.5", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Eternal Fire EF EXO 23 06 25 -1.5 maps (2-0 win)", "confidence": 79.5433888888889, "reasoning": ["Based on team strength difference: 19.5"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 19.5 difference"]}, "CORRECT_SCORE": {"prediction": "Eternal Fire EF EXO 23 06 25 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: XELLOW vs lugseN", "confidence": 55, "reasoning": ["K/D comparison: 1.21 vs 1.11"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1363316-nexus-gaming-nexus-vs-eternal-fire-ef-exo-23-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 14:55:40.314156"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 1850.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 100, "recommendations": [{"match": "Keyd Stars vs KRU Esport KRU CCT SA 24 06 25", "bet_type": "Moneyline", "recommendation": "KRU Esport KRU CCT SA 24 06 25 to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 32.2", "Primary betting market"]}, {"match": "Bestia Bestia vs Shinden Shinden CCT SA 24 06 25", "bet_type": "Moneyline", "recommendation": "Bestia Bestia to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 25.8", "Primary betting market"]}, {"match": "Se<PERSON><PERSON> vs 2game 2game CCT SA 23 06 25", "bet_type": "Moneyline", "recommendation": "2game 2game CCT SA 23 06 25 to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 27.5", "Primary betting market"]}, {"match": "Sharks Esports vs LP LP CCT SA 24 06 25", "bet_type": "Moneyline", "recommendation": "Sharks Esports to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 26.4", "Primary betting market"]}, {"match": "FURIA Esports Female Furiafe vs Flamengo Esports Flamengo CCT SA 24 06 25", "bet_type": "Moneyline", "recommendation": "FURIA Esports Female Furiafe to win match", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 26.2", "Primary betting market"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Moneyline", "recommendation": "9z Team to win match", "confidence": 81.85091666666665, "value_rating": 0.637018333333333, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 21.2", "Primary betting market"]}, {"match": "Bestia Bestia vs Shinden Shinden CCT SA 24 06 25", "bet_type": "Map Handicap", "recommendation": "Bestia Bestia -1.5 maps (2-0 win)", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 25.8"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Map Handicap", "recommendation": "9z Team -1.5 maps (2-0 win)", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 21.2"]}, {"match": "Se<PERSON><PERSON> vs 2game 2game CCT SA 23 06 25", "bet_type": "Map Handicap", "recommendation": "2game 2game CCT SA 23 06 25 -1.5 maps (2-0 win)", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 27.5"]}, {"match": "Oddik Oddik vs Bounty Hunters BH CCT SA 23 06 25", "bet_type": "Moneyline", "recommendation": "<PERSON><PERSON> Oddik to win match", "confidence": 79.56299999999999, "value_rating": 0.5912599999999997, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 19.7", "Primary betting market"]}, {"match": "Nexus Gaming vs Eternal Fire EF EXO 23 06 25", "bet_type": "Map Handicap", "recommendation": "Eternal Fire EF EXO 23 06 25 -1.5 maps (2-0 win)", "confidence": 79.5433888888889, "value_rating": 0.590867777777778, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 19.5"]}, {"match": "Nexus Gaming vs Eternal Fire EF EXO 23 06 25", "bet_type": "Moneyline", "recommendation": "Eternal Fire EF EXO 23 06 25 to win match", "confidence": 79.31508333333335, "value_rating": 0.5863016666666669, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 19.5", "Primary betting market"]}, {"match": "Tpudcatb TPU TPU vs KS Esports KS WIN 24 06 25", "bet_type": "Map Handicap", "recommendation": "Tpudcatb TPU TPU +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 4.4"]}, {"match": "XI Esport vs Prestige Prestige", "bet_type": "Map Handicap", "recommendation": "XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 74.63461111111111, "value_rating": 0.4926922222222223, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 5.4"]}, {"match": "HEROIC Academy vs Genone Gone", "bet_type": "Map Handicap", "recommendation": "HEROIC Academy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 74.20772222222223, "value_rating": 0.48415444444444455, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 5.8"]}, {"match": "Akimbo Esports vs Marca Registrada", "bet_type": "Map Handicap", "recommendation": "Akimbo Esports +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 73.68166666666667, "value_rating": 0.47363333333333335, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 6.3"]}, {"match": "Tpudcatb TPU TPU vs KS Esports KS WIN 24 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 73.6395, "value_rating": 0.47279000000000004, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 4.4 difference"]}, {"match": "XI Esport vs Prestige Prestige", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 72.63461111111111, "value_rating": 0.4526922222222223, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 5.4 difference"]}, {"match": "Iberian Soul IS vs Nexus Gaming", "bet_type": "Map Handicap", "recommendation": "Nexus Gaming +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 72.6286111111111, "value_rating": 0.45257222222222193, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 7.4"]}, {"match": "Se<PERSON><PERSON> vs 2game 2game CCT SA 23 06 25", "bet_type": "Total Maps", "recommendation": "UNDER 2.5 maps (2-0 likely)", "confidence": 72.51166666666666, "value_rating": 0.45023333333333304, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 27.5 difference"]}, {"match": "Fish123 Fish123 vs Kronjyllands Esports Kronjy", "bet_type": "Map Handicap", "recommendation": "Kronjyllands Esports Kronjy +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 72.4265, "value_rating": 0.4485300000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 7.6"]}, {"match": "HEROIC Academy vs Genone Gone", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 72.20772222222223, "value_rating": 0.4441544444444445, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 5.8 difference"]}, {"match": "Akimbo Esports vs Marca Registrada", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 71.68166666666667, "value_rating": 0.43363333333333354, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 6.3 difference"]}, {"match": "Bestia Bestia vs Shinden Shinden CCT SA 24 06 25", "bet_type": "Total Maps", "recommendation": "UNDER 2.5 maps (2-0 likely)", "confidence": 70.75311111111111, "value_rating": 0.4150622222222222, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 25.8 difference"]}, {"match": "Genone Gone vs KS Esports", "bet_type": "Map Handicap", "recommendation": "KS Esports +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 70.74255555555555, "value_rating": 0.41485111111111106, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 9.3"]}, {"match": "Iberian Soul IS vs Nexus Gaming", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 70.6286111111111, "value_rating": 0.4125722222222219, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 7.4 difference"]}, {"match": "Fish123 Fish123 vs Kronjyllands Esports Kronjy", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 70.4265, "value_rating": 0.40853000000000006, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 7.6 difference"]}, {"match": "Bestia Bestia vs Shinden Shinden CCT SA 24 06 25", "bet_type": "Total Rounds", "recommendation": "Bestia Bestia first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Total Rounds", "recommendation": "9z Team first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Sharks Esports vs LP LP CCT SA 24 06 25", "bet_type": "Total Rounds", "recommendation": "Sharks Esports first map", "confidence": 69.0, "value_rating": 0.3799999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Oddik Oddik vs Bounty Hunters BH CCT SA 23 06 25", "bet_type": "Total Rounds", "recommendation": "Bounty Hunters BH CCT SA 23 06 25 first map", "confidence": 67.5, "value_rating": 0.3500000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "9z Team vs Dusty Roots DR CCT SA 24 06 25", "bet_type": "Total Maps", "recommendation": "UNDER 2.5 maps (2-0 likely)", "confidence": 66.23394444444443, "value_rating": 0.32467888888888874, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 21.2 difference"]}, {"match": "FURIA Esports Female Furiafe vs Flamengo Esports Flamengo CCT SA 24 06 25", "bet_type": "Total Rounds", "recommendation": "FURIA Esports Female Furiafe first map", "confidence": 65.0, "value_rating": 0.30000000000000004, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Genone Gone vs KS Esports", "bet_type": "Total Rounds", "recommendation": "Genone Gone first map", "confidence": 64.0, "value_rating": 0.28, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Keyd Stars vs KRU Esport KRU CCT SA 24 06 25", "bet_type": "Total Rounds", "recommendation": "KRU Esport KRU CCT SA 24 06 25 first map", "confidence": 63.0, "value_rating": 0.26, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Genone Gone vs KS Esports", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 9.3 difference"]}, {"match": "Nexus Gaming vs Eternal Fire EF EXO 23 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 19.5 difference"]}]}, "enhancement_stats": {"total_predictions": 15, "average_confidence": 77.04099, "premium_bets": 8, "strong_bets": 0, "good_bets": 0, "lean_bets": 1, "confidence_distribution": {"premium_pct": 53.333333333333336, "strong_pct": 0.0, "good_pct": 0.0, "lean_pct": 6.666666666666667}}}