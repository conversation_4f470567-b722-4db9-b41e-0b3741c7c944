#!/usr/bin/env python3
"""
AUTOMATIC MATCH RESULT COLLECTOR
===============================
Automatically scrapes match results from Ensigame and feeds them back
to the ML calibration system for continuous learning.
"""

import json
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from prediction_calibration import PredictionCalibrator, PredictionResult

class MatchResultCollector:
    """Automatically collects match results for ML learning"""
    
    def __init__(self, calibrator: PredictionCalibrator):
        self.calibrator = calibrator
        self.pending_predictions_file = "pending_predictions.json"
        self.pending_predictions = []
        self.load_pending_predictions()
        
        # Setup Chrome driver for result scraping
        self.setup_driver()
    
    def setup_driver(self):
        """Setup Chrome driver for scraping results"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Result collector driver initialized")
        except Exception as e:
            print(f"⚠️ Failed to initialize driver: {e}")
            self.driver = None
    
    def save_prediction_for_tracking(self, prediction_data: Dict, match_url: str):
        """Save a prediction to track its result later"""
        
        pending_prediction = {
            'match_id': self.generate_match_id(match_url),
            'match_url': match_url,
            'prediction_data': prediction_data,
            'timestamp': datetime.now().isoformat(),
            'checked': False,
            'result_found': False
        }
        
        self.pending_predictions.append(pending_prediction)
        self.save_pending_predictions()
        
        print(f"📝 Saved prediction for tracking: {prediction_data.get('prediction', 'Unknown')}")
    
    def generate_match_id(self, match_url: str) -> str:
        """Generate unique match ID from URL"""
        # Extract match ID from Ensigame URL
        # Example: https://ensigame.com/matches/cs-2/1369180-natus-vincere-navi-vs-team-vitality-vitality-blast-19-06-25
        if '/matches/cs-2/' in match_url:
            match_id = match_url.split('/matches/cs-2/')[1].split('-')[0]
            return f"ensigame_{match_id}"
        else:
            return f"match_{hash(match_url)}"
    
    def load_pending_predictions(self):
        """Load pending predictions from file"""
        try:
            with open(self.pending_predictions_file, 'r') as f:
                self.pending_predictions = json.load(f)
            print(f"✅ Loaded {len(self.pending_predictions)} pending predictions")
        except FileNotFoundError:
            self.pending_predictions = []
            print("📝 No pending predictions file found - starting fresh")
        except Exception as e:
            print(f"⚠️ Failed to load pending predictions: {e}")
            self.pending_predictions = []
    
    def save_pending_predictions(self):
        """Save pending predictions to file"""
        try:
            with open(self.pending_predictions_file, 'w') as f:
                json.dump(self.pending_predictions, f, indent=2)
        except Exception as e:
            print(f"⚠️ Failed to save pending predictions: {e}")
    
    def check_match_results(self):
        """Check results for all pending predictions"""
        
        if not self.driver:
            print("⚠️ No driver available for result checking")
            return
        
        print(f"🔍 Checking results for {len(self.pending_predictions)} pending predictions...")
        
        results_found = 0
        
        for prediction in self.pending_predictions:
            if prediction['checked']:
                continue  # Already processed
            
            # Only check matches that should be finished (older than 4 hours)
            prediction_time = datetime.fromisoformat(prediction['timestamp'])
            if datetime.now() - prediction_time < timedelta(hours=4):
                continue  # Too recent, match might still be ongoing
            
            try:
                result = self.scrape_match_result(prediction['match_url'])
                if result:
                    self.process_match_result(prediction, result)
                    prediction['checked'] = True
                    prediction['result_found'] = True
                    results_found += 1
                    
                    # Small delay between requests
                    time.sleep(2)
                else:
                    # Mark as checked but no result found
                    prediction['checked'] = True
                    prediction['result_found'] = False
                    
            except Exception as e:
                print(f"⚠️ Error checking result for {prediction['match_id']}: {e}")
                continue
        
        self.save_pending_predictions()
        print(f"✅ Found results for {results_found} matches")
    
    def scrape_match_result(self, match_url: str) -> Optional[Dict]:
        """Scrape the actual result of a match from Ensigame"""
        
        try:
            print(f"🔍 Checking result for: {match_url}")
            self.driver.get(match_url)
            time.sleep(3)
            
            # Look for match result indicators
            result_data = {}
            
            # Check if match is finished
            try:
                # Look for "Ended" text or final score
                ended_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Ended')]")
                if not ended_elements:
                    print("   ⏳ Match not finished yet")
                    return None
                
                # Extract final score
                score_elements = self.driver.find_elements(By.CSS_SELECTOR, ".match-score, .score")
                if score_elements:
                    score_text = score_elements[0].text
                    result_data['final_score'] = score_text
                    
                    # Parse winner from score (e.g., "2:1" means team1 won)
                    if ':' in score_text:
                        team1_score, team2_score = score_text.split(':')
                        team1_score = int(team1_score.strip())
                        team2_score = int(team2_score.strip())
                        
                        if team1_score > team2_score:
                            result_data['winner'] = 'team1'
                        else:
                            result_data['winner'] = 'team2'
                        
                        result_data['team1_maps'] = team1_score
                        result_data['team2_maps'] = team2_score
                        result_data['total_maps'] = team1_score + team2_score
                
                # Extract team names for validation
                team_elements = self.driver.find_elements(By.CSS_SELECTOR, ".team-name, .team")
                if len(team_elements) >= 2:
                    result_data['team1_name'] = team_elements[0].text.strip()
                    result_data['team2_name'] = team_elements[1].text.strip()
                
                print(f"   ✅ Found result: {result_data}")
                return result_data
                
            except Exception as e:
                print(f"   ⚠️ Error parsing result: {e}")
                return None
                
        except Exception as e:
            print(f"⚠️ Failed to scrape result from {match_url}: {e}")
            return None
    
    def process_match_result(self, prediction: Dict, actual_result: Dict):
        """Process a match result and update ML calibration"""
        
        prediction_data = prediction['prediction_data']
        
        # Extract our predictions
        our_prediction = prediction_data.get('prediction', '')
        our_confidence = prediction_data.get('confidence', 0)
        
        # Check if our prediction was correct
        correct = self.evaluate_prediction_accuracy(prediction_data, actual_result)
        
        # Create PredictionResult for ML system
        result = PredictionResult(
            match_id=prediction['match_id'],
            prediction=our_prediction,
            confidence=our_confidence,
            actual_result=str(actual_result),
            correct=correct,
            timestamp=datetime.fromisoformat(prediction['timestamp']),
            market_type=prediction_data.get('market_type', 'match_winner'),
            tournament_tier=prediction_data.get('tournament_tier', 'Unknown'),
            team1_rank=prediction_data.get('team1_rank', 50),
            team2_rank=prediction_data.get('team2_rank', 50)
        )
        
        # Save to ML calibration system
        self.calibrator.save_prediction_result(result)
        
        print(f"📊 ML Learning: {our_prediction} was {'✅ CORRECT' if correct else '❌ WRONG'}")
        print(f"   Confidence: {our_confidence}% | Actual: {actual_result.get('winner', 'Unknown')}")
    
    def evaluate_prediction_accuracy(self, prediction_data: Dict, actual_result: Dict) -> bool:
        """Evaluate if our prediction was correct"""
        
        our_prediction = prediction_data.get('prediction', '').lower()
        actual_winner = actual_result.get('winner', '')
        
        # Match winner predictions
        if 'team1' in our_prediction or prediction_data.get('team1_name', '').lower() in our_prediction:
            return actual_winner == 'team1'
        elif 'team2' in our_prediction or prediction_data.get('team2_name', '').lower() in our_prediction:
            return actual_winner == 'team2'
        
        # Total maps predictions
        if 'over 2.5' in our_prediction:
            return actual_result.get('total_maps', 0) > 2.5
        elif 'under 2.5' in our_prediction:
            return actual_result.get('total_maps', 0) < 2.5
        
        # Map handicap predictions
        if '+1.5' in our_prediction:
            # Check if underdog avoided 0-2 loss
            team1_maps = actual_result.get('team1_maps', 0)
            team2_maps = actual_result.get('team2_maps', 0)
            return min(team1_maps, team2_maps) >= 1  # At least one map won
        
        # Default: assume incorrect if we can't parse
        return False
    
    def run_continuous_monitoring(self, check_interval_hours: int = 6):
        """Run continuous monitoring for match results"""
        
        print(f"🔄 Starting continuous result monitoring (every {check_interval_hours} hours)")
        
        while True:
            try:
                self.check_match_results()
                
                # Generate accuracy report
                if len(self.calibrator.prediction_history) > 0:
                    report = self.calibrator.get_accuracy_report()
                    print(f"📊 Current accuracy: {report.get('overall_accuracy', 0)*100:.1f}%")
                
                # Wait before next check
                time.sleep(check_interval_hours * 3600)
                
            except KeyboardInterrupt:
                print("🛑 Monitoring stopped by user")
                break
            except Exception as e:
                print(f"⚠️ Error in monitoring loop: {e}")
                time.sleep(300)  # Wait 5 minutes before retrying
    
    def cleanup(self):
        """Cleanup resources"""
        if self.driver:
            self.driver.quit()
            print("🧹 Result collector cleaned up")

# Example usage
if __name__ == "__main__":
    calibrator = PredictionCalibrator()
    collector = MatchResultCollector(calibrator)
    
    try:
        # Run one-time check
        collector.check_match_results()
        
        # Or run continuous monitoring
        # collector.run_continuous_monitoring(check_interval_hours=6)
        
    finally:
        collector.cleanup()
