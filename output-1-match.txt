PS J:\cs2-bet-sonnet-scrap> python enhanced_automated_pipeline.py --tier 1 2 3 --min-confidence 70 --max-matches 1
⚠️ Map pick predictor not available - using fallback logic
✅ All modules imported successfully
⚠️ HLTV real data scraper not available: No module named 'hltv_real_data_scraper'
⚠️ Enhanced Dust2 scraper disabled - using integrated dust2.in scraping
⚠️ Enhanced data integrator not available: No module named 'enhanced_data_integrator'
⚠️ Enhanced map stats scraper not available: No module named 'enhanced_map_stats_scraper'
================================================================================
🎯 CS2 BETTING SYSTEM VALIDATION SUMMARY
================================================================================
✅ CRITICAL CS2 MECHANICS COMPLIANCE:
   • Rounds to win: 13 (NOT 16 like CS:GO)
   • Maximum regulation rounds: 24 (12-12 goes to OT)
   • Overtime trigger: 12-12 (NOT 15-15)
   • Round handicap calculations use 13-round wins
   • Total rounds betting lines: 18.5-24.5 range
🚫 FALLBACK DATA ELIMINATION:
   • NO mock/placeholder data in betting logic
   • NO hardcoded team statistics
   • NO generic win rate assumptions
   • Only REAL, LIVE, DYNAMIC data sources
   • Ensigame, HLTV, dust2.in scraping only
📊 ENHANCED BETTING TYPES (CS2-COMPLIANT):
   • Match Winner (Moneyline)
   • Map Winner (Individual maps)
   • Round Handicap (-7.5 to +6.5 realistic bookmaker range)
   • Total Rounds (17.5 to 22.5 range, OVER/UNDER based on 13-round wins)
   • Map Handicap (-1.5/+1.5 for BO3)
   • Total Maps (BO3: OVER/UNDER 2.5)
   • Overtime (12-12 trigger)
   • Correct Map Score (2-0, 2-1, 1-2, 0-2)
🔍 REAL-DATA VALIDATION ENFORCED:
   • Team names: NO 'Team1'/'Team2' placeholders
   • Rankings: Live HLTV/Ensigame rankings only
   • Form data: Recent match results only
   • H2H records: Scraped match history only
   • Map statistics: dust2.in live data only
   • Player stats: Real K/D ratios and ratings
🎯 ACCURACY IMPROVEMENTS:
   • CS2 round mechanics validation on every prediction
   • Fallback data functions disabled/removed
   • Real-time odds integration
   • Tournament context weighting
   • Map-specific win rate analysis
   • Live veto detection and integration
⚠️ CRITICAL FIXES APPLIED:
   • FIXED: Round handicap examples (13-6 not 16-9)
   • FIXED: Eliminated all fallback map statistics
   • FIXED: Total rounds calculations use CS2 maximums
   • FIXED: Overtime references use 12-12 not 15-15
   • VALIDATED: All betting lines appropriate for CS2
🚀 SYSTEM READY FOR PRODUCTION:
   ✅ CS2 mechanics 100% compliant
   ✅ Real data only policy enforced
   ✅ No fallback data contamination
   ✅ Accurate betting calculations
   ✅ Enhanced prediction accuracy
   ✅ Live market integration
================================================================================
🎯 CS2 BETTING SYSTEM: FULLY OPTIMIZED & VALIDATED
================================================================================
🚀 ENHANCED CS2 BETTING AUTOMATION PIPELINE
======================================================================
✅ Final betting enhancements enabled
📊 Enhanced accuracy and confidence calibration
🎯 Professional-grade betting analysis
======================================================================
⚙️ Configuration:
   🎯 Tier Filter: [1, 2, 3]
   📊 Max Matches: 1
   📈 Min Confidence: 70.0%
   💰 Bankroll: $1,000.00
   ⏳ Delay: 3s
✅ Enhanced Chrome driver initialized
🎯 TRULY DYNAMIC Ensigame Scraper initialized
🚀 NOW EXTRACTS REAL DATA FROM ANY ENSIGAME URL
🚫 ZERO hardcoded values - 100% dynamic extraction!
📊 BATCH PROCESSING SUPPORT ENABLED
✅ Loaded 5 historical predictions
🎯 Prediction calibrator initialized - ML confidence calibration enabled
📊 Using standard betting logic without map predictions
🔍 Phase 1: Enhanced URL Discovery
--------------------------------------------------
🔍 Discovering CS2 matches (Tiers: [1, 2, 3])
🔍 Using ENHANCED browser extraction to find ALL matches...

DevTools listening on ws://127.0.0.1:26532/devtools/browser/82a27060-f5c5-4a65-97d6-dba90b068cd2
   🌐 Loading: https://ensigame.com/matches/cs-2
[11640:17996:0623/151340.916:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1092] [GroupMarkerNotSet(crbug.com/242999)!:A0802A005C430000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[15228:24616:0623/151341.102:ERROR:components\device_event_log\device_event_log_impl.cc:202] [15:13:41.102] USB: usb_service_win.cc:105 SetupDiGetDeviceProperty({{A45C254E-DF1C-4EFD-8020-67D146A850E0}, 6}) failed: Element not found. (0x490)
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1750706021.216441   24348 voice_transcription.cc:58] Registering VoiceTranscriptionCapability
   📜 AGGRESSIVE scrolling to load all matches...
Created TensorFlow Lite XNNPACK delegate for CPU.
Attempting to use a delegate that only supports static-sized tensors with a graph that has dynamic-sized tensors (tensor#-1 is a dynamic-sized tensor).
   📜 Deep scroll 1/8 completed
   📜 Deep scroll 2/8 completed
   📜 Deep scroll 3/8 completed
   📜 Deep scroll 4/8 completed
   📜 Deep scroll 5/8 completed
   📜 Deep scroll 6/8 completed
   📜 Deep scroll 7/8 completed
   📜 Deep scroll 8/8 completed
   🔍 Checking page sections for BLAST matches...
   🔍 Extracting match data from fully loaded page...
   📊 Found 142 total match elements
   🎯 Found 0 BLAST/TIER-1 elements via XPath
   🔍 DEBUG: Page source saved to debug_page_source.html
   ❌ No 'BLAST' found in page source
   ❌ No 'TIER-1' found in page source
   🔍 DEBUG - Element text: tier-3
untd21
bo3
tomorrow at 10:30 ...
   ✅ Found Tier 3 via pattern 'tier-3'
   ✅ DISCOVERED: Genone Gone vs Ks Esports - BO3 - Tier 3
   🔍 DEBUG - Element text: tier-3
epl s28
bo3
tomorrow at 10:30
xi esport
vs
prestige ...
   ✅ Found Tier 2 via pattern 'epl'
   ✅ DISCOVERED: Xi Esport Xi vs Prestige Prestige - BO3 - Tier 2
   🔍 DEBUG - Element text: furia esports female
vs
flamengo esports ...
   ✅ Found Tier 2 via URL indicators
   ✅ DISCOVERED: Furia Esports Female Furiafe vs Flamengo Esports - BO1 - Tier 2
   🔍 DEBUG - Element text: tier-3
cct sa
bo3 ...
   ✅ Found Tier 2 via pattern 'cct'
   ✅ DISCOVERED: Selva Selva vs 2Game 2Game - BO3 - Tier 2
   🔍 DEBUG - Element text: akimbo esports
vs
marca registrada ...
   ⚠️ Defaulting to Tier 3 - no tier indicators found
   ✅ DISCOVERED: Akimbo Esports Akimbo vs Marca Registrada - BO1 - Tier 3
   🔍 DEBUG - Element text: tier-3
cct sa
bo3
today at 19:20
9z team
vs
dusty roots ...
   ✅ Found Tier 2 via pattern 'cct'
   ✅ DISCOVERED: 9Z Team 9Z vs Dusty Roots - BO3 - Tier 2
   🔍 DEBUG - Element text: tier-3
gb
bo3
2nd map
iberian soul
1
:
0
live
nexus gaming
tier-3
cct sa
bo3
today at 19:20
9z team
vs
dusty roots
tier-3
exo
bo3
today at 19:40
nexus gaming
vs
eternal fire
tier-3
cct sa
bo3
today at...
   ✅ Found Tier 2 via pattern 'cct'
   ✅ DISCOVERED: Iberian Soul Is vs Nexus Gaming - BO3 - Tier 2
   🔍 DEBUG - Element text: tier-3
exo
bo3
today at 19:40 ...
   ✅ Found Tier 3 via pattern 'tier-3'
   ✅ DISCOVERED: Nexus Gaming Nexus vs Eternal Fire - BO3 - Tier 3
   🔍 DEBUG - Element text: tier-3
epl s28
bo3
tomorrow at 08:00 ...
   ✅ Found Tier 2 via pattern 'epl'
   ✅ DISCOVERED: Heroic Academy Heroa vs Genone Gone - BO3 - Tier 2
   🔍 DEBUG - Element text: tier-3
win
bo3
tomorrow at 14:00
tpudcatb tpu
vs
ks esports ...
   ✅ Found Tier 3 via pattern 'tier-3'
   ✅ DISCOVERED: Tpudcatb Tpu Tpu vs Ks Esports - BO3 - Tier 3
   🔍 DEBUG - Element text: tier-3
cct sa
bo3 ...
   ✅ Found Tier 2 via pattern 'cct'
   ✅ DISCOVERED: Sharks Esports Sharks vs Lp Lp - BO3 - Tier 2
   🔍 DEBUG - Element text: fish123
vs
kronjyllands esports ...
   ⚠️ Defaulting to Tier 3 - no tier indicators found
   ✅ DISCOVERED: Fish123 Fish123 vs Kronjyllands Esports - BO1 - Tier 3
   🔍 DEBUG - Element text: tier-3
win
bo3
jun 25 at 10:30
gun5 esports
vs
esc gaming ...
   ✅ Found Tier 3 via pattern 'tier-3'
   ✅ DISCOVERED: Gun5 Esports Gun5 vs Esc Gaming - BO3 - Tier 3
   🔍 DEBUG - Element text: tier-3
cct sa
bo3
tomorrow at 19:00
bestia
vs
shinden ...
   ✅ Found Tier 2 via pattern 'cct'
   ✅ DISCOVERED: Bestia Bestia vs Shinden Shinden - BO3 - Tier 2
   🔍 DEBUG - Element text: tier-3
cct sa
bo3
tomorrow at 16:00
oddik
vs
bounty hunters ...
   ✅ Found Tier 2 via pattern 'cct'
   ✅ DISCOVERED: Oddik Oddik vs Bounty Hunters - BO3 - Tier 2
   🎯 Successfully discovered 15 unique matches
💾 Enhanced URLs saved with metadata: enhanced_urls_20250623_151430.txt
💾 Discovered URLs saved (sorted by priority): enhanced_urls_20250623_151430.txt
📊 DISCOVERY BREAKDOWN:
   🥇 Tier-1 matches: 0
   🥈 Tier-2 matches: 1
   🥉 Tier-3 matches: 0
🏆 TOURNAMENTS:
   EPL: 1 matches
🎯 Matches matching filter (Tier [1, 2, 3]): 1
------------------------------------------------------------
📊 Phase 2: Sequential Match Analysis
--------------------------------------------------
🚀 ENHANCED AUTOMATED ANALYSIS STARTING
📊 Matches to analyze: 1
🎯 Min confidence threshold: 70.0%
💰 Bankroll: $1,000.00
================================================================================
📊 MATCH 1/1
🔗 URL: https://ensigame.com/matches/cs-2/1375305-xi-esport-xi-vs-prestige-prestige-epl-s28-24-06-25
------------------------------------------------------------
🔍 Scraping match: https://ensigame.com/matches/cs-2/1375305-xi-esport-xi-vs-prestige-prestige-epl-s28-24-06-25
🌐 Navigating to match page (attempt 1)
🔍 Enhanced team extraction with validation...
   Trying method 1: extract_teams_dynamic
🔍 Extracting team names dynamically...
✅ Teams extracted from URL pattern 1: XI Esport vs Prestige Prestige
✅ Teams fixed from URL: XI Esport vs Prestige Prestige
✅ Team names validated: XI Esport vs Prestige Prestige
   ✅ Method 1 succeeded and passed validation
✅ Teams: XI Esport vs Prestige Prestige
📊 Extracting core team statistics...
🔍 Extracting rankings dynamically...
✅ Found rankings: [326, 153]
🔍 Extracting ENSI scores dynamically...
✅ Found ENSI scores: [1287, 1428]
🔍 Extracting win rates with enhanced accuracy and correct team mapping...
   Method 1: Teams Comparison section analysis (DOM-based)
   Found Teams Comparison section
   ✅ Extracted Winrate 10: 40% (Team1) vs 60% (Team2)
   ✅ Extracted Winrate 30: 27% (Team1) vs 58% (Team2)
🔍 Extracting current shape with correct team ordering...
   Found Current Shape section
   ✅ Extracted Current Shape: 113.0% (Team1) vs 102.0% (Team2)
✅ Found shapes: [113.0, 102.0]
   XI Esport: 40.0% (10 games) / 27.0% (30 games)
   Prestige Prestige: 60.0% (10 games) / 58.0% (30 games)
🔍 Extracting head-to-head data from Previous Encounters...
📜 Scrolling to Previous Encounters section...
✅ Found Previous Encounters section
✅ Found win percentages: 0% vs 100%
🔍 Parsing: 'Wins of XI: 0' -> Team: 'XI', Wins: 0
✅ Team 1: XI has 0 wins
✅ Extracted draws: 0 (EXCLUDED from competitive analysis)
🔍 Parsing: 'Wins of Prestige: 1' -> Team: 'Prestige', Wins: 1
✅ Team 2: Prestige has 1 wins
📊 H2H Summary: 1 competitive games (0 draws excluded)
✅ Found match: : Prestige 2:0 Prestige
✅ Head-to-head extraction successful! Total: 1 matches
🔍 Extracting players from cs-player-comp__player blocks...
🔍 Found 10 cs-player-comp__player elements
✅ Team1 Player #1: Skejs (K/D: 1.0) Denmark
✅ Team1 Player #2: Stesso (K/D: 0.94) Denmark
✅ Team1 Player #3: Few (K/D: 0.79) Denmark
✅ Team1 Player #4: Kragh (K/D: 0.64) Denmark
⚠️ Could not find/parse K/D for Sinzey: could not convert string to float: 'n/a'
✅ Team1 Player #5: Sinzey (K/D: 1.0) Denmark
✅ Team2 Player #1: Mol011 (K/D: 1.04) Denmark
✅ Team2 Player #2: Mizi (K/D: 1.0) Denmark
✅ Team2 Player #3: NickyB (K/D: 0.48) Denmark
⚠️ Could not find/parse K/D for Folke: could not convert string to float: 'n/a'
✅ Team2 Player #4: Folke (K/D: 1.0) Denmark
⚠️ Could not find/parse K/D for GA1De: could not convert string to float: 'n/a'
✅ Team2 Player #5: GA1De (K/D: 1.0) Denmark
📊 Team1 average K/D: 0.87
📊 Team2 average K/D: 0.9
🎯 EXTRACTION COMPLETE: Team1=5, Team2=5
📊 Extracting additional betting-relevant data...
🎯 Match Format: BO3 (explicit pattern: 'bo3')
🏆 Tournament Tier: Tier-3
🏅 Tournament: 2025 United21 Season 33
📈 H2H Matches Found: 4
👥 Roster Stability: 80/100
🏆 Extracting enhanced tournament context...
❌ Enhanced tournament context extraction failed: no such group
📈 Extracting teams recent performance...
✅ Found Teams Recent Performance section
   ⚠️ Filtered 12 low-quality Team1 opponent names
   ⚠️ Filtered 10 low-quality Team2 opponent names
✅ Team1 recent form: 13W-5L from 18 matches
✅ Team2 recent form: 6W-10L from 16 matches
   Team1 opponents: Vol, Pre, Pre...
   Team2 opponents: Ast, Pre, Gen...
🤝 Extracting common opponents analysis...
✅ Found common opponents section: 'common opponents'
ℹ️ Common opponents section found but no container located
🔍 Trying alternative extraction: searching for specific HTML elements...
✅ Found advantage data: [('XI', '3'), ('Prestige', '3')]
✅ Found opponent: Prestige
✅ Found opponent: XI Esport
✅ Found opponent: Ex-Astralis Talent
✅ Found opponent: Ex-Astra
✅ Found opponent: Volt
✅ Found opponent: Brute
✅ Found opponent: GenOne
✅ Found opponent: GOne
✅ Found opponent: HEROIC Academy
✅ Found opponent: Hero.A
✅ Found opponent: KS Esports
✅ Found opponent: ESC Gaming
✅ Found opponent: ESC
✅ Found 13 common opponents with real data
⚠️ Could not match advantage team 'XI' to main teams [, ]
⚠️ Could not match advantage team 'Prestige' to main teams [, ]
⚠️ Found opponents but no valid advantage numbers - skipping common opponents
🗺️ Extracting map statistics and preferences...
ℹ️ No specific map data found
📈 Extracting extended team form data...
💰 Extracting betting context data...
✅ Found betting section: 'betting.*odds'
✅ Found betting data: 11 values
📊 GENERATING ENHANCED PREDICTION FROM COMPREHENSIVE DATA
======================================================================
💪 XI Esport: 26.9/100
💪 Prestige Prestige: 32.2/100
🗺️ ANALYZING MAP POOL AND VETO PATTERNS...
🗺️ Predicted first map: mirage
👤 ANALYZING PLAYER FORM AND KEY MATCHUPS...
🎰 ANALYZING REAL BETTING MARKETS WITH MAP DATA...
🎯 Analyzing REAL betting markets available on bookmakers...
🥇 Analyzing Match Winner (Moneyline)...
🗺️ Analyzing Map Handicap...
📊 Analyzing Total Maps...
🎯 Analyzing Correct Score...
🗺️ Analyzing First Map Winner...
🔢 Analyzing First Map Total Rounds...
👤 Analyzing Player Props...
⏰ Analyzing Match Duration...
📊 Using fallback confidence calculation: 58.04808333333334%
🔍 Roster Analysis:
   XI Esport roster era: current
   Prestige Prestige roster era: current
   ✅ Current rosters - H2H impact maintained at 0.70
   📈 H2H supports prediction: +10.5 confidence
   🎯 Final confidence: 58.0 → 68.5
🔍 Roster Analysis:
   XI Esport roster era: current
   Prestige Prestige roster era: current
   ✅ Current rosters - H2H impact maintained at 0.70
✅ Data quality score: 100/100
🎯 Final prediction: Prestige Prestige (68.5%)
🗺️ Map statistics enhancement ENABLED - integrating map data...
   👥 Team Mapping: Team1=XI Esport, Team2=Prestige Prestige
   ❌ Dust2/map statistics functionality disabled
🗺️ Map statistics enhancement completed
   🔍 Checking Prestige Prestige confidence: 68.5% vs threshold 70.0%
✅ Qualifying bet found: MAP_HANDICAP (75%)
🔍 VALIDATING CS2 mechanics for betting recommendations...
✅ CS2 betting mechanics validation PASSED
🚨 CS2 MECHANICS VALIDATION FAILED - Applying corrections...
🎯 APPLYING CORRECTED BETTING LOGIC WITH MAP POOL ANALYSIS
   🔍 Analyzing XI Esport vs Prestige Prestige
   ❌ Bo3.GG map analysis disabled - using statistical prediction only
   ⚠️ No real map data available - using statistical prediction only
🚫 REFUSING hardcoded map predictions - real data only policy enforced
🔍 VALIDATING CS2 mechanics for betting recommendations...
✅ CS2 betting mechanics validation PASSED
✅ Enhanced prediction: Prestige Prestige (69%)
🔍 ANALYZING PORTFOLIO with 1 predictions
🔍 EXTRACTING OPPORTUNITIES from 1 predictions
   Processing: XI Esport vs Prestige Prestige
      📊 Found betting markets with 8 markets
   ❌ Confidence 58.04808333333334% below dynamic threshold 60.0% (data quality: 50)
⚠️ Invalid or missing odds (0) - cannot calculate expected value
         ✅ Added: Map Handicap - XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)
⚠️ Invalid or missing odds (0) - cannot calculate expected value
         ✅ Added: Total Maps - OVER 2.5 maps (3 maps likely)
   ❌ Confidence 55% below dynamic threshold 60.0% (data quality: 50)
   ❌ Confidence 50% below dynamic threshold 60.0% (data quality: 50)
   ❌ Confidence 0% below dynamic threshold 60.0% (data quality: 50)
   🚫 PLAYER PROPS DISABLED: Skipping Close matchup: Skejs vs Mol011
   ❌ Confidence 0% below dynamic threshold 60.0% (data quality: 50)
🎯 Total opportunities extracted: 2
   ✅ Standard bet accepted: Map Handicap for XI Esport vs Prestige Prestige (74.63461111111111% confidence, 0.4926922222222223/10 value)
   ✅ PROVEN STRATEGY total maps OVER accepted: XI Esport vs Prestige Prestige (72.63461111111111% confidence)
🔍 ENHANCED filtering: 2 → 2 opportunities
🔗 Calculating bet correlations...
📊 Portfolio generated: 2 opportunities
🎯 ENHANCED ANALYSIS COMPLETE
⏱️ Processing time: 20.6s
✅ Success rate: 100.0%
📊 Qualifying predictions: 1
💾 Phase 3: Enhanced Results Export
--------------------------------------------------
🔍 Detected format: BO3 for URL: https://ensigame.com/matches/cs-2/1375305-xi-esport-xi-vs-prestige-prestige-epl-s28-24-06-25
📊 Team Strength Analysis:
   XI Esport: Rank #326, ENSI 1287
   Prestige Prestige: Rank #153, ENSI 1428
   🎯 Current Prediction: Prestige Prestige
🗺️ Attempting to scrape live map vetos from: https://ensigame.com/matches/cs-2/1375305-xi-esport-xi-vs-prestige-prestige-epl-s28-24-06-25
   🔍 Analyzing ensigame page structure...
   📅 UPCOMING MATCH DETECTED - Not live yet
   🔄 Trying text pattern matching...
   ❌ Could not detect map vetos
   ⚠️ No live veto data available - refusing to predict maps
   ⏳ UPCOMING MATCH: Map will be determined in veto phase
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
📅 UPCOMING MATCH - No explicit live indicators detected
   🎯 Stored 7 betting markets in prediction data for portfolio analysis
   🎯 Stored 7 betting markets for portfolio analysis
🗺️ Attempting to scrape live map vetos from: https://ensigame.com/matches/cs-2/1375305-xi-esport-xi-vs-prestige-prestige-epl-s28-24-06-25
🗺️ Using cached veto data for: https://ensigame.com/matches/cs-2/1375305-xi-esport-xi-vs-prestige-prestige-epl-s28-24-06-25
   ⚠️ No live veto data available - refusing to predict maps
   ⏳ UPCOMING MATCH: Map will be determined in veto phase
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
🗺️ ENHANCED MAP STATISTICS ANALYSIS for XI Esport vs Prestige Prestige
🗺️ ENHANCED MAP STATISTICS ANALYSIS for XI Esport vs Prestige Prestige
🌪️ Scraping dust2.in for XI Esport vs Prestige Prestige
🔍 Found 6 unique matches on dust2.in
❌ Could not find dust2.in URL for XI Esport vs Prestige Prestige
   ❌ No real map statistics available - refusing to use fallback data
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
📅 UPCOMING MATCH - No explicit live indicators detected
📅 UPCOMING MATCH - No explicit live indicators detected
🎯 GENERATING MARKET-AGNOSTIC BETTING RECOMMENDATIONS (min confidence: 60.0%)
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   ✅ MONEYLINE: Unknown (61.5%)
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   ✅ TEAM TOTAL ROUNDS: Prestige Prestige OVER 10.5 rounds on First Map (62.5%)
   ⚙️ PLAYER PROPS: Disabled (use --enable-props to analyze)
   🎲 Upset Factor: Lower-tier match (avg rank: 239.5)
   ⚠️ Upset potential: 1/5 factors = 3.0% confidence reduction
   🏆 TOP RECOMMENDATIONS: 2 markets ranked by composite score
💾 ENHANCED RESULTS SAVED:
   📊 Analysis: logs/enhanced_batch_analysis_20250623_151451.json
   📋 Report: logs/enhanced_batch_report_20250623_151451.txt
   💼 Portfolio: logs/enhanced_portfolio_report_20250623_151451.txt
🔧 RUNNING COMPREHENSIVE DATA VALIDATION...
🔍 VALIDATING BETTING CONSISTENCY ACROSS ALL OUTPUTS...
📊 VALIDATION REPORT:
   Total matches validated: 1
   Successful validations: 1
   Total consistency issues: 0
   Total Rounds inconsistencies: 0
   Confidence discrepancies: 0
   ✅ Success rate: 100.0%
✅ NO CONSISTENCY ISSUES FOUND - All outputs match perfectly!
✅ VALIDATION COMPLETE - All 3 files are now consistent!
✅ Loaded 5 historical predictions
✅ Loaded 1170 pending predictions

DevTools listening on ws://127.0.0.1:26820/devtools/browser/03e0ee4c-451c-4714-b451-83ffb3265574
✅ Result collector driver initialized
📝 Saved prediction for tracking: Prestige Prestige
📝 Saved prediction for tracking: 🎯 HIGH VALUE: XI Esport to WIN AT LEAST ONE MAP (close match)
📝 Saved prediction for tracking: 🎯 RECOMMENDED: OVER 2.5 maps (close series expected)
📝 Saved prediction for tracking: 🎯 SAFE BET: XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)
📝 Saved prediction for tracking: 📊 Prestige Prestige -2.5 rounds (must win by 3+ rounds)
📝 Saved prediction for tracking: 📊 XI Esport UNDER 8.5 rounds (facing stronger opponent)
📝 Saved prediction for tracking: 📊 Prestige Prestige OVER 8.5 rounds (slight advantage)
🧠 Saved 1 predictions for ML learning
🧠 Predictions saved for ML learning
🎯 ENHANCED ANALYSIS COMPLETE!
==================================================
✅ Processed: 1/1 matches
📊 Success Rate: 100.0%
⏱️ Processing Time: 20.6s
💰 Portfolio Value: 100.00
🚀 Average Confidence: 68.5%
⭐ Premium Bets: 0
📁 Check the logs/ folder for detailed reports!
🧹 Performing final cleanup...
🧹 Cleaning up Chrome driver...
✅ Chrome driver cleaned up successfully
🧹 EMERGENCY CLEANUP: Terminating all Chrome processes...
🔪 Killing Chrome process: chrome.exe (PID: 648)
🔪 Killing Chrome process: chrome.exe (PID: 4168)
🔪 Killing Chrome process: chromedriver.exe (PID: 26872)
🧹 Killed 3 Chrome processes
✅ Emergency cleanup completed
✅ Cleanup completed - all Chrome processes should be terminated
🧹 EMERGENCY CLEANUP: Terminating all Chrome processes...
✅ No lingering Chrome processes found
✅ Emergency cleanup completed
PS J:\cs2-bet-sonnet-scrap>
PS J:\cs2-bet-sonnet-scrap>