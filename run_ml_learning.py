#!/usr/bin/env python3
"""
AUTOMATED ML LEARNING RUNNER
============================
Runs the ML result collection system to automatically learn from match results.
Can be run manually or scheduled to run automatically.
"""

import time
import schedule
from prediction_calibration import PredictionCalibrator
from match_result_collector import MatchResultCollector

def run_result_check():
    """Run a single result check cycle"""
    
    print("🔄 Starting ML result collection cycle...")
    
    try:
        # Initialize systems
        calibrator = PredictionCalibrator()
        collector = MatchResultCollector(calibrator)
        
        # Check for new results
        collector.check_match_results()
        
        # Generate updated accuracy report
        if len(calibrator.prediction_history) > 0:
            report = calibrator.get_accuracy_report()
            print(f"📊 Current ML Stats:")
            print(f"   Total Predictions: {report['total_predictions']}")
            print(f"   Overall Accuracy: {report['overall_accuracy']*100:.1f}%")
            print(f"   Recent Accuracy: {report['recent_accuracy']*100:.1f}%")
            print(f"   Calibration Quality: {report['calibration_quality']}")
        
        # Cleanup
        collector.cleanup()
        
        print("✅ ML learning cycle completed")
        
    except Exception as e:
        print(f"⚠️ Error in ML learning cycle: {e}")

def run_continuous_learning():
    """Run continuous ML learning with scheduling"""
    
    print("🚀 STARTING CONTINUOUS ML LEARNING SYSTEM")
    print("=" * 50)
    print("🔄 Will check for match results every 6 hours")
    print("🧠 System will automatically learn and improve")
    print("⏹️ Press Ctrl+C to stop")
    print()
    
    # Schedule result checks every 6 hours
    schedule.every(6).hours.do(run_result_check)
    
    # Run initial check
    run_result_check()
    
    # Keep running scheduled checks
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute for scheduled tasks
            
    except KeyboardInterrupt:
        print("\n🛑 ML learning system stopped by user")
    except Exception as e:
        print(f"\n⚠️ ML learning system error: {e}")

def run_manual_check():
    """Run a single manual check"""
    
    print("🔍 MANUAL ML RESULT CHECK")
    print("=" * 30)
    
    run_result_check()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        # Run continuous learning
        run_continuous_learning()
    else:
        # Run single manual check
        run_manual_check()
        
        print("\n💡 USAGE OPTIONS:")
        print("   python run_ml_learning.py           # Single check")
        print("   python run_ml_learning.py --continuous  # Continuous learning")
        print()
        print("🔄 For automatic learning, run with --continuous")
        print("📊 Results will be saved to prediction_results.json")
        print("🧠 System will improve predictions over time")
