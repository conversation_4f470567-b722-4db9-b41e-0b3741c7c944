#!/usr/bin/env python3
"""
ADVANCED PREDICTION CALIBRATION SYSTEM
=====================================
Tracks prediction accuracy over time and calibrates confidence levels
using machine learning techniques for maximum accuracy.
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import os

@dataclass
class PredictionResult:
    """Store prediction results for calibration"""
    match_id: str
    prediction: str
    confidence: float
    actual_result: str
    correct: bool
    timestamp: datetime
    market_type: str
    tournament_tier: str
    team1_rank: int
    team2_rank: int

class PredictionCalibrator:
    """ADVANCED: Machine learning-based prediction calibration system"""
    
    def __init__(self, results_file: str = "prediction_results.json"):
        self.results_file = results_file
        self.prediction_history: List[PredictionResult] = []
        self.calibration_data = {}
        self.load_historical_results()
    
    def load_historical_results(self):
        """Load historical prediction results"""
        try:
            if os.path.exists(self.results_file):
                with open(self.results_file, 'r') as f:
                    data = json.load(f)
                    for item in data:
                        result = PredictionResult(
                            match_id=item['match_id'],
                            prediction=item['prediction'],
                            confidence=item['confidence'],
                            actual_result=item['actual_result'],
                            correct=item['correct'],
                            timestamp=datetime.fromisoformat(item['timestamp']),
                            market_type=item['market_type'],
                            tournament_tier=item['tournament_tier'],
                            team1_rank=item['team1_rank'],
                            team2_rank=item['team2_rank']
                        )
                        self.prediction_history.append(result)
                print(f"✅ Loaded {len(self.prediction_history)} historical predictions")
        except Exception as e:
            print(f"⚠️ Failed to load historical results: {e}")
    
    def save_prediction_result(self, result: PredictionResult):
        """Save a new prediction result"""
        self.prediction_history.append(result)
        
        # Save to file
        try:
            data = []
            for r in self.prediction_history:
                data.append({
                    'match_id': r.match_id,
                    'prediction': r.prediction,
                    'confidence': r.confidence,
                    'actual_result': r.actual_result,
                    'correct': r.correct,
                    'timestamp': r.timestamp.isoformat(),
                    'market_type': r.market_type,
                    'tournament_tier': r.tournament_tier,
                    'team1_rank': r.team1_rank,
                    'team2_rank': r.team2_rank
                })
            
            with open(self.results_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            print(f"⚠️ Failed to save prediction result: {e}")
    
    def calibrate_confidence(self, raw_confidence: float, market_type: str, 
                           tournament_tier: str, rank_diff: int) -> float:
        """ADVANCED: Calibrate confidence based on historical accuracy"""
        
        if len(self.prediction_history) < 10:
            return raw_confidence  # Not enough data for calibration
        
        # Filter relevant historical data
        relevant_predictions = self.filter_relevant_predictions(
            market_type, tournament_tier, rank_diff
        )
        
        if len(relevant_predictions) < 5:
            return raw_confidence  # Not enough relevant data
        
        # Calculate calibration adjustment
        calibration_factor = self.calculate_calibration_factor(
            relevant_predictions, raw_confidence
        )
        
        # Apply calibration
        calibrated_confidence = raw_confidence * calibration_factor
        
        # Ensure reasonable bounds
        return max(50, min(95, calibrated_confidence))
    
    def filter_relevant_predictions(self, market_type: str, tournament_tier: str, 
                                  rank_diff: int) -> List[PredictionResult]:
        """Filter predictions similar to current context"""
        
        relevant = []
        cutoff_date = datetime.now() - timedelta(days=90)  # Last 90 days
        
        for pred in self.prediction_history:
            if pred.timestamp < cutoff_date:
                continue
                
            # Market type match
            if pred.market_type != market_type:
                continue
            
            # Tournament tier match
            if pred.tournament_tier != tournament_tier:
                continue
            
            # Similar ranking difference (within 10 ranks)
            pred_rank_diff = abs(pred.team1_rank - pred.team2_rank)
            if abs(pred_rank_diff - rank_diff) > 10:
                continue
            
            relevant.append(pred)
        
        return relevant
    
    def calculate_calibration_factor(self, relevant_predictions: List[PredictionResult], 
                                   target_confidence: float) -> float:
        """Calculate how to adjust confidence based on historical accuracy"""
        
        # Group predictions by confidence ranges
        confidence_ranges = [
            (50, 60), (60, 70), (70, 80), (80, 90), (90, 100)
        ]
        
        target_range = None
        for min_conf, max_conf in confidence_ranges:
            if min_conf <= target_confidence < max_conf:
                target_range = (min_conf, max_conf)
                break
        
        if not target_range:
            return 1.0  # No adjustment
        
        # Find predictions in this confidence range
        range_predictions = [
            p for p in relevant_predictions 
            if target_range[0] <= p.confidence < target_range[1]
        ]
        
        if len(range_predictions) < 3:
            return 1.0  # Not enough data
        
        # Calculate actual accuracy in this range
        correct_predictions = sum(1 for p in range_predictions if p.correct)
        actual_accuracy = correct_predictions / len(range_predictions)
        expected_accuracy = (target_range[0] + target_range[1]) / 200  # Convert to 0-1
        
        # Calculate calibration factor
        if actual_accuracy > expected_accuracy:
            # We're underconfident, increase confidence
            calibration_factor = min(1.2, actual_accuracy / expected_accuracy)
        else:
            # We're overconfident, decrease confidence
            calibration_factor = max(0.8, actual_accuracy / expected_accuracy)
        
        return calibration_factor
    
    def get_accuracy_report(self) -> Dict:
        """Generate comprehensive accuracy report"""
        
        if not self.prediction_history:
            return {'error': 'No historical data available'}
        
        total_predictions = len(self.prediction_history)
        correct_predictions = sum(1 for p in self.prediction_history if p.correct)
        overall_accuracy = correct_predictions / total_predictions
        
        # Accuracy by market type
        market_accuracy = {}
        for market in ['match_winner', 'map_handicap', 'total_maps', 'total_rounds']:
            market_preds = [p for p in self.prediction_history if p.market_type == market]
            if market_preds:
                market_correct = sum(1 for p in market_preds if p.correct)
                market_accuracy[market] = market_correct / len(market_preds)
        
        # Accuracy by confidence range
        confidence_accuracy = {}
        ranges = [(50, 60), (60, 70), (70, 80), (80, 90), (90, 100)]
        for min_conf, max_conf in ranges:
            range_preds = [
                p for p in self.prediction_history 
                if min_conf <= p.confidence < max_conf
            ]
            if range_preds:
                range_correct = sum(1 for p in range_preds if p.correct)
                confidence_accuracy[f"{min_conf}-{max_conf}%"] = range_correct / len(range_preds)
        
        # Recent performance (last 30 days)
        recent_cutoff = datetime.now() - timedelta(days=30)
        recent_preds = [p for p in self.prediction_history if p.timestamp > recent_cutoff]
        recent_accuracy = 0
        if recent_preds:
            recent_correct = sum(1 for p in recent_preds if p.correct)
            recent_accuracy = recent_correct / len(recent_preds)
        
        return {
            'total_predictions': total_predictions,
            'overall_accuracy': overall_accuracy,
            'recent_accuracy': recent_accuracy,
            'market_accuracy': market_accuracy,
            'confidence_accuracy': confidence_accuracy,
            'calibration_quality': self.assess_calibration_quality()
        }
    
    def assess_calibration_quality(self) -> str:
        """Assess how well-calibrated our confidence levels are"""
        
        if len(self.prediction_history) < 20:
            return "INSUFFICIENT_DATA"
        
        # Check if confidence levels match actual accuracy
        ranges = [(50, 60), (60, 70), (70, 80), (80, 90), (90, 100)]
        calibration_errors = []
        
        for min_conf, max_conf in ranges:
            range_preds = [
                p for p in self.prediction_history 
                if min_conf <= p.confidence < max_conf
            ]
            
            if len(range_preds) >= 5:
                expected_accuracy = (min_conf + max_conf) / 200
                actual_accuracy = sum(1 for p in range_preds if p.correct) / len(range_preds)
                error = abs(expected_accuracy - actual_accuracy)
                calibration_errors.append(error)
        
        if not calibration_errors:
            return "INSUFFICIENT_DATA"
        
        avg_error = sum(calibration_errors) / len(calibration_errors)
        
        if avg_error < 0.05:
            return "EXCELLENT"
        elif avg_error < 0.10:
            return "GOOD"
        elif avg_error < 0.15:
            return "FAIR"
        else:
            return "POOR"
