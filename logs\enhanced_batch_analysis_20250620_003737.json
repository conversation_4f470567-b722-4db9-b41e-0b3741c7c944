{"timestamp": "20250620_003737", "total_matches": 1, "successful_scrapes": 1, "failed_urls": [], "success_rate": 100.0, "processing_time": 20.***************, "min_confidence": 60.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "FaZe Clan", "ranking": 7, "ensi_score": 1893, "winrate_10": 60.0, "winrate_30": 53.0, "current_shape": 107.0, "avg_kd": 1.03, "players": [{"name": "s1mple", "nationality": "", "kd_ratio": 1.17}, {"name": "EliGE", "nationality": "United States", "kd_ratio": 1.16}, {"name": "rain", "nationality": "Norway", "kd_ratio": 1.08}, {"name": "frozen", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.75}]}, "team2": {"name": "THE Mongolz Mongolz", "ranking": 5, "ensi_score": 1946, "winrate_10": 60.0, "winrate_30": 57.0, "current_shape": 103.0, "avg_kd": 1.13, "players": [{"name": "senzu", "nationality": "", "kd_ratio": 1.25}, {"name": "910", "nationality": "", "kd_ratio": 1.21}, {"name": "m<PERSON>ho", "nationality": "", "kd_ratio": 1.13}, {"name": "bLitz", "nationality": "", "kd_ratio": 1.08}, {"name": "Techno4k", "nationality": "", "kd_ratio": 0.98}]}, "h2h_record": "FaZe: 1 - Draws: 0 - Mongolz: 1 (50% vs 50%)", "prediction": "THE Mongolz Mongolz", "confidence": 62.**************, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - FaZe Clan +1.5 maps (avoid 0-2 loss) (71.**************% confidence) | Alternative: MATCH_WINNER (62.**************%)", "key_factors": ["🆚 H2H: Even record (1-1)", "📈 ENSI advantage: THE Mongolz Mongolz (1893 vs 1946)"], "additional_factors": {"h2h_data": {"previous_encounters": 2, "h2h_record": "FaZe: 1 - Draws: 0 - Mongolz: 1 (50% vs 50%)", "team1_wins": 1, "team2_wins": 1, "draws": 0, "recent_matches": [": The Mongolz 0:2 Mongolz", ": FaZe Clan 0:2 FaZe"], "team1_name": "FaZe", "team2_name": "Mongolz", "team1_win_percentage": 50, "team2_win_percentage": 50, "competitive_encounters": 2, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-1", "tournament": "2025 BLAST.tv Austin Major", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.17, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "s1mple", "nationality": "", "kd_ratio": 1.17}, {"name": "EliGE", "nationality": "United States", "kd_ratio": 1.16}, {"name": "rain", "nationality": "Norway", "kd_ratio": 1.08}, {"name": "frozen", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.75}], "team2_players": [{"name": "senzu", "nationality": "", "kd_ratio": 1.25}, {"name": "910", "nationality": "", "kd_ratio": 1.21}, {"name": "m<PERSON>ho", "nationality": "", "kd_ratio": 1.13}, {"name": "bLitz", "nationality": "", "kd_ratio": 1.08}, {"name": "Techno4k", "nationality": "", "kd_ratio": 0.98}], "team1_avg_kd": 1.03, "team2_avg_kd": 1.13}, "recent_performance": {"team1_recent_matches": [{"score": "0:2", "result": "L", "opponent": "FaZe Clan\nFaZe\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "The Mongolz\nMongolz\n\n\n\n\n\n\n\n\n\n\n\n            All FaZe and Mongolz Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "FaZe Clan\nFaZe\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "FaZe Clan\nFaZe\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Made in Brazil\nMIBR\n\n\n\n\n\n\n\n\n\n\n                    All FaZe Encounters\n                \n\n\n\n\nThe Mongolz\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:0", "result": "W", "opponent": "16\n\n\n\n\n\n\nThe Mongolz\nMongolz", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "46\n\n\n\n\n\n\nFaZe Clan\nFaZe", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "56\n\n\n\n\n\n\nLegacy\nLegacy", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "30\n\n\n\n\n\n\nMOUZ\nMOUZ", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "37\n\n\n\n\n\n\nAurora Gaming\nAurora", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 11, "losses": 9}, "team2_recent_form": {"wins": 10, "losses": 9}, "team1_opponents": ["FaZ", "The", "FaZ", "FaZ", "Mad", "<PERSON><PERSON>", "Tea", "FUR", "The", "FaZ", "FUR", "Mad", "FaZ", "FaZ", "Mad", "<PERSON><PERSON>", "Tea", "FUR"], "team2_opponents": ["The", "FaZ", "Leg", "MOU", "<PERSON><PERSON>", "FaZ", "The", "The", "The"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["The Mongolz", "Mongolz", "FaZe Clan", "FaZe", "MOUZ", "Aurora Gaming", "Aurora", "Team Liquid", "Liquid", "Made in Brazil", "MIBR", "Legacy", "G2 Esports", "Lynn Vision Gaming", "LVG", "FURIA Esports", "FURIA"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"s1mple": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 117.0}, "EliGE": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "rain": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "frozen": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Karrigan": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 75.0}}, "team2_form_trends": {"senzu": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 125.0}, "910": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 121.0}, "mzinho": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "bLitz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 108.0}, "Techno4k": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON>", "player1_kd": 0.75, "player2": "Techno4k", "player2_kd": 0.98, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON> vs Techno4k"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"senzu": {"team": "THE Mongolz Mongolz", "kd_ratio": 1.25, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "910": {"team": "THE Mongolz Mongolz", "kd_ratio": 1.21, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "THE Mongolz Mongolz to win match", "confidence": 62.**************, "reasoning": ["Team strength difference: 8.1", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "FaZe Clan +1.5 maps (avoid 0-2 loss)", "confidence": 71.**************, "reasoning": ["Based on team strength difference: 8.1"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 8.1 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: s1<PERSON> vs senzu", "confidence": 55, "reasoning": ["K/D comparison: 1.17 vs 1.25"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1369181-faze-clan-faze-vs-the-mongolz-mongolz-blast-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:37:37.481105"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 150.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 90, "recommendations": [{"match": "FaZe Clan vs THE Mongolz Mongolz", "bet_type": "Map Handicap", "recommendation": "FaZe Clan +1.5 maps (avoid 0-2 loss)", "confidence": 71.**************, "value_rating": 0.*****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 8.1"]}, {"match": "FaZe Clan vs THE Mongolz Mongolz", "bet_type": "Moneyline", "recommendation": "THE Mongolz Mongolz to win match", "confidence": 62.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 8.1", "Primary betting market"]}, {"match": "FaZe Clan vs THE Mongolz Mongolz", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 8.1 difference"]}]}, "enhancement_stats": {"total_predictions": 1, "average_confidence": 62.**************, "premium_bets": 0, "strong_bets": 0, "good_bets": 0, "lean_bets": 0, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 0.0, "good_pct": 0.0, "lean_pct": 0.0}}}