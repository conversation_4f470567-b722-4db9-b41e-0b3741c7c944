import streamlit as st
import pandas as pd
import re
import glob
import os
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime

# Configure Streamlit page
st.set_page_config(
    page_title="CS2 Betting Analysis Dashboard",
    page_icon="🎯",
    layout="wide"
)

def find_batch_report_files():
    """Find all batch report files in the logs folder."""
    logs_folder = "../logs"
    pattern = os.path.join(logs_folder, "*batch_report*.txt")
    files = glob.glob(pattern)
    return sorted(files, key=os.path.getmtime, reverse=True)

def parse_report_header(content):
    """Parse header information from batch report."""
    header = {}
    
    # Extract basic stats
    total_matches = re.search(r'📊 Total Matches Analyzed: (\d+)', content)
    success_rate = re.search(r'📈 Success Rate: ([\d.]+)%', content)
    generated = re.search(r'⏰ Generated: ([^\n]+)', content)
    bankroll = re.search(r'💰 Bankroll: \$([^\n]+)', content)
    
    # Extract enhancement stats
    avg_confidence = re.search(r'Average Confidence: ([\d.]+)%', content)
    premium_bets = re.search(r'Premium Bets \(85%\+\): (\d+)', content)
    strong_bets = re.search(r'Strong Bets \(78-84%\): (\d+)', content)
    good_bets = re.search(r'Good Bets \(70-77%\): (\d+)', content)
    
    header = {
        'total_matches': int(total_matches.group(1)) if total_matches else 0,
        'success_rate': float(success_rate.group(1)) if success_rate else 0,
        'generated': generated.group(1) if generated else '',
        'bankroll': bankroll.group(1) if bankroll else '0',
        'avg_confidence': float(avg_confidence.group(1)) if avg_confidence else 0,
        'premium_bets': int(premium_bets.group(1)) if premium_bets else 0,
        'strong_bets': int(strong_bets.group(1)) if strong_bets else 0,
        'good_bets': int(good_bets.group(1)) if good_bets else 0
    }
    
    return header

def parse_matches(content):
    """Parse matches from batch report."""
    matches = []
    
    # Find all match sections
    match_pattern = r'(🥇|🥈|🥉|📊|💰) MATCH \d+: ([^-]+) vs ([^-]+) - ([^\n]+)'
    match_sections = re.split(match_pattern, content)
    
    for i in range(1, len(match_sections), 5):  # Every 5 elements is one match
        if i + 4 < len(match_sections):
            category_emoji = match_sections[i]
            team1 = match_sections[i + 1].strip()
            team2 = match_sections[i + 2].strip()
            category = match_sections[i + 3].strip()
            section_content = match_sections[i + 4]
            
            # Extract additional match data
            rankings = re.search(r'🏅 Rankings: #(\d+) vs #(\d+)', section_content)
            confidence = re.search(r'📊 Confidence: ([\d.]+)%', section_content)
            prediction = re.search(r'🎯 Primary Prediction: ([^\n]+)', section_content)
            
            match_data = {
                'category_emoji': category_emoji,
                'team1': team1,
                'team2': team2,
                'category': category,
                'team1_rank': int(rankings.group(1)) if rankings else None,
                'team2_rank': int(rankings.group(2)) if rankings else None,
                'confidence': float(confidence.group(1)) if confidence else 0,
                'predicted_winner': prediction.group(1).strip() if prediction else 'Unknown'
            }
            
            matches.append(match_data)
    
    return matches

def main():
    st.title("🎯 CS2 Betting Analysis Dashboard")
    
    # Find all batch report files
    files = find_batch_report_files()
    
    if not files:
        st.error("No batch report files found in the logs folder!")
        st.info("Make sure batch report files exist in the '../logs' directory with 'batch_report' in the filename.")
        return
    
    # Sidebar for file selection
    st.sidebar.header("📁 Select Report File")
    file_names = [os.path.basename(f) for f in files]
    selected_file_idx = st.sidebar.selectbox(
        "Choose a report:",
        range(len(files)),
        format_func=lambda i: file_names[i]
    )
    
    selected_file = files[selected_file_idx]
    
    # Load and parse the selected file
    try:
        with open(selected_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        header = parse_report_header(content)
        matches = parse_matches(content)
        
        # Display header metrics
        st.header("📊 Report Summary")
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Matches", header['total_matches'])
        with col2:
            st.metric("Success Rate", f"{header['success_rate']:.1f}%")
        with col3:
            st.metric("Avg Confidence", f"{header['avg_confidence']:.1f}%")
        with col4:
            st.metric("Bankroll", f"${header['bankroll']}")
        
        # Bet distribution
        st.subheader("🎲 Bet Distribution")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("🥇 Premium Bets (85%+)", header['premium_bets'])
        with col2:
            st.metric("🥈 Strong Bets (78-84%)", header['strong_bets'])
        with col3:
            st.metric("🥉 Good Bets (70-77%)", header['good_bets'])
        
        st.info(f"📅 Report Generated: {header['generated']}")
        
        # Display matches
        if matches:
            st.header("🏆 Match Predictions")
            
            # Create a DataFrame for better display
            df = pd.DataFrame(matches)
            
            # Confidence distribution chart
            if len(matches) > 1:
                fig = px.histogram(
                    df, 
                    x='confidence', 
                    nbins=10,
                    title="Confidence Distribution"
                )
                st.plotly_chart(fig, use_container_width=True)
            
            # Display each match
            for i, match in enumerate(matches):
                # Determine styling based on category
                if match['category_emoji'] == '🥇':
                    color = "red"
                    badge = "PREMIUM"
                elif match['category_emoji'] == '🥈':
                    color = "orange"
                    badge = "STRONG"
                elif match['category_emoji'] == '🥉':
                    color = "green"
                    badge = "GOOD"
                else:
                    color = "blue"
                    badge = "LEAN"
                
                with st.expander(f"{match['category_emoji']} {match['team1']} vs {match['team2']} - {badge}", expanded=i < 3):
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.write("**Team Rankings:**")
                        st.write(f"🥇 {match['team1']}: #{match['team1_rank'] or 'N/A'}")
                        st.write(f"🥈 {match['team2']}: #{match['team2_rank'] or 'N/A'}")
                    
                    with col2:
                        st.write("**Prediction:**")
                        st.write(f"Winner: **{match['predicted_winner']}**")
                        st.write(f"Confidence: **{match['confidence']:.1f}%**")
                    
                    with col3:
                        st.write("**Category:**")
                        st.write(f"Type: **{match['category']}**")
                        
                        # Color-coded confidence meter
                        confidence_color = "red" if match['confidence'] >= 85 else "orange" if match['confidence'] >= 78 else "green" if match['confidence'] >= 70 else "blue"
                        st.markdown(f"<div style='background-color: {confidence_color}; color: white; padding: 5px; border-radius: 5px; text-align: center;'>Confidence: {match['confidence']:.1f}%</div>", unsafe_allow_html=True)
        else:
            st.warning("No matches found in the selected report.")
            
    except Exception as e:
        st.error(f"Error loading file: {e}")
        st.info("Please check if the file format is correct.")
    
    # Analytics section
    if len(files) > 1:
        st.header("📈 Multi-Report Analytics")
        
        all_data = []
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                header = parse_report_header(content)
                header['filename'] = os.path.basename(file_path)
                all_data.append(header)
            except:
                continue
        
        if all_data:
            df_all = pd.DataFrame(all_data)
            
            # Performance over time
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=df_all['generated'],
                y=df_all['success_rate'],
                mode='lines+markers',
                name='Success Rate %'
            ))
            fig.add_trace(go.Scatter(
                x=df_all['generated'],
                y=df_all['avg_confidence'],
                mode='lines+markers',
                name='Avg Confidence %',
                yaxis='y2'
            ))
            
            fig.update_layout(
                title='Performance Trends Across Reports',
                xaxis_title='Report Date',
                yaxis_title='Success Rate %',
                yaxis2=dict(
                    title='Avg Confidence %',
                    overlaying='y',
                    side='right'
                )
            )
            st.plotly_chart(fig, use_container_width=True)
    
    # Footer
    st.markdown("---")
    st.markdown("**🤖 CS2 Betting Analysis System** - Automated betting predictions and analytics")

if __name__ == "__main__":
    main() 