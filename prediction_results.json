[{"match_id": "demo_match_1", "prediction": "Team A to win", "confidence": 75.0, "actual_result": "Team A won 2-1", "correct": true, "timestamp": "2025-06-10T00:31:17.323424", "market_type": "match_winner", "tournament_tier": "Tier-1", "team1_rank": 5, "team2_rank": 8}, {"match_id": "demo_match_2", "prediction": "Team B to win", "confidence": 80.0, "actual_result": "Team C won 2-0", "correct": false, "timestamp": "2025-06-12T00:31:17.323424", "market_type": "match_winner", "tournament_tier": "Tier-1", "team1_rank": 3, "team2_rank": 12}, {"match_id": "demo_match_3", "prediction": "OVER 2.5 maps", "confidence": 70.0, "actual_result": "3 maps played (2-1)", "correct": true, "timestamp": "2025-06-15T00:31:17.323424", "market_type": "total_maps", "tournament_tier": "Tier-1", "team1_rank": 7, "team2_rank": 9}, {"match_id": "demo_match_4", "prediction": "Team D +1.5 maps", "confidence": 65.0, "actual_result": "Team D lost 1-2 (covered +1.5)", "correct": true, "timestamp": "2025-06-17T00:31:17.323424", "market_type": "map_handicap", "tournament_tier": "Tier-2", "team1_rank": 15, "team2_rank": 4}, {"match_id": "demo_match_5", "prediction": "UNDER 21.5 rounds", "confidence": 85.0, "actual_result": "24 rounds played (went to overtime)", "correct": false, "timestamp": "2025-06-19T00:31:17.323424", "market_type": "total_rounds", "tournament_tier": "Tier-1", "team1_rank": 2, "team2_rank": 6}]