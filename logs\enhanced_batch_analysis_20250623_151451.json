{"timestamp": "20250623_151451", "total_matches": 1, "successful_scrapes": 1, "failed_urls": [], "success_rate": 100.0, "processing_time": 20.***************, "min_confidence": 70.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "XI Esport", "ranking": 326, "ensi_score": 1287, "winrate_10": 40.0, "winrate_30": 27.0, "current_shape": 113.0, "avg_kd": 0.87, "players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "Few", "nationality": "Denmark", "kd_ratio": 0.79}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.64}, {"name": "Sin<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}]}, "team2": {"name": "Prestige Prestige", "ranking": 153, "ensi_score": 1428, "winrate_10": 60.0, "winrate_30": 58.0, "current_shape": 102.0, "avg_kd": 0.9, "players": [{"name": "Mol011", "nationality": "Denmark", "kd_ratio": 1.04}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "NickyB", "nationality": "Denmark", "kd_ratio": 0.48}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "GA1De", "nationality": "Denmark", "kd_ratio": 1.0}]}, "h2h_record": "XI: 0 - Draws: 0 - Prestige: 1 (0% vs 100%)", "prediction": "Prestige Prestige", "confidence": 68.**************, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep) (74.**************% confidence) | Alternative: TOTAL_MAPS (72.**************%)", "key_factors": ["🆚 H2H record: Prestige Prestige leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: XI Esport (13W-5L vs 6W-10L)", "🏆 Ranking advantage: Prestige Prestige (#326 vs #153)", "📈 ENSI advantage: Prestige Prestige (1287 vs 1428)", "⚡ Better current shape: XI Esport (113.0% vs 102.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "XI: 0 - Draws: 0 - Prestige: 1 (0% vs 100%)", "team1_wins": 0, "team2_wins": 1, "draws": 0, "recent_matches": [": Prestige 2:0 Prestige"], "team1_name": "XI", "team2_name": "Prestige", "team1_win_percentage": 0, "team2_win_percentage": 100, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 United21 Season 33", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.94}, {"name": "Few", "nationality": "Denmark", "kd_ratio": 0.79}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.64}, {"name": "Sin<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}], "team2_players": [{"name": "Mol011", "nationality": "Denmark", "kd_ratio": 1.04}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "NickyB", "nationality": "Denmark", "kd_ratio": 0.48}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "GA1De", "nationality": "Denmark", "kd_ratio": 1.0}], "team1_avg_kd": 0.87, "team2_avg_kd": 0.9}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n            All XI and Prestige Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Volt\nVolt\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "55\n\n\n\n\n\n\nPrestige\nPrestige", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "03\n\n\n\n\n\n\nXI Esport\nXI", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "57\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "33\n\n\n\n\n\n\nXI Esport\nXI", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "A", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 13, "losses": 5}, "team2_recent_form": {"wins": 6, "losses": 10}, "team1_opponents": ["Vol", "Pre", "Pre", "Vol", "Pre", "Pre"], "team2_opponents": ["Ast", "Pre", "Gen", "Pre", "ESC", "Bru"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 13, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Prestige", "XI Esport", "Ex-<PERSON><PERSON><PERSON>", "Ex-<PERSON><PERSON>", "Volt", "Bru<PERSON>", "GenOne", "GOne", "HEROIC Academy", "Hero.A", "KS Esports", "ESC Gaming", "ESC"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Skejs": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Stesso": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "Few": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}, "Kragh": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 64.0}, "Sinzey": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"Mol011": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "Mizi": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "NickyB": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 48.0}, "Folke": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "GA1De": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON>", "player1_kd": 0.64, "player2": "NickyB", "player2_kd": 0.48, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs <PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Prestige Prestige to win match", "confidence": 58.04808333333334, "reasoning": ["Team strength difference: 5.4", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 74.**************, "reasoning": ["Based on team strength difference: 5.4"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 72.**************, "reasoning": ["Team strength analysis: 5.4 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON><PERSON> vs Mol011", "confidence": 55, "reasoning": ["K/D comparison: 1.00 vs 1.04"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375305-xi-esport-xi-vs-prestige-prestige-epl-s28-24-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-23 15:14:51.057083"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 100.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 65, "recommendations": [{"match": "XI Esport vs Prestige Prestige", "bet_type": "Map Handicap", "recommendation": "XI Esport +1.5 maps (wins AT LEAST 1 map - avoids 0-2 sweep)", "confidence": 74.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 5.4"]}, {"match": "XI Esport vs Prestige Prestige", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 72.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 5.4 difference"]}]}, "enhancement_stats": {"total_predictions": 1, "average_confidence": 68.**************, "premium_bets": 0, "strong_bets": 0, "good_bets": 0, "lean_bets": 1, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 0.0, "good_pct": 0.0, "lean_pct": 100.0}}}