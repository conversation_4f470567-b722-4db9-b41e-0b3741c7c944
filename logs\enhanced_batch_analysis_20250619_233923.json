{"timestamp": "20250619_233923", "total_matches": 1, "successful_scrapes": 1, "failed_urls": [], "success_rate": 100.0, "processing_time": 28.***************, "min_confidence": 70.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "FURIA Esports", "ranking": 11, "ensi_score": 1850, "winrate_10": 70.0, "winrate_30": 50.0, "current_shape": 120.0, "avg_kd": 1.16, "players": [{"name": "molodoy", "nationality": "", "kd_ratio": 1.43}, {"name": "yu<PERSON>h", "nationality": "", "kd_ratio": 1.22}, {"name": "KSCerato", "nationality": "", "kd_ratio": 1.22}, {"name": "FalleN", "nationality": "", "kd_ratio": 1.05}, {"name": "YEKINDAR", "nationality": "", "kd_ratio": 0.9}]}, "team2": {"name": "paiN Gaming", "ranking": 28, "ensi_score": 1764, "winrate_10": 60.0, "winrate_30": 37.0, "current_shape": 123.0, "avg_kd": 1.09, "players": [{"name": "nqz", "nationality": "", "kd_ratio": 1.27}, {"name": "Snow", "nationality": "", "kd_ratio": 1.14}, {"name": "dgt", "nationality": "", "kd_ratio": 1.12}, {"name": "dav1deuS", "nationality": "", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}]}, "h2h_record": "FURIA: 7 - Draws: 0 - paiN: 2 (78% vs 22%)", "prediction": "FURIA Esports", "confidence": 85, "betting_advice": "🟢 BEST BET: MONEYLINE - STRONG BET: FURIA Esports to WIN (85% confidence) | Alternative: total_rounds (70%)", "key_factors": ["🆚 H2H advantage: FURIA Esports (7-2 record)", "📈 Better recent form: FURIA Esports (18W-11L vs 9W-19L)", "🏆 Ranking advantage: FURIA Esports (#11 vs #28)", "📈 ENSI advantage: FURIA Esports (1850 vs 1764)"], "additional_factors": {"h2h_data": {"previous_encounters": 9, "h2h_record": "FURIA: 7 - Draws: 0 - paiN: 2 (78% vs 22%)", "team1_wins": 7, "team2_wins": 2, "draws": 0, "recent_matches": [": paiN Gaming 1:0 paiN", ": FURIA Esports 2:1 FURIA", ": FURIA Esports 2:0 FURIA", ": paiN Gaming 1:2 paiN"], "team1_name": "FURIA", "team2_name": "paiN", "team1_win_percentage": 78, "team2_win_percentage": 22, "competitive_encounters": 9, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-1", "tournament": "2025 BLAST.tv Austin Major", "h2h_history": [{"score": "1:0", "context": "recent_match"}, {"score": "1:0", "context": "recent_match"}, {"score": "1:0", "context": "recent_match"}, {"score": "1:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 5}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.43, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "molodoy", "nationality": "", "kd_ratio": 1.43}, {"name": "yu<PERSON>h", "nationality": "", "kd_ratio": 1.22}, {"name": "KSCerato", "nationality": "", "kd_ratio": 1.22}, {"name": "FalleN", "nationality": "", "kd_ratio": 1.05}, {"name": "YEKINDAR", "nationality": "", "kd_ratio": 0.9}], "team2_players": [{"name": "nqz", "nationality": "", "kd_ratio": 1.27}, {"name": "Snow", "nationality": "", "kd_ratio": 1.14}, {"name": "dgt", "nationality": "", "kd_ratio": 1.12}, {"name": "dav1deuS", "nationality": "", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}], "team1_avg_kd": 1.16, "team2_avg_kd": 1.09}, "recent_performance": {"team1_recent_matches": [{"score": "1:1", "result": "L", "opponent": "LIVE\n        \n\n\n\n\n\n\n\npaiN\n\n\n\n\n\n\n\nbiguzera\n\nRodrigo <PERSON>court\n\n\n\n\ndav1deuS\n\nDavid <PERSON>\n\n\n\n\ndgt\n\n<PERSON>\n\n\n\n\nnqz\n\nLucas  <PERSON>\n\n\n\n\nSnow\n\nJoão <PERSON> Age\n\n\n\n\n\n                    Create Prediction\n                \n\n                Betting Tips", "tournament": "Recent", "date": "Recent"}, {"score": "1:0", "result": "W", "opponent": "FURIA Esports\nFURIA\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "paiN Gaming\npaiN\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "paiN Gaming\npaiN\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "FURIA Esports\nFURIA\n\n\n\n\n\n\n\n\n\n\n\n            All FURIA and paiN Encounters", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:1", "result": "L", "opponent": "1 \n\n3rd Map\n\n\n\n\n\n\n\nFalleN\n\nGabriel Toledo\n\n\n\n\nyu<PERSON>h\n\n<PERSON>\n\n\n\n\nYEKINDAR\n\nMareks Gaļinskis\n\n\n\n\nKSCerato\n\nKaike Cerato\n\n\n\n\nmolodoy\n\n<PERSON>\n\n\n\n\n\n\n\n\nFURIA", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "31\n\n\n\n\n\n\npaiN Gaming\npaiN", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "21\n\n\n\n\n\n\nFURIA Esports\nFURIA", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "04\n\n\n\n\n\n\nFURIA Esports\nFURIA", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "52\n\n\n\n\n\n\npaiN Gaming\npaiN", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 18, "losses": 11}, "team2_recent_form": {"wins": 9, "losses": 19}, "team1_opponents": ["LIV", "FUR", "pai", "pai", "FUR", "Vir", "FUR", "M80", "pai", "pai", "Nem", "pai", "pai", "LIV", "LIV", "FUR", "FUR", "pai", "pai", "pai", "M80", "FUR", "M80", "pai", "pai", "Nem", "pai"], "team2_opponents": ["Pro", "Fal", "FUR", "pai", "FUR", "FUR", "pai", "<PERSON><PERSON>", "The", "FUR", "pai", "Tea"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 21, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["paiN Gaming", "paiN", "FURIA Esports", "FURIA", "Virtus.Pro", "Lynn Vision Gaming", "LVG", "Made in Brazil", "MIBR", "Team Spirit", "Spirit", "ODDIK", "Aurora Gaming", "Aurora", "The Mongolz", "Mongolz", "M80", "3DMAX", "Nemiga Gaming", "Nemiga", "G2 Esports"]}, "map_statistics": {"map_pool": [], "team1_map_preferences": {}, "team2_map_preferences": {}, "banned_maps": [], "picked_maps": [], "map_winrates": {}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "betting_markets": {"moneyline": {"prediction": "STRONG BET: FURIA Esports to WIN", "confidence": 85, "reasoning": ["Significant advantage: 18.5 points", "Strong H2H record: FURIA Esports dominates", "Identical current form: 3% gap", "Hot streak: 5 wins", "Individual skill edge: FURIA Esports (0.113 K/D gap)", "Slight ENSI edge: FURIA Esports (86 points)"], "risk_level": "Low", "value_rating": 9}, "total_rounds": {"prediction": "OVER 20.5 avg rounds per map", "confidence": 70, "reasoning": ["Close teams (0.070 K/D diff)", "Multiple competitive maps expected", "BO3 format allows for long matches", "High player skill variance: 0.167 (unstable rounds)", "Similar recent form suggests close contest"], "risk_level": "Medium", "value_rating": 7, "value_analysis": {"has_value": true, "value_percentage": 74.**************, "implied_probability": 40.0, "our_probability": 70, "recommendation": "STRONG VALUE", "stake_suggestion": "High (3-5% bankroll)"}, "live_factors": {"momentum_shift": "hot_streak", "pressure_level": "low", "upset_potential": 10, "market_sentiment": "neutral", "late_roster_changes": true, "time_until_match": "unknown"}}, "combination_bets": {"prediction": "Combine 2-3 highest confidence bets", "confidence": 55, "reasoning": ["2 high-confidence markets available", "Increased payout potential", "Higher risk but good value"], "risk_level": "High", "value_rating": 8}}, "original_url": "https://ensigame.com/matches/cs-2/1369182-furia-esports-furia-vs-pain-gaming-pain-blast-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-19 23:39:23.495346"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 100.0, "expected_return": 47.**************, "risk_score": 60.0, "diversification_score": 65, "recommendations": [{"match": "FURIA Esports vs paiN Gaming", "bet_type": "Moneyline", "recommendation": "STRONG BET: FURIA Esports to WIN", "confidence": 85, "value_rating": 0.7, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 30.75, "risk_level": "MEDIUM", "reasoning": ["Significant advantage: 18.5 points", "Strong H2H record: FURIA Esports dominates", "Identical current form: 3% gap", "Hot streak: 5 wins", "Individual skill edge: FURIA Esports (0.113 K/D gap)", "Slight ENSI edge: FURIA Esports (86 points)"]}, {"match": "FURIA Esports vs paiN Gaming", "bet_type": "Total Rounds", "recommendation": "OVER 20.5 avg rounds per map", "confidence": 70, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 16.***************, "risk_level": "MEDIUM", "reasoning": ["Close teams (0.070 K/D diff)", "Multiple competitive maps expected", "BO3 format allows for long matches", "High player skill variance: 0.167 (unstable rounds)", "Similar recent form suggests close contest"]}]}, "enhancement_stats": {"total_predictions": 1, "average_confidence": 85.0, "premium_bets": 1, "strong_bets": 0, "good_bets": 0, "lean_bets": 0, "confidence_distribution": {"premium_pct": 100.0, "strong_pct": 0.0, "good_pct": 0.0, "lean_pct": 0.0}}}