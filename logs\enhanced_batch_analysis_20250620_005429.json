{"timestamp": "20250620_005429", "total_matches": 15, "successful_scrapes": 15, "failed_urls": [], "success_rate": 100.0, "processing_time": 328.**************, "min_confidence": 65.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "500 500", "ranking": 69, "ensi_score": 1588, "winrate_10": 20.0, "winrate_30": 37.0, "current_shape": 83.0, "avg_kd": 1.03, "players": [{"name": "Rainwaker", "nationality": "", "kd_ratio": 1.12}, {"name": "REDSTAR", "nationality": "", "kd_ratio": 1.09}, {"name": "Oxygen", "nationality": "", "kd_ratio": 1.09}, {"name": "CeRq", "nationality": "", "kd_ratio": 1.05}, {"name": "SPELLAN", "nationality": "", "kd_ratio": 0.82}]}, "team2": {"name": "Amkal Amkal CCT EU 20 06 25", "ranking": 68, "ensi_score": 1589, "winrate_10": 80.0, "winrate_30": 60.0, "current_shape": 120.0, "avg_kd": 0.97, "players": [{"name": "tommy171", "nationality": "", "kd_ratio": 1.15}, {"name": "AW", "nationality": "", "kd_ratio": 1.09}, {"name": "kAlash", "nationality": "", "kd_ratio": 0.94}, {"name": "sstiNiX", "nationality": "", "kd_ratio": 0.92}, {"name": "sFade8", "nationality": "", "kd_ratio": 0.77}]}, "h2h_record": "500: 1 - Draws: 0 - AMKAL: 0 (100% vs 0%)", "prediction": "Amkal Amkal CCT EU 20 06 25", "confidence": 74.20691666666667, "betting_advice": "🟢 BEST BET: MATCH_WINNER - Amkal Amkal CCT EU 20 06 25 to win match (84.**************% confidence) | Alternative: MAP_HANDICAP (80%)", "key_factors": ["🆚 H2H record: 500 500 leads (1-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: Amkal Amkal CCT EU 20 06 25 (15W-6L vs 6W-16L)", "⚡ Better current shape: Amkal Amkal CCT EU 20 06 25 (83.0% vs 120.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "500: 1 - Draws: 0 - AMKAL: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": AMKAL 0:2 AMKAL"], "team1_name": "500", "team2_name": "AMKAL", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2024 YaLLa Compass Fall", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.12, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "Rainwaker", "nationality": "", "kd_ratio": 1.12}, {"name": "REDSTAR", "nationality": "", "kd_ratio": 1.09}, {"name": "Oxygen", "nationality": "", "kd_ratio": 1.09}, {"name": "CeRq", "nationality": "", "kd_ratio": 1.05}, {"name": "SPELLAN", "nationality": "", "kd_ratio": 0.82}], "team2_players": [{"name": "tommy171", "nationality": "", "kd_ratio": 1.15}, {"name": "AW", "nationality": "", "kd_ratio": 1.09}, {"name": "kAlash", "nationality": "", "kd_ratio": 0.94}, {"name": "sstiNiX", "nationality": "", "kd_ratio": 0.92}, {"name": "sFade8", "nationality": "", "kd_ratio": 0.77}], "team1_avg_kd": 1.03, "team2_avg_kd": 0.97}, "recent_performance": {"team1_recent_matches": [{"score": "500:1", "result": "W", "opponent": "Draws", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "500\n500\n\n\n\n\n\n\n\n\n\n\n\n            All 500 and AMKAL Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "500:0", "result": "W", "opponent": "Wins of AMKAL", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "9INE\n9INE\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:500", "result": "L", "opponent": "Wins of", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "14\n\n\n\n\n\n\nAMKAL\nAMKAL", "tournament": "Recent", "date": "Recent"}, {"score": "0:500", "result": "L", "opponent": "Wins of", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "56\n\n\n\n\n\n\n500\n500", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "27\n\n\n\n\n\n\n500\n500", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 6, "losses": 16}, "team2_recent_form": {"wins": 15, "losses": 6}, "team1_opponents": ["<PERSON>a", "Win", "<PERSON><PERSON>", "RUS", "Tea", "AMK", "AMK", "AMK", "Inn", "Inn", "<PERSON><PERSON>", "RUS", "AMK", "AMK", "AMK", "Inn"], "team2_opponents": ["Win", "Win", "AMK", "ENC", "Par", "RUB", "Fis", "<PERSON><PERSON>", "AMK"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 20, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["AMKAL", "500", "RUBY", "Sangal Esports", "<PERSON><PERSON>", "PARIVISION", "Passion UA", "Partizan Esports", "Partizan", "Iberian Soul", "9INE", "ENCE", "RUSH B", "Team Spirit Academy", "TS.A", "Fisher College", "Dynamo Eclot", "Eclot", "Inner Circle Esports", "ICE"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Rainwaker": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "REDSTAR": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "Oxygen": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "CeRq": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 105.0}, "SPELLAN": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 82.0}}, "team2_form_trends": {"tommy171": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "AW": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "kAlash": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "sstiNiX": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "sFade8": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 77.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "SPELLAN", "player1_kd": 0.82, "player2": "sFade8", "player2_kd": 0.77, "impact": "MEDIUM", "description": "Tactical battle: SPELLAN vs sFade8"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Amkal Amkal CCT EU 20 06 25 to win match", "confidence": 84.**************, "reasoning": ["Team strength difference: 23.1", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Amkal Amkal CCT EU 20 06 25 -1.5 maps (2-0 win)", "confidence": 80, "reasoning": ["Based on team strength difference: 23.1"]}, "TOTAL_MAPS": {"prediction": "UNDER 2.5 maps (2-0 likely)", "confidence": 68.13794444444444, "reasoning": ["Team strength analysis: 23.1 difference"]}, "CORRECT_SCORE": {"prediction": "Amkal Amkal CCT EU 20 06 25 2-0", "confidence": 75, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Amkal Amkal CCT EU 20 06 25 first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON> vs tommy171", "confidence": 55, "reasoning": ["K/D comparison: 1.12 vs 1.15"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1379847-500-500-vs-amkal-amkal-cct-eu-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:49:23.665247"}}, "page_content": ""}, {"team1": {"name": "HEROIC Academy", "ranking": 188, "ensi_score": 1403, "winrate_10": 50.0, "winrate_30": 45.0, "current_shape": 105.0, "avg_kd": 0.87, "players": [{"name": "fnl", "nationality": "", "kd_ratio": 0.99}, {"name": "Scr0b", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "anber", "nationality": "Denmark", "kd_ratio": 0.89}, {"name": "St0m4k", "nationality": "", "kd_ratio": 0.8}, {"name": "Dengzoe", "nationality": "Denmark", "kd_ratio": 0.71}]}, "team2": {"name": "KS Esports", "ranking": 129, "ensi_score": 1452, "winrate_10": 50.0, "winrate_30": 56.0, "current_shape": 94.0, "avg_kd": 0.95, "players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "KS Esports", "confidence": 50.426416666666675, "betting_advice": "🟢 BEST BET: TOTAL_MAPS - OVER 2.5 maps (3 maps likely) (77.71572222222221% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: KS Esports (#188 vs #129)", "⚡ Better current shape: HEROIC Academy (105.0% vs 94.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 European Pro League Season 28: Division 2", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "fnl", "nationality": "", "kd_ratio": 0.99}, {"name": "Scr0b", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "anber", "nationality": "Denmark", "kd_ratio": 0.89}, {"name": "St0m4k", "nationality": "", "kd_ratio": 0.8}, {"name": "Dengzoe", "nationality": "Denmark", "kd_ratio": 0.71}], "team2_players": [{"name": "<PERSON>ey", "nationality": "", "kd_ratio": 1.11}, {"name": "BledarD", "nationality": "", "kd_ratio": 1.0}, {"name": "ammar", "nationality": "", "kd_ratio": 1.0}, {"name": "Caleyy", "nationality": "", "kd_ratio": 0.98}, {"name": "gejmzilla", "nationality": "", "kd_ratio": 0.67}], "team1_avg_kd": 0.87, "team2_avg_kd": 0.95}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "XI Esport\nXI\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "HEROIC Academy\nHero", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "HEROIC Academy\nHero", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "KS Esports\nKS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "The Suspect\nSuspect\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "A", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "03\n\n\n\n\n\n\nNexus Gaming\nNexus", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "29\n\n\n\n\n\n\nFisher College\nFC", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "36\n\n\n\n\n\n\nPartizan Esports\nPartizan", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "45\n\n\n\n\n\n\nIberian Soul\nIS", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 6, "losses": 7}, "team2_recent_form": {"wins": 8, "losses": 9}, "team1_opponents": ["HER", "HER", "The", "The"], "team2_opponents": ["Nex", "Fis", "Par", "<PERSON><PERSON>", "los", "eSp", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"fnl": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "Scr0b": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "anber": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}, "St0m4k": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 80.0}, "Dengzoe": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 71.0}}, "team2_form_trends": {"tripey": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "BledarD": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "ammar": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Caleyy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "gejmzilla": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 67.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Dengzoe", "player1_kd": 0.71, "player2": "gejmzilla", "player2_kd": 0.67, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs gejm<PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "KS Esports to win match", "confidence": 50.426416666666675, "reasoning": ["Team strength difference: 0.3", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "HEROIC Academy +1.5 maps (avoid 0-2 loss)", "confidence": 75, "reasoning": ["Based on team strength difference: 0.3"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 77.71572222222221, "reasoning": ["Team strength analysis: 0.3 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "tripey most kills vs fnl", "confidence": 72.00000000000001, "reasoning": ["K/D comparison: 0.99 vs 1.11"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375299-heroic-academy-heroa-vs-ks-esports-ks-epl-s28-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:49:45.672048"}}, "page_content": ""}, {"team1": {"name": "ESC Gaming", "ranking": 89, "ensi_score": 1549, "winrate_10": 80.0, "winrate_30": 63.0, "current_shape": 117.0, "avg_kd": 0.98, "players": [{"name": "reiko", "nationality": "", "kd_ratio": 1.14}, {"name": "bajmi", "nationality": "", "kd_ratio": 1.07}, {"name": "SaMeY", "nationality": "", "kd_ratio": 1.04}, {"name": "olimp", "nationality": "", "kd_ratio": 0.88}, {"name": "moonwalk", "nationality": "", "kd_ratio": 0.76}]}, "team2": {"name": "Prestige Prestige", "ranking": 166, "ensi_score": 1420, "winrate_10": 60.0, "winrate_30": 60.0, "current_shape": 100.0, "avg_kd": 0.9, "players": [{"name": "Mol011", "nationality": "Denmark", "kd_ratio": 1.04}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "NickyB", "nationality": "Denmark", "kd_ratio": 0.48}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "GA1De", "nationality": "Denmark", "kd_ratio": 1.0}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "ESC Gaming", "confidence": 71.37325, "betting_advice": "🟢 BEST BET: MATCH_WINNER - ESC Gaming to win match (71.37325% confidence) | Alternative: CORRECT_SCORE (70%)", "key_factors": ["🏆 Ranking advantage: ESC Gaming (#89 vs #166)", "📈 ENSI advantage: ESC Gaming (1549 vs 1420)", "⚡ Better current shape: ESC Gaming (117.0% vs 100.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 European Pro League Season 28: Division 2", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.14, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "reiko", "nationality": "", "kd_ratio": 1.14}, {"name": "bajmi", "nationality": "", "kd_ratio": 1.07}, {"name": "SaMeY", "nationality": "", "kd_ratio": 1.04}, {"name": "olimp", "nationality": "", "kd_ratio": 0.88}, {"name": "moonwalk", "nationality": "", "kd_ratio": 0.76}], "team2_players": [{"name": "Mol011", "nationality": "Denmark", "kd_ratio": 1.04}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "NickyB", "nationality": "Denmark", "kd_ratio": 0.48}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 1.0}, {"name": "GA1De", "nationality": "Denmark", "kd_ratio": 1.0}], "team1_avg_kd": 0.98, "team2_avg_kd": 0.9}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "ESC Gaming\nESC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Kronjyllands esports\nKronjy\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "ESC Gaming\nESC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Mousquetaires\nMSQ\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "ESC Gaming\nESC\n\n\n\n\n\n\n\n\n\n\n                    All ESC Encounters\n                \n\n\n\n\nPrestige\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "08\n\n\n\n\n\n\nENERGYULTRA\nENY", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "50\n\n\n\n\n\n\nESC Gaming\nESC", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "03\n\n\n\n\n\n\nBad News Eagles\nBNE", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "27\n\n\n\n\n\n\nESC Gaming\nESC", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Reload", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 13, "losses": 9}, "team2_recent_form": {"wins": 7, "losses": 10}, "team1_opponents": ["ESC", "<PERSON><PERSON>", "ESC", "<PERSON><PERSON>", "ESC", "Pre", "Pre", "Pre", "Vol", "Vol", "ESC", "ESC", "<PERSON><PERSON>", "ESC", "<PERSON><PERSON>", "ESC", "Pre", "Pre", "Pre", "Vol"], "team2_opponents": ["<PERSON><PERSON>", "Ast", "ENE", "ESC", "Bad", "Bru", "Pre", "<PERSON><PERSON>", "Pre"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 20, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Ex-<PERSON><PERSON><PERSON>", "Ex-<PERSON><PERSON>", "Volt", "Bru<PERSON>", "ENERGYULTRA", "ENY", "ESC Gaming", "ESC", "Kronjyllands esports", "<PERSON><PERSON><PERSON><PERSON>", "Bad News Eagles", "BNE", "Mousquetaires", "MSQ", "FORZE Reload", "<PERSON><PERSON>", "Prestige", "XI Esport", "Anonymo Esports", "Anonymo"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"reiko": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 113.99999999999999}, "bajmi": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "SaMeY": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "olimp": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "moonwalk": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 76.0}}, "team2_form_trends": {"Mol011": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "Mizi": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "NickyB": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 48.0}, "Folke": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "GA1De": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "moonwalk", "player1_kd": 0.76, "player2": "NickyB", "player2_kd": 0.48, "impact": "MEDIUM", "description": "Tactical battle: moonwalk vs Nicky<PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "ESC Gaming to win match", "confidence": 71.37325, "reasoning": ["Team strength difference: 14.2", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Prestige Prestige +1.5 maps (avoid 0-2 loss)", "confidence": 65.75116666666668, "reasoning": ["Based on team strength difference: 14.2"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 14.2 difference"]}, "CORRECT_SCORE": {"prediction": "ESC Gaming 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "ESC Gaming first map", "confidence": 67.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON> vs Mol011", "confidence": 55, "reasoning": ["K/D comparison: 1.14 vs 1.04"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1375298-esc-gaming-esc-vs-prestige-prestige-epl-s28-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:50:06.388539"}}, "page_content": ""}, {"team1": {"name": "BIG BIG", "ranking": 45, "ensi_score": 1680, "winrate_10": 50.0, "winrate_30": 43.0, "current_shape": 107.0, "avg_kd": 1.05, "players": [{"name": "hyped", "nationality": "", "kd_ratio": 1.24}, {"name": "Krimbo", "nationality": "", "kd_ratio": 1.12}, {"name": "tabseN", "nationality": "", "kd_ratio": 1.0}, {"name": "JDC", "nationality": "", "kd_ratio": 0.97}, {"name": "k<PERSON><PERSON>i", "nationality": "", "kd_ratio": 0.91}]}, "team2": {"name": "Zero Tenacity Z10 CCT EU 20 06 25", "ranking": 72, "ensi_score": 1576, "winrate_10": 50.0, "winrate_30": 53.0, "current_shape": 97.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.12}, {"name": "nEMANHA", "nationality": "", "kd_ratio": 1.04}, {"name": "brutmonster", "nationality": "", "kd_ratio": 1.04}, {"name": "simke", "nationality": "", "kd_ratio": 1.02}, {"name": "aVN", "nationality": "", "kd_ratio": 0.82}]}, "h2h_record": "BIG: 1 - Draws: 0 - Z10: 0 (100% vs 0%)", "prediction": "BIG BIG", "confidence": 72.7825, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - hyped most kills vs Cjoffo (71.99999999999999% confidence) | Alternative: MAP_HANDICAP (71.81166666666667%)", "key_factors": ["🆚 H2H record: BIG BIG leads (1-0) 🎯 (Similar rosters - high relevance)", "🏆 Ranking advantage: BIG BIG (#45 vs #72)", "📈 ENSI advantage: BIG BIG (1680 vs 1576)", "⚡ Better current shape: BIG BIG (107.0% vs 97.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "BIG: 1 - Draws: 0 - Z10: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": BIG 2:0 BIG"], "team1_name": "BIG", "team2_name": "Z10", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2023 CCT Online Finals 5", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.24, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "hyped", "nationality": "", "kd_ratio": 1.24}, {"name": "Krimbo", "nationality": "", "kd_ratio": 1.12}, {"name": "tabseN", "nationality": "", "kd_ratio": 1.0}, {"name": "JDC", "nationality": "", "kd_ratio": 0.97}, {"name": "k<PERSON><PERSON>i", "nationality": "", "kd_ratio": 0.91}], "team2_players": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.12}, {"name": "nEMANHA", "nationality": "", "kd_ratio": 1.04}, {"name": "brutmonster", "nationality": "", "kd_ratio": 1.04}, {"name": "simke", "nationality": "", "kd_ratio": 1.02}, {"name": "aVN", "nationality": "", "kd_ratio": 0.82}], "team1_avg_kd": 1.05, "team2_avg_kd": 1.01}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "Zero Tenacity\nZ10\n\n\n\n\n\n\n\n\n\n\n\n            All BIG  and Z10 Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "PARIVISION\nPARIVISION\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "TEAM NEXT LEVEL\nTNL\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "CYBERSHOKE Esports\nCS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "29\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "21\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "42\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "59\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "36\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 11, "losses": 8}, "team2_recent_form": {"wins": 8, "losses": 12}, "team1_opponents": ["<PERSON><PERSON>", "PAR", "TEA", "Ast", "CYB", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "PAR", "TEA", "Ast", "CYB", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "team2_opponents": ["BIG", "BIG", "BIG", "BIG", "Fna", "Par", "CYB", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["BIG", "Zero Tenacity", "Z10", "Astrum", "PARIVISION", "CYBERSHOKE Esports", "TEAM NEXT LEVEL", "TNL", "ex-Permitta Esports", "ex-<PERSON><PERSON><PERSON>", "Fnatic", "FNC", "Partizan Esports", "Partizan", "9INE", "Ex-Sabre Esports", "Ex-Sabre"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"hyped": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 124.0}, "Krimbo": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "tabseN": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "JDC": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "kyuubii": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 91.0}}, "team2_form_trends": {"Cjoffo": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "nEMANHA": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "brutmonster": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "simke": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "aVN": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 82.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "k<PERSON><PERSON>i", "player1_kd": 0.91, "player2": "aVN", "player2_kd": 0.82, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs aVN"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"hyped": {"team": "BIG BIG", "kd_ratio": 1.24, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "BIG BIG to win match", "confidence": 62.2825, "reasoning": ["Team strength difference: 8.2", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Zero Tenacity Z10 CCT EU 20 06 25 +1.5 maps (avoid 0-2 loss)", "confidence": 71.81166666666667, "reasoning": ["Based on team strength difference: 8.2"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 8.2 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "BIG BIG first map", "confidence": 62.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "hyped most kills vs <PERSON><PERSON><PERSON><PERSON>", "confidence": 71.99999999999999, "reasoning": ["K/D comparison: 1.24 vs 1.12"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1379783-big-big-vs-zero-tenacity-z10-cct-eu-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:50:29.148234"}}, "page_content": ""}, {"team1": {"name": "Passion UA Passion UA", "ranking": 29, "ensi_score": 1758, "winrate_10": 80.0, "winrate_30": 67.0, "current_shape": 113.0, "avg_kd": 1.0, "players": [{"name": "Woro2k", "nationality": "", "kd_ratio": 1.19}, {"name": "Kvem", "nationality": "", "kd_ratio": 1.02}, {"name": "DemQQ", "nationality": "", "kd_ratio": 1.01}, {"name": "JACKASMO", "nationality": "", "kd_ratio": 0.96}, {"name": "Topa", "nationality": "", "kd_ratio": 0.82}]}, "team2": {"name": "Gun5 Esports Gun5 CCT EU 20 06 25", "ranking": 70, "ensi_score": 1581, "winrate_10": 50.0, "winrate_30": 43.0, "current_shape": 107.0, "avg_kd": 0.95, "players": [{"name": "fineshine52", "nationality": "", "kd_ratio": 1.09}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}, {"name": "SELLTER", "nationality": "", "kd_ratio": 0.93}, {"name": "XiELO", "nationality": "", "kd_ratio": 0.92}, {"name": "Lack1", "nationality": "", "kd_ratio": 0.84}]}, "h2h_record": "Passion UA: 1 - Draws: 0 - GUN5: 1 (50% vs 50%)", "prediction": "Passion UA Passion UA", "confidence": 82.**************, "betting_advice": "🟢 BEST BET: MATCH_WINNER - Passion UA Passion UA to win match (82.**************% confidence) | Alternative: MAP_HANDICAP (80%)", "key_factors": ["🆚 H2H record: Even (1-1) 🎯 (Similar rosters - high relevance)", "🏆 Ranking advantage: Passion UA Passion UA (#29 vs #70)", "📈 ENSI advantage: Passion UA Passion UA (1758 vs 1581)"], "additional_factors": {"h2h_data": {"previous_encounters": 2, "h2h_record": "Passion UA: 1 - Draws: 0 - GUN5: 1 (50% vs 50%)", "team1_wins": 1, "team2_wins": 1, "draws": 0, "recent_matches": [": Passion UA 1:2 Passion UA", ": GUN5 Esports 0:2 GUN5"], "team1_name": "Passion UA", "team2_name": "GUN5", "team1_win_percentage": 50, "team2_win_percentage": 50, "competitive_encounters": 2, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 2 European Series #18", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 6}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.19, "team2_odds": 1.02, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "Woro2k", "nationality": "", "kd_ratio": 1.19}, {"name": "Kvem", "nationality": "", "kd_ratio": 1.02}, {"name": "DemQQ", "nationality": "", "kd_ratio": 1.01}, {"name": "JACKASMO", "nationality": "", "kd_ratio": 0.96}, {"name": "Topa", "nationality": "", "kd_ratio": 0.82}], "team2_players": [{"name": "fineshine52", "nationality": "", "kd_ratio": 1.09}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.95}, {"name": "SELLTER", "nationality": "", "kd_ratio": 0.93}, {"name": "XiELO", "nationality": "", "kd_ratio": 0.92}, {"name": "Lack1", "nationality": "", "kd_ratio": 0.84}], "team1_avg_kd": 1.0, "team2_avg_kd": 0.95}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "GUN5 Esports\nGUN5\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Passion UA\nPassion UA\n\n\n\n\n\n\n\n\n\n\n\n            All Passion UA and GUN5 Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Passion UA\nPassion UA\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Passion UA\nPassion UA\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Sashi Esport\nSashi\n\n\n\n\n\n\n\n\n\n\n                    All Passion UA Encounters\n                \n\n\n\n\nGUN5 Esports\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "31\n\n\n\n\n\n\nPassion UA\nPassion UA", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "54\n\n\n\n\n\n\nGUN5 Esports\nGUN5", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "49\n\n\n\n\n\n\nFnatic\nFNC", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "13\n\n\n\n\n\n\n9INE\n9INE", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "06\n\n\n\n\n\n\nECSTATIC\nECSTATIC", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 14, "losses": 7}, "team2_recent_form": {"wins": 12, "losses": 10}, "team1_opponents": ["GUN", "Pas", "Pas", "Pas", "Sas", "GUN", "<PERSON><PERSON>", "RUB", "Fis", "FAV", "Pas", "GUN", "FAV", "Sas", "Pas", "Sas", "GUN", "<PERSON><PERSON>", "RUB", "Fis", "FAV"], "team2_opponents": ["Pas", "GUN", "Fna", "ECS", "All", "Pas", "Nex", "GUN", "GUN"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 22, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Passion UA", "GUN5 Esports", "GUN5", "Nexus Gaming", "Nexus", "Dynamo Eclot", "Eclot", "9INE", "Partizan Esports", "Partizan", "Team Spirit Academy", "TS.A", "Fnatic", "FNC", "ECSTATIC", "Alliance", "Sashi Esport", "<PERSON><PERSON>", "RUBY", "Fisher College", "FAVBET Team", "FAVBET"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Woro2k": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 119.0}, "Kvem": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "DemQQ": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "JACKASMO": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "Topa": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 82.0}}, "team2_form_trends": {"fineshine52": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "Sdaim": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "SELLTER": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "XiELO": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "Lack1": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Topa", "player1_kd": 0.82, "player2": "Lack1", "player2_kd": 0.84, "impact": "MEDIUM", "description": "Tactical battle: Topa vs Lack1"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Passion UA Passion UA to win match", "confidence": 82.**************, "reasoning": ["Team strength difference: 21.4", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Passion UA Passion UA -1.5 maps (2-0 win)", "confidence": 80, "reasoning": ["Based on team strength difference: 21.4"]}, "TOTAL_MAPS": {"prediction": "UNDER 2.5 maps (2-0 likely)", "confidence": 66.43511111111113, "reasoning": ["Team strength analysis: 21.4 difference"]}, "CORRECT_SCORE": {"prediction": "Passion UA Passion UA 2-0", "confidence": 75, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Passion UA Passion UA first map", "confidence": 60.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: Woro2k vs fineshine52", "confidence": 55, "reasoning": ["K/D comparison: 1.19 vs 1.09"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1379815-passion-ua-passion-ua-vs-gun5-esports-gun5-cct-eu-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:50:50.143980"}}, "page_content": ""}, {"team1": {"name": "Betclic Apogee Esports", "ranking": 194, "ensi_score": 1401, "winrate_10": 33.0, "winrate_30": 33.0, "current_shape": 100.0, "avg_kd": 0.97, "players": [{"name": "jcobbb", "nationality": "", "kd_ratio": 1.13}, {"name": "hypex", "nationality": "", "kd_ratio": 1.13}, {"name": "demho", "nationality": "", "kd_ratio": 0.94}, {"name": "hfah", "nationality": "", "kd_ratio": 0.85}, {"name": "Prism", "nationality": "", "kd_ratio": 0.78}]}, "team2": {"name": "Dynamo Eclot Eclot CCT EU 20 06 25", "ranking": 99, "ensi_score": 1507, "winrate_10": 40.0, "winrate_30": 37.0, "current_shape": 103.0, "avg_kd": 1.06, "players": [{"name": "FORSYY", "nationality": "", "kd_ratio": 1.41}, {"name": "nbqq", "nationality": "", "kd_ratio": 1.12}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.01}, {"name": "M1key", "nationality": "", "kd_ratio": 0.99}, {"name": "The eLiVe", "nationality": "", "kd_ratio": 0.78}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Dynamo Eclot Eclot CCT EU 20 06 25", "confidence": 57.12441666666667, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - FORSYY most kills vs jcobbb (80% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🏆 Ranking advantage: Dynamo Eclot Eclot CCT EU 20 06 25 (#194 vs #99)", "📈 ENSI advantage: Dynamo Eclot Eclot CCT EU 20 06 25 (1401 vs 1507)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 3 European Series #2", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.13, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "jcobbb", "nationality": "", "kd_ratio": 1.13}, {"name": "hypex", "nationality": "", "kd_ratio": 1.13}, {"name": "demho", "nationality": "", "kd_ratio": 0.94}, {"name": "hfah", "nationality": "", "kd_ratio": 0.85}, {"name": "Prism", "nationality": "", "kd_ratio": 0.78}], "team2_players": [{"name": "FORSYY", "nationality": "", "kd_ratio": 1.41}, {"name": "nbqq", "nationality": "", "kd_ratio": 1.12}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.01}, {"name": "M1key", "nationality": "", "kd_ratio": 0.99}, {"name": "The eLiVe", "nationality": "", "kd_ratio": 0.78}], "team1_avg_kd": 0.97, "team2_avg_kd": 1.06}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Betclic Apogee Esports\nBetclic\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Fire Flux Esports\nFF\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Nexus Gaming\nNexus\n\n\n\n\n\n\n\n\n\n\n                    All Betclic Encounters\n                \n\n\n\n\nDynamo Eclot\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "FAVBET Team\nFAVBET\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Monte\nMonte\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "11\n\n\n\n\n\n\nPartizan Esports\nPartizan", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "39\n\n\n\n\n\n\nBetclic Apogee Esports\nBetclic", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "54\n\n\n\n\n\n\nBetclic Apogee Esports\nBetclic", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "17\n\n\n\n\n\n\nDynamo Eclot\nEclot", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "26\n\n\n\n\n\n\nDynamo Eclot\nEclot", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 8, "losses": 8}, "team2_recent_form": {"wins": 6, "losses": 10}, "team1_opponents": ["Bet", "Fir", "Nex", "FAV", "Mon", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Nex", "Bet", "Fir", "Nex", "FAV", "Mon", "<PERSON><PERSON>", "<PERSON><PERSON>"], "team2_opponents": ["Par", "Bet", "Bet", "<PERSON><PERSON>", "<PERSON><PERSON>", "PAR", "GUN", "Sin"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Nexus Gaming", "Nexus", "Fire Flux Esports", "Partizan Esports", "Partizan", "Betclic Apogee Esports", "Betclic", "Dynamo Eclot", "Eclot", "FAVBET Team", "FAVBET", "Monte", "PARIVISION", "GUN5 Esports", "GUN5", "Sinners Esports", "Sinners"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"jcobbb": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "hypex": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "demho": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "hfah": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 85.0}, "Prism": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 78.0}}, "team2_form_trends": {"FORSYY": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 141.0}, "nbqq": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "Dytor": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "M1key": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 99.0}, "The eLiVe": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 78.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Prism", "player1_kd": 0.78, "player2": "The eLiVe", "player2_kd": 0.78, "impact": "MEDIUM", "description": "Tactical battle: Prism vs The eLiVe"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"FORSYY": {"team": "Dynamo Eclot Eclot CCT EU 20 06 25", "kd_ratio": 1.41, "impact_level": "HIGH", "recent_form": "IMPROVING"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Dynamo Eclot Eclot CCT EU 20 06 25 to win match", "confidence": 57.12441666666667, "reasoning": ["Team strength difference: 4.7", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Betclic Apogee Esports +1.5 maps (avoid 0-2 loss)", "confidence": 75, "reasoning": ["Based on team strength difference: 4.7"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 73.25038888888889, "reasoning": ["Team strength analysis: 4.7 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Dynamo Eclot Eclot CCT EU 20 06 25 first map", "confidence": 61.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "FORSYY most kills vs jcobbb", "confidence": 80, "reasoning": ["K/D comparison: 1.13 vs 1.41"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1379846-betclic-apogee-esports-betclic-vs-dynamo-eclot-eclot-cct-eu-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:51:10.785640"}}, "page_content": ""}, {"team1": {"name": "<PERSON><PERSON>", "ranking": 247, "ensi_score": 1378, "winrate_10": 33.0, "winrate_30": 33.0, "current_shape": 100.0, "avg_kd": 0.78, "players": [{"name": "Profug", "nationality": "", "kd_ratio": 0.93}, {"name": "zockie", "nationality": "", "kd_ratio": 0.84}, {"name": "Slayerhz", "nationality": "", "kd_ratio": 0.76}, {"name": "BabyRage", "nationality": "", "kd_ratio": 0.74}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.64}]}, "team2": {"name": "LP LP CCT SA 20 06 25", "ranking": 219, "ensi_score": 1386, "winrate_10": 33.0, "winrate_30": 33.0, "current_shape": 100.0, "avg_kd": 1.08, "players": [{"name": "happ", "nationality": "", "kd_ratio": 1.22}, {"name": "realz1n", "nationality": "", "kd_ratio": 1.14}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "bsd", "nationality": "", "kd_ratio": 1.05}, {"name": "zmb", "nationality": "", "kd_ratio": 0.91}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "LP LP CCT SA 20 06 25", "confidence": 56.99833333333333, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - happ most kills vs Profug (80% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["📈 Better recent form: LP LP CCT SA 20 06 25 (7W-4L vs 5W-9L)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: LP LP CCT SA 20 06 25 (#247 vs #219)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 3 South American Series #1", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "Profug", "nationality": "", "kd_ratio": 0.93}, {"name": "zockie", "nationality": "", "kd_ratio": 0.84}, {"name": "Slayerhz", "nationality": "", "kd_ratio": 0.76}, {"name": "BabyRage", "nationality": "", "kd_ratio": 0.74}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.64}], "team2_players": [{"name": "happ", "nationality": "", "kd_ratio": 1.22}, {"name": "realz1n", "nationality": "", "kd_ratio": 1.14}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "bsd", "nationality": "", "kd_ratio": 1.05}, {"name": "zmb", "nationality": "", "kd_ratio": 0.91}], "team1_avg_kd": 0.78, "team2_avg_kd": 1.08}, "recent_performance": {"team1_recent_matches": [{"score": "0:2", "result": "L", "opponent": "Yawara Esports\nYawara\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Bounty Hunters\nBH\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Messitas\nMessitas\n\n\n\n\n\n\n\n\n\n\n                    All JERSA Encounters\n                \n\n\n\n\nLP\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "LP\nLP\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "LP\nLP\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:0", "result": "W", "opponent": "34\n\n\n\n\n\n\nJERSA\nJERSA", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "31\n\n\n\n\n\n\nJERSA\nJERSA", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "45\n\n\n\n\n\n\nJERSA\nJERSA", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "47\n\n\n\n\n\n\nShindeN\nShindeN", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "38\n\n\n\n\n\n\n<PERSON>", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 5, "losses": 9}, "team2_recent_form": {"wins": 7, "losses": 4}, "team1_opponents": ["Yaw", "<PERSON><PERSON>", "<PERSON><PERSON>", "KRU", "KRU", "<PERSON><PERSON>", "Yaw", "<PERSON><PERSON>", "<PERSON><PERSON>", "KRU"], "team2_opponents": ["JER", "JER", "Shi", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Profug": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "zockie": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 84.0}, "Slayerhz": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 76.0}, "BabyRage": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 74.0}, "Antuanette": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 64.0}}, "team2_form_trends": {"happ": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 122.0}, "realz1n": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 113.99999999999999}, "Leomonster": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "bsd": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 105.0}, "zmb": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 91.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON><PERSON>", "player1_kd": 0.64, "player2": "zmb", "player2_kd": 0.91, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON> vs zmb"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"happ": {"team": "LP LP CCT SA 20 06 25", "kd_ratio": 1.22, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "LP LP CCT SA 20 06 25 to win match", "confidence": 56.99833333333333, "reasoning": ["Team strength difference: 4.7", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Jersa Jersa +1.5 maps (avoid 0-2 loss)", "confidence": 75, "reasoning": ["Based on team strength difference: 4.7"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 73.33444444444444, "reasoning": ["Team strength analysis: 4.7 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "LP LP CCT SA 20 06 25 first map", "confidence": 70.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "happ most kills vs Profug", "confidence": 80, "reasoning": ["K/D comparison: 0.93 vs 1.22"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384822-jersa-jersa-vs-lp-lp-cct-sa-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:51:32.151533"}}, "page_content": ""}, {"team1": {"name": "Betclic Apogee Esports", "ranking": 194, "ensi_score": 1401, "winrate_10": 33.0, "winrate_30": 33.0, "current_shape": 100.0, "avg_kd": 0.97, "players": [{"name": "jcobbb", "nationality": "", "kd_ratio": 1.13}, {"name": "hypex", "nationality": "", "kd_ratio": 1.13}, {"name": "demho", "nationality": "", "kd_ratio": 0.94}, {"name": "hfah", "nationality": "", "kd_ratio": 0.85}, {"name": "Prism", "nationality": "", "kd_ratio": 0.78}]}, "team2": {"name": "Copenhagen Wolves", "ranking": 147, "ensi_score": 1436, "winrate_10": 50.0, "winrate_30": 43.0, "current_shape": 107.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.15}, {"name": "Bielany", "nationality": "", "kd_ratio": 1.07}, {"name": "n1xen", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "Tapewaare", "nationality": "Norway", "kd_ratio": 0.87}, {"name": "matheos", "nationality": "", "kd_ratio": 1.0}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Copenhagen Wolves", "confidence": 59.25508333333333, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Betclic Apogee Esports +1.5 maps (avoid 0-2 loss) (73.82994444444444% confidence) | Alternative: TOTAL_MAPS (71.82994444444444%)", "key_factors": ["🏆 Ranking advantage: Copenhagen Wolves (#194 vs #147)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 3 European Series #2", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.13, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "jcobbb", "nationality": "", "kd_ratio": 1.13}, {"name": "hypex", "nationality": "", "kd_ratio": 1.13}, {"name": "demho", "nationality": "", "kd_ratio": 0.94}, {"name": "hfah", "nationality": "", "kd_ratio": 0.85}, {"name": "Prism", "nationality": "", "kd_ratio": 0.78}], "team2_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "Sweden", "kd_ratio": 1.15}, {"name": "Bielany", "nationality": "", "kd_ratio": 1.07}, {"name": "n1xen", "nationality": "Denmark", "kd_ratio": 0.95}, {"name": "Tapewaare", "nationality": "Norway", "kd_ratio": 0.87}, {"name": "matheos", "nationality": "", "kd_ratio": 1.0}], "team1_avg_kd": 0.97, "team2_avg_kd": 1.01}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Betclic Apogee Esports\nBetclic\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Fire Flux Esports\nFF\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Nexus Gaming\nNexus\n\n\n\n\n\n\n\n\n\n\n                    All Betclic Encounters\n                \n\n\n\n\nCopenhagen Wolves\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "ENCE Academy\nENCE", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Copenhagen Wolves\nCPHW\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "11\n\n\n\n\n\n\nPartizan Esports\nPartizan", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "39\n\n\n\n\n\n\nBetclic Apogee Esports\nBetclic", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "54\n\n\n\n\n\n\nBetclic Apogee Esports\nBetclic", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "06\n\n\n\n\n\n\nCopenhagen Wolves\nCPHW", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "19\n\n\n\n\n\n\nRebels Gaming\nREBELS", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 9, "losses": 8}, "team2_recent_form": {"wins": 5, "losses": 10}, "team1_opponents": ["Bet", "Fir", "Nex", "ENC", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Nex", "Bet", "Fir", "Nex", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "team2_opponents": ["<PERSON>b", "Par", "Bet", "Bet", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ast", "UNi"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 18, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Partizan Esports", "Partizan", "Betclic Apogee Esports", "Betclic", "Fire Flux Esports", "Nexus Gaming", "Nexus", "Copenhagen Wolves", "CPHW", "ENCE Academy", "ENCE.A", "Rebels Gaming", "REBELS", "Astrum", "UNiTY Esports", "UNiTY", "Ex-Sabre Esports", "Ex-Sabre"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"jcobbb": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "hypex": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.99999999999999}, "demho": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "hfah": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 85.0}, "Prism": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 78.0}}, "team2_form_trends": {"Jackinho": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 114.99999999999999}, "Bielany": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "n1xen": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 95.0}, "Tapewaare": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "matheos": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "Prism", "player1_kd": 0.78, "player2": "Tapewaare", "player2_kd": 0.87, "impact": "MEDIUM", "description": "Tactical battle: Prism vs Tapewaare"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Copenhagen Wolves to win match", "confidence": 59.25508333333333, "reasoning": ["Team strength difference: 6.2", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Betclic Apogee Esports +1.5 maps (avoid 0-2 loss)", "confidence": 73.82994444444444, "reasoning": ["Based on team strength difference: 6.2"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 71.82994444444444, "reasoning": ["Team strength analysis: 6.2 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Copenhagen Wolves first map", "confidence": 60.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON>", "confidence": 55, "reasoning": ["K/D comparison: 1.13 vs 1.15"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378186-betclic-apogee-esports-betclic-vs-copenhagen-wolves-cphw-gb-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:51:55.403750"}}, "page_content": ""}, {"team1": {"name": "Parivision", "ranking": 50, "ensi_score": 1669, "winrate_10": 50.0, "winrate_30": 60.0, "current_shape": 90.0, "avg_kd": 1.04, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.35}, {"name": "nota", "nationality": "", "kd_ratio": 1.02}, {"name": "BELCHONOKK", "nationality": "", "kd_ratio": 0.97}, {"name": "Qikert", "nationality": "", "kd_ratio": 0.96}, {"name": "XiELO", "nationality": "", "kd_ratio": 0.92}]}, "team2": {"name": "Roler <PERSON>", "ranking": 98, "ensi_score": 1510, "winrate_10": 60.0, "winrate_30": 63.0, "current_shape": 98.0, "avg_kd": 1.03, "players": [{"name": "ArtFr0st", "nationality": "", "kd_ratio": 1.37}, {"name": "d1<PERSON><PERSON>z", "nationality": "", "kd_ratio": 1.12}, {"name": "n0rb3r7", "nationality": "", "kd_ratio": 1.03}, {"name": "KaiR0N", "nationality": "", "kd_ratio": 0.86}, {"name": "na<PERSON>y", "nationality": "", "kd_ratio": 0.78}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Parivision", "confidence": 61.687499999999986, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Roler Coaster +1.5 maps (avoid 0-2 loss) (72.20833333333334% confidence) | Alternative: TOTAL_MAPS (70.20833333333334%)", "key_factors": ["📈 Better recent form: <PERSON><PERSON> (13W-6L vs 6W-12L)", "🏆 Ranking advantage: Parivision (#50 vs #98)", "📈 ENSI advantage: Parivision (1669 vs 1510)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 Exort The Proving Grounds Season 1", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 3}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.35, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.35}, {"name": "nota", "nationality": "", "kd_ratio": 1.02}, {"name": "BELCHONOKK", "nationality": "", "kd_ratio": 0.97}, {"name": "Qikert", "nationality": "", "kd_ratio": 0.96}, {"name": "XiELO", "nationality": "", "kd_ratio": 0.92}], "team2_players": [{"name": "ArtFr0st", "nationality": "", "kd_ratio": 1.37}, {"name": "d1<PERSON><PERSON>z", "nationality": "", "kd_ratio": 1.12}, {"name": "n0rb3r7", "nationality": "", "kd_ratio": 1.03}, {"name": "KaiR0N", "nationality": "", "kd_ratio": 0.86}, {"name": "na<PERSON>y", "nationality": "", "kd_ratio": 0.78}], "team1_avg_kd": 1.04, "team2_avg_kd": 1.03}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "PARIVISION\nPARIVISION\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "PARIVISION\nPARIVISION\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Dynamo Eclot\nEclot\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Zero Tenacity\nZ10\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Partizan Esports\nPartizan\n\n\n\n\n\n\n\n\n\n\n                    All PARIVISION Encounters\n                \n\n\n\n\nRoler Coaster\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "21\n\n\n\n\n\n\nBIG \nBIG", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "41\n\n\n\n\n\n\nSashi Esport\nSashi", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "27\n\n\n\n\n\n\nPARIVISION\nPARIVISION", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "13\n\n\n\n\n\n\nPARIVISION\nPARIVISION", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "48\n\n\n\n\n\n\nPARIVISION\nPARIVISION", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 6, "losses": 12}, "team2_recent_form": {"wins": 13, "losses": 6}, "team1_opponents": ["PAR", "PAR", "<PERSON><PERSON>", "<PERSON><PERSON>", "Par", "Rol", "Rol", "Sas", "Sas", "Par", "PAR", "PAR", "<PERSON><PERSON>", "<PERSON><PERSON>", "Par", "Rol", "Rol", "Sas"], "team2_opponents": ["<PERSON>b", "BIG", "Sas", "PAR", "PAR", "PAR", "FAV", "Mar", "Ast", "Rol"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Astrum", "<PERSON>", "BIG", "PARIVISION", "Sashi Esport", "<PERSON><PERSON>", "Dynamo Eclot", "Eclot", "Zero Tenacity", "Z10", "Partizan Esports", "Partizan", "FAVBET Team", "FAVBET", "Roler <PERSON>", "Ex-Sabre Esports", "Ex-Sabre"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Jame": {"trend": "STABLE_HIGH", "confidence": 75, "recent_matches": 5, "performance_rating": 135.0}, "nota": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "BELCHONOKK": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "Qikert": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "XiELO": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}}, "team2_form_trends": {"ArtFr0st": {"trend": "STABLE_HIGH", "confidence": 75, "recent_matches": 5, "performance_rating": 137.0}, "d1Ledez": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 112.00000000000001}, "n0rb3r7": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 103.0}, "KaiR0N": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 86.0}, "nafany": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 78.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "<PERSON><PERSON>", "player1_kd": 1.35, "player2": "ArtFr0st", "player2_kd": 1.37, "impact": "VERY_HIGH", "description": "Battle of star players: <PERSON><PERSON> vs ArtF<PERSON><PERSON><PERSON>"}, {"type": "IGL_BATTLE", "player1": "XiELO", "player1_kd": 0.92, "player2": "na<PERSON>y", "player2_kd": 0.78, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs na<PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"Jame": {"team": "Parivision", "kd_ratio": 1.35, "impact_level": "MEDIUM", "recent_form": "STABLE_HIGH"}, "ArtFr0st": {"team": "Roler <PERSON>", "kd_ratio": 1.37, "impact_level": "MEDIUM", "recent_form": "STABLE_HIGH"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Parivision to win match", "confidence": 61.687499999999986, "reasoning": ["Team strength difference: 7.8", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "<PERSON><PERSON>er +1.5 maps (avoid 0-2 loss)", "confidence": 72.20833333333334, "reasoning": ["Based on team strength difference: 7.8"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 70.20833333333334, "reasoning": ["Team strength analysis: 7.8 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: <PERSON><PERSON> vs ArtFr0st", "confidence": 55, "reasoning": ["K/D comparison: 1.35 vs 1.37"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378189-parivision-parivision-vs-roler-coaster-rc-gb-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:52:17.277945"}}, "page_content": ""}, {"team1": {"name": "Fire Flux Esports FF", "ranking": 108, "ensi_score": 1481, "winrate_10": 40.0, "winrate_30": 40.0, "current_shape": 100.0, "avg_kd": 0.99, "players": [{"name": "Soulfly", "nationality": "", "kd_ratio": 1.26}, {"name": "paz", "nationality": "", "kd_ratio": 0.98}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.97}, {"name": "ScrunK", "nationality": "", "kd_ratio": 0.88}, {"name": "duggy", "nationality": "", "kd_ratio": 0.87}]}, "team2": {"name": "Bushido Wildcats BW Btct 20 06 25", "ranking": 154, "ensi_score": 1426, "winrate_10": 40.0, "winrate_30": 50.0, "current_shape": 90.0, "avg_kd": 0.96, "players": [{"name": "Cizzx", "nationality": "", "kd_ratio": 0.81}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "vej", "nationality": "", "kd_ratio": 1.0}, {"name": "cadnyx", "nationality": "", "kd_ratio": 1.0}, {"name": "rim3", "nationality": "", "kd_ratio": 1.0}]}, "h2h_record": "FF: 1 - Draws: 0 - BW: 0 (100% vs 0%)", "prediction": "Fire Flux Esports FF", "confidence": 63.88016666666667, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - Soulfly most kills vs Darendeli (80% confidence) | Alternative: TOTAL_MAPS (75.74655555555556%)", "key_factors": ["🆚 H2H record: Fire Flux Esports FF leads (1-0) 🎯 (Similar rosters - high relevance)", "🏆 Ranking advantage: Fire Flux Esports FF (#108 vs #154)", "📈 ENSI advantage: Fire Flux Esports FF (1481 vs 1426)", "⚡ Better current shape: Fire Flux Esports FF (100.0% vs 90.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "FF: 1 - Draws: 0 - BW: 0 (100% vs 0%)", "team1_wins": 1, "team2_wins": 0, "draws": 0, "recent_matches": [": Bushido Wildcats 0:2 BW"], "team1_name": "FF", "team2_name": "BW", "team1_win_percentage": 100, "team2_win_percentage": 0, "competitive_encounters": 1, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 BtcTurk GameFest", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.26, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "Soulfly", "nationality": "", "kd_ratio": 1.26}, {"name": "paz", "nationality": "", "kd_ratio": 0.98}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.97}, {"name": "ScrunK", "nationality": "", "kd_ratio": 0.88}, {"name": "duggy", "nationality": "", "kd_ratio": 0.87}], "team2_players": [{"name": "Cizzx", "nationality": "", "kd_ratio": 0.81}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "vej", "nationality": "", "kd_ratio": 1.0}, {"name": "cadnyx", "nationality": "", "kd_ratio": 1.0}, {"name": "rim3", "nationality": "", "kd_ratio": 1.0}], "team1_avg_kd": 0.99, "team2_avg_kd": 0.96}, "recent_performance": {"team1_recent_matches": [{"score": "0:2", "result": "L", "opponent": "Fire Flux Esports\nFF\n\n\n\n\n\n\n\n\n\n\n\n            All FF and BW Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Fire Flux Esports\nFF\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Fire Flux Esports\nFF\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "<PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "RUBY\nRUBY\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:0", "result": "W", "opponent": "38\n\n\n\n\n\n\nBushido Wildcats\nBW", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "18\n\n\n\n\n\n\nEternal Fire\nEF", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "04\n\n\n\n\n\n\nFire Flux Esports\nFF", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "45\n\n\n\n\n\n\nFire Flux Esports\nFF", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "03\n\n\n\n\n\n\nAstrum\nAstrum", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 9, "losses": 11}, "team2_recent_form": {"wins": 9, "losses": 7}, "team1_opponents": ["Fir", "Fir", "Fir", "Mar", "RUB", "Fir", "Bus", "Bus", "Bus", "Bus", "Fir", "Bus", "Fir", "Fir", "Mar", "RUB", "Bus", "Bus", "Bus", "Bus"], "team2_opponents": ["Aca", "Bus", "Ete", "Fir", "Fir", "Ast", "Inn", "K27"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 13, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Bushido Wildcats", "Fire Flux Esports", "Eternal Fire", "<PERSON>", "RUBY", "Astrum", "Sangal Academy", "Sangal.A", "Inner Circle Esports", "ICE", "K27", "Wildcard Academy", "WC.Acad"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Soulfly": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 126.0}, "paz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "Banjo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "ScrunK": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}, "duggy": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}}, "team2_form_trends": {"Cizzx": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 81.0}, "Darendeli": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "vej": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "cadnyx": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "rim3": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "duggy", "player1_kd": 0.87, "player2": "Cizzx", "player2_kd": 0.81, "impact": "MEDIUM", "description": "Tactical battle: dug<PERSON> vs Cizzx"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"Soulfly": {"team": "Fire Flux Esports FF", "kd_ratio": 1.26, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Fire Flux Esports FF to win match", "confidence": 53.38016666666667, "reasoning": ["Team strength difference: 2.3", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Bushido Wildcats BW Btct 20 06 25 +1.5 maps (avoid 0-2 loss)", "confidence": 75, "reasoning": ["Based on team strength difference: 2.3"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 75.74655555555556, "reasoning": ["Team strength analysis: 2.3 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Fire Flux Esports FF first map", "confidence": 61.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Soulfly most kills vs Darendeli", "confidence": 80, "reasoning": ["K/D comparison: 1.26 vs 1.00"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1377481-fire-flux-esports-ff-vs-bushido-wildcats-bw-btct-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:52:38.212902"}}, "page_content": ""}, {"team1": {"name": "Genone Gone", "ranking": 92, "ensi_score": 1529, "winrate_10": 60.0, "winrate_30": 63.0, "current_shape": 97.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "Volt Volt", "ranking": 299, "ensi_score": 1339, "winrate_10": 50.0, "winrate_30": 40.0, "current_shape": 110.0, "avg_kd": 0.92, "players": [{"name": "Licale", "nationality": "Sweden", "kd_ratio": 1.03}, {"name": "JBOEN", "nationality": "Denmark", "kd_ratio": 1.02}, {"name": "kory", "nationality": "", "kd_ratio": 0.9}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.85}, {"name": "kwezz", "nationality": "Denmark", "kd_ratio": 0.79}]}, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "prediction": "Genone Gone", "confidence": 58.54391666666667, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - Volt Volt +1.5 maps (avoid 0-2 loss) (74.30405555555555% confidence) | Alternative: PLAYER_PROPS (72.99999999999999%)", "key_factors": ["📈 Better recent form: Genone Gone (17W-4L vs 4W-14L)", "🏆 Ranking advantage: <PERSON><PERSON> (#92 vs #299)", "📈 ENSI advantage: Genone Gone (1529 vs 1339)", "⚡ Better current shape: Volt Volt (97.0% vs 110.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 0, "h2h_record": "Team1: 0 - Draws: 0 - Team2: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 0, "recent_matches": [], "team1_name": "", "team2_name": "", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 European Pro League Season 28: Division 2", "h2h_history": [{"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}, {"score": "2:0", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 4}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.16, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "Unknown Tournament", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 1.16}, {"name": "B<PERSON>x<PERSON>", "nationality": "", "kd_ratio": 1.0}, {"name": "Tarkky", "nationality": "", "kd_ratio": 1.0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.87}, {"name": "SLIE9000", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "Licale", "nationality": "Sweden", "kd_ratio": 1.03}, {"name": "JBOEN", "nationality": "Denmark", "kd_ratio": 1.02}, {"name": "kory", "nationality": "", "kd_ratio": 0.9}, {"name": "<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.85}, {"name": "kwezz", "nationality": "Denmark", "kd_ratio": 0.79}], "team1_avg_kd": 1.01, "team2_avg_kd": 0.92}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "los kogutos\nLK\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Ex", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "GenOne\nGOne\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Kubix Esports\nkubix\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "GenOne\nGOne\n\n\n\n\n\n\n\n\n\n\n                    All GOne Encounters\n                \n\n\n\n\nVolt\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "34\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "51\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "17\n\n\n\n\n\n\nPrestige\nPrestige", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "50\n\n\n\n\n\n\nGenOne\nGOne", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "35\n\n\n\n\n\n\nAMKAL\nAMKAL", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 17, "losses": 4}, "team2_recent_form": {"wins": 4, "losses": 14}, "team1_opponents": ["los", "Gen", "<PERSON><PERSON>", "Gen", "Vol", "<PERSON><PERSON>", "Vol", "Vol", "MAS", "MAS", "Gen", "los", "Gen", "<PERSON><PERSON>", "Gen", "Vol", "<PERSON><PERSON>", "Vol", "Vol", "MAS"], "team2_opponents": ["Gen", "Pre", "Gen", "AMK", "Vol", "Fis", "Pre"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 15, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Prestige", "Ex-<PERSON><PERSON><PERSON>", "Ex-<PERSON><PERSON>", "GenOne", "GOne", "los kogutos", "Kubix Esports", "kubix", "AMKAL", "XI Esport", "Volt", "Anonymo Esports", "Anonymo", "Fisher College", "MASONIC"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"Chucky": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 115.99999999999999}, "Brooxsy": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "Tarkky": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "cHeuuuuk": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}, "SLIE9000": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"Licale": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 103.0}, "JBOEN": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "kory": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}, "Maze": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 85.0}, "kwezz": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "player1_kd": 0.87, "player2": "kwezz", "player2_kd": 0.79, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vs kwezz"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Genone Gone to win match", "confidence": 58.54391666666667, "reasoning": ["Team strength difference: 5.7", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Volt Volt +1.5 maps (avoid 0-2 loss)", "confidence": 74.30405555555555, "reasoning": ["Based on team strength difference: 5.7"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 72.30405555555555, "reasoning": ["Team strength analysis: 5.7 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "<PERSON><PERSON> most kills vs Li<PERSON>", "confidence": 72.99999999999999, "reasoning": ["K/D comparison: 1.16 vs 1.03"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1384803-genone-gone-vs-volt-volt-untd21-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:53:00.288739"}}, "page_content": ""}, {"team1": {"name": "CYBERSHOKE", "ranking": 60, "ensi_score": 1606, "winrate_10": 60.0, "winrate_30": 57.0, "current_shape": 103.0, "avg_kd": 1.06, "players": [{"name": "FenomeN", "nationality": "", "kd_ratio": 1.18}, {"name": "glowiing", "nationality": "", "kd_ratio": 1.07}, {"name": "lov1kus", "nationality": "", "kd_ratio": 1.02}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.02}, {"name": "bl1x1", "nationality": "", "kd_ratio": 1.0}]}, "team2": {"name": "Astrum", "ranking": 73, "ensi_score": 1574, "winrate_10": 40.0, "winrate_30": 53.0, "current_shape": 87.0, "avg_kd": 1.07, "players": [{"name": "gr1ks", "nationality": "", "kd_ratio": 1.45}, {"name": "clax", "nationality": "", "kd_ratio": 1.11}, {"name": "KENSI", "nationality": "", "kd_ratio": 0.96}, {"name": "patsi", "nationality": "", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.88}]}, "h2h_record": "CS: 1 - Draws: 0 - Astrum: 2 (33% vs 67%)", "prediction": "CYBERSHOKE", "confidence": 55.036833333333334, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - gr1ks most kills vs FenomeN (80% confidence) | Alternative: MAP_HANDICAP (74.30877777777778%)", "key_factors": ["🆚 H2H record: <PERSON><PERSON><PERSON> leads (2-1) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: CYBERSHOKE (15W-7L vs 6W-18L)", "🏆 Ranking advantage: CYBERSHOKE (#60 vs #73)", "⚡ Better current shape: CYBERSHOKE (103.0% vs 87.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 3, "h2h_record": "CS: 1 - Draws: 0 - Astrum: 2 (33% vs 67%)", "team1_wins": 1, "team2_wins": 2, "draws": 0, "recent_matches": [": CYBERSHOKE Esports 2:1 CS", ": CYBERSHOKE Esports 0:2 CS", ": Astrum 2:0 Astrum"], "team1_name": "CS", "team2_name": "Astrum", "team1_win_percentage": 33, "team2_win_percentage": 67, "competitive_encounters": 3, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 3 European Series #3", "h2h_history": [{"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}, {"score": "2:1", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "FenomeN", "nationality": "", "kd_ratio": 1.18}, {"name": "glowiing", "nationality": "", "kd_ratio": 1.07}, {"name": "lov1kus", "nationality": "", "kd_ratio": 1.02}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.02}, {"name": "bl1x1", "nationality": "", "kd_ratio": 1.0}], "team2_players": [{"name": "gr1ks", "nationality": "", "kd_ratio": 1.45}, {"name": "clax", "nationality": "", "kd_ratio": 1.11}, {"name": "KENSI", "nationality": "", "kd_ratio": 0.96}, {"name": "patsi", "nationality": "", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON>", "nationality": "", "kd_ratio": 0.88}], "team1_avg_kd": 1.06, "team2_avg_kd": 1.07}, "recent_performance": {"team1_recent_matches": [{"score": "2:1", "result": "W", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "CYBERSHOKE Esports\nCS\n\n\n\n\n\n\n\n\n\n\n\n            All CS and Astrum Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "CYBERSHOKE Esports\nCS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Zero Tenacity\nZ10\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "1:2", "result": "L", "opponent": "07\n\n\n\n\n\n\nCYBERSHOKE Esports\nCS", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "54\n\n\n\n\n\n\nCYBERSHOKE Esports\nCS", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "48\n\n\n\n\n\n\nAstrum\nAstrum", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "43\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "47\n\n\n\n\n\n\nBetclic\nBetclic", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 15, "losses": 7}, "team2_recent_form": {"wins": 6, "losses": 18}, "team1_opponents": ["Ast", "Ast", "CYB", "CYB", "<PERSON><PERSON>", "Sas", "CYB", "Nex", "FAV", "<PERSON><PERSON>", "RUB", "CYB", "Ast", "Ast", "RUB", "CYB", "<PERSON><PERSON>", "Sas", "Nex", "FAV", "<PERSON><PERSON>", "RUB"], "team2_opponents": ["CYB", "CYB", "Ast", "<PERSON>n", "Bet", "CYB", "<PERSON><PERSON>", "<PERSON>n", "Ast", "Ast", "Ast"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 18, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["CYBERSHOKE Esports", "Astrum", "Nexus Gaming", "Nexus", "Zero Tenacity", "Z10", "Sashi Esport", "<PERSON><PERSON>", "Passion UA", "Iberian Soul", "Ninjas in Pyjamas", "NiP", "Betclic", "FAVBET Team", "FAVBET", "Copenhagen Wolves", "CPHW", "RUBY"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"FenomeN": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 118.0}, "glowiing": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "lov1kus": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "notineki": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "bl1x1": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}}, "team2_form_trends": {"gr1ks": {"trend": "IMPROVING", "confidence": 85, "recent_matches": 5, "performance_rating": 145.0}, "clax": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "KENSI": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "patsi": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "Norwi": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 88.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "bl1x1", "player1_kd": 1.0, "player2": "<PERSON><PERSON>", "player2_kd": 0.88, "impact": "MEDIUM", "description": "Tactical battle: bl1x1 vs <PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"gr1ks": {"team": "Astrum", "kd_ratio": 1.45, "impact_level": "HIGH", "recent_form": "IMPROVING"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "CYBERSHOKE to win match", "confidence": 58.536833333333334, "reasoning": ["Team strength difference: 5.7", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Astrum +1.5 maps (avoid 0-2 loss)", "confidence": 74.30877777777778, "reasoning": ["Based on team strength difference: 5.7"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 72.30877777777778, "reasoning": ["Team strength analysis: 5.7 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "CYBERSHOKE first map", "confidence": 62.5, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "gr1ks most kills vs FenomeN", "confidence": 80, "reasoning": ["K/D comparison: 1.18 vs 1.45"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378187-cybershoke-esports-cs-vs-astrum-astrum-gb-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:53:21.966509"}}, "page_content": ""}, {"team1": {"name": "Fnatic FNC", "ranking": 28, "ensi_score": 1764, "winrate_10": 80.0, "winrate_30": 63.0, "current_shape": 117.0, "avg_kd": 1.05, "players": [{"name": "blameF", "nationality": "Denmark", "kd_ratio": 1.3}, {"name": "Jambo", "nationality": "", "kd_ratio": 1.11}, {"name": "KRiMZ", "nationality": "Sweden", "kd_ratio": 0.98}, {"name": "fear", "nationality": "", "kd_ratio": 0.94}, {"name": "MATYS", "nationality": "", "kd_ratio": 0.94}]}, "team2": {"name": "OG Gaming", "ranking": 30, "ensi_score": 1756, "winrate_10": 50.0, "winrate_30": 63.0, "current_shape": 87.0, "avg_kd": 0.99, "players": [{"name": "spooke", "nationality": "Sweden", "kd_ratio": 1.09}, {"name": "nicoodoz", "nationality": "Denmark", "kd_ratio": 1.07}, {"name": "Buzz", "nationality": "Denmark", "kd_ratio": 0.98}, {"name": "F1KU", "nationality": "", "kd_ratio": 0.92}, {"name": "Chr1zN", "nationality": "Denmark", "kd_ratio": 0.89}]}, "h2h_record": "FNC: 5 - Draws: 0 - OG: 6 (45% vs 55%)", "prediction": "Fnatic FNC", "confidence": 70.14172727272728, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - blameF most kills vs spooke (80% confidence) | Alternative: MATCH_WINNER (75.90536363636365%)", "key_factors": ["🆚 H2H record: OG Gaming leads (6-5) ⚡ (Some roster changes - moderate relevance)", "📈 Better recent form: Fnatic FNC (18W-9L vs 10W-14L)", "⚡ Better current shape: Fnatic FNC (117.0% vs 87.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 11, "h2h_record": "FNC: 5 - Draws: 0 - OG: 6 (45% vs 55%)", "team1_wins": 5, "team2_wins": 6, "draws": 0, "recent_matches": [": Fnatic 1:2 FNC", ": Fnatic 2:1 FNC", ": Fnatic 2:0 FNC", ": OG Gaming 0:2 OG"], "team1_name": "FNC", "team2_name": "OG", "team1_win_percentage": 45, "team2_win_percentage": 55, "competitive_encounters": 11, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 2 European Series #20", "h2h_history": [{"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}, {"score": "1:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.11, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "blameF", "nationality": "Denmark", "kd_ratio": 1.3}, {"name": "Jambo", "nationality": "", "kd_ratio": 1.11}, {"name": "KRiMZ", "nationality": "Sweden", "kd_ratio": 0.98}, {"name": "fear", "nationality": "", "kd_ratio": 0.94}, {"name": "MATYS", "nationality": "", "kd_ratio": 0.94}], "team2_players": [{"name": "spooke", "nationality": "Sweden", "kd_ratio": 1.09}, {"name": "nicoodoz", "nationality": "Denmark", "kd_ratio": 1.07}, {"name": "Buzz", "nationality": "Denmark", "kd_ratio": 0.98}, {"name": "F1KU", "nationality": "", "kd_ratio": 0.92}, {"name": "Chr1zN", "nationality": "Denmark", "kd_ratio": 0.89}], "team1_avg_kd": 1.05, "team2_avg_kd": 0.99}, "recent_performance": {"team1_recent_matches": [{"score": "1:2", "result": "L", "opponent": "OG Gaming\nOG\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "OG Gaming\nOG\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "OG Gaming\nOG\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Fnatic\nFNC\n\n\n\n\n\n\n\n\n\n\n\n            All FNC and OG Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Zero Tenacity\nZ10\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:1", "result": "W", "opponent": "17\n\n\n\n\n\n\nFnatic\nFNC", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "00\n\n\n\n\n\n\nFnatic\nFNC", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "36\n\n\n\n\n\n\nFnatic\nFNC", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "24\n\n\n\n\n\n\nOG Gaming\nOG", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "06\n\n\n\n\n\n\nFnatic\nFNC", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 18, "losses": 9}, "team2_recent_form": {"wins": 10, "losses": 14}, "team1_opponents": ["Fna", "<PERSON><PERSON>", "Pas", "Fna", "Pas", "RUB", "Tea", "NRG", "Fna", "NRG", "Pas", "<PERSON><PERSON>", "Pas", "Pas", "RUB", "Tea", "NRG"], "team2_opponents": ["Pro", "Fna", "Fna", "Fna", "Fna", "<PERSON>n", "M80"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 18, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Fnatic", "FNC", "OG Gaming", "9INE", "Zero Tenacity", "Z10", "Dynamo Eclot", "Eclot", "Passion UA", "Ninjas in Pyjamas", "NiP", "RUBY", "Team Falcons", "Falcons", "M80", "Virtus.Pro", "NRG Esports", "NRG"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"blameF": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 130.0}, "Jambo": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 111.00000000000001}, "KRiMZ": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "fear": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}, "MATYS": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 94.0}}, "team2_form_trends": {"spooke": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 109.00000000000001}, "nicoodoz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "Buzz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "F1KU": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "Chr1zN": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 89.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "fear", "player1_kd": 0.94, "player2": "Chr1zN", "player2_kd": 0.89, "impact": "MEDIUM", "description": "Tactical battle: fear vs Chr1zN"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"blameF": {"team": "Fnatic FNC", "kd_ratio": 1.3, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Fnatic FNC to win match", "confidence": 75.90536363636365, "reasoning": ["Team strength difference: 13.9", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "OG Gaming +1.5 maps (avoid 0-2 loss)", "confidence": 66.0630909090909, "reasoning": ["Based on team strength difference: 13.9"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (competitive series)", "confidence": 60, "reasoning": ["Team strength analysis: 13.9 difference"]}, "CORRECT_SCORE": {"prediction": "Fnatic FNC 2-1", "confidence": 70, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Fnatic FNC first map", "confidence": 70, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "blameF most kills vs spooke", "confidence": 80, "reasoning": ["K/D comparison: 1.30 vs 1.09"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1365013-fnatic-fnc-vs-og-gaming-og-epg-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:53:44.118097"}}, "page_content": ""}, {"team1": {"name": "9ine", "ranking": 61, "ensi_score": 1603, "winrate_10": 30.0, "winrate_30": 50.0, "current_shape": 80.0, "avg_kd": 1.06, "players": [{"name": "faveN", "nationality": "", "kd_ratio": 1.24}, {"name": "MoDo", "nationality": "", "kd_ratio": 1.22}, {"name": "bobeksde", "nationality": "Sweden", "kd_ratio": 1.01}, {"name": "r<PERSON>z", "nationality": "Denmark", "kd_ratio": 0.93}, {"name": "kraghen", "nationality": "Denmark", "kd_ratio": 0.9}]}, "team2": {"name": "Rebels Gaming", "ranking": 93, "ensi_score": 1522, "winrate_10": 60.0, "winrate_30": 53.0, "current_shape": 107.0, "avg_kd": 1.01, "players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.22}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "Sobol", "nationality": "", "kd_ratio": 0.98}, {"name": "innocent", "nationality": "", "kd_ratio": 0.96}, {"name": "Qlocu<PERSON>", "nationality": "", "kd_ratio": 0.81}]}, "h2h_record": "9INE: 2 - Draws: 0 - REBELS: 2 (50% vs 50%)", "prediction": "Rebels Gaming", "confidence": 56.08175, "betting_advice": "🟢 BEST BET: MAP_HANDICAP - 9ine +1.5 maps (avoid 0-2 loss) (75% confidence) | Alternative: TOTAL_MAPS (73.94550000000001%)", "key_factors": ["🆚 H2H record: Even (2-2) 🎯 (Similar rosters - high relevance)", "🤝 No common opponents in recent period (60 days)", "🏆 Ranking advantage: 9ine (#61 vs #93)", "📈 ENSI advantage: 9ine (1603 vs 1522)", "⚡ Better current shape: Rebels Gaming (80.0% vs 107.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 4, "h2h_record": "9INE: 2 - Draws: 0 - REBELS: 2 (50% vs 50%)", "team1_wins": 2, "team2_wins": 2, "draws": 0, "recent_matches": [": Rebels Gaming 0:2 REBELS", ": 9INE 2:0 9INE", ": Rebels Gaming 2:1 REBELS", ": Rebels Gaming 2:1 REBELS"], "team1_name": "9INE", "team2_name": "REBELS", "team1_win_percentage": 50, "team2_win_percentage": 50, "competitive_encounters": 4, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 PGL Bucharest", "h2h_history": [{"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}, {"score": "0:2", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "unknown", "count": 5}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.24, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "faveN", "nationality": "", "kd_ratio": 1.24}, {"name": "MoDo", "nationality": "", "kd_ratio": 1.22}, {"name": "bobeksde", "nationality": "Sweden", "kd_ratio": 1.01}, {"name": "r<PERSON>z", "nationality": "Denmark", "kd_ratio": 0.93}, {"name": "kraghen", "nationality": "Denmark", "kd_ratio": 0.9}], "team2_players": [{"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.22}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.07}, {"name": "Sobol", "nationality": "", "kd_ratio": 0.98}, {"name": "innocent", "nationality": "", "kd_ratio": 0.96}, {"name": "Qlocu<PERSON>", "nationality": "", "kd_ratio": 0.81}], "team1_avg_kd": 1.06, "team2_avg_kd": 1.01}, "recent_performance": {"team1_recent_matches": [{"score": "0:2", "result": "L", "opponent": "9INE\n9INE\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Rebels Gaming\nREBELS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "9INE\n9INE\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "9INE\n9INE\n\n\n\n\n\n\n\n\n\n\n\n            All 9INE and RE<PERSON>LS Encounters", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Iberian Soul\nIS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "2:0", "result": "W", "opponent": "58\n\n\n\n\n\n\nRebels Gaming\nREBELS", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "47\n\n\n\n\n\n\n9INE\n9INE", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "44\n\n\n\n\n\n\nRebels Gaming\nREBELS", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "57\n\n\n\n\n\n\nRebels Gaming\nREBELS", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "11\n\n\n\n\n\n\n9INE\n9INE", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 10, "losses": 13}, "team2_recent_form": {"wins": 13, "losses": 10}, "team1_opponents": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pas", "los", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pas", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pas", "los", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "team2_opponents": ["<PERSON><PERSON>", "<PERSON><PERSON>", "TEA", "<PERSON><PERSON>", "<PERSON><PERSON>", "TPu", "RUB"]}, "common_opponents": {"has_common_opponents": false, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 0, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": []}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"faveN": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 124.0}, "MoDo": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 122.0}, "bobeksde": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 101.0}, "raalz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 93.0}, "kraghen": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 90.0}}, "team2_form_trends": {"Flayy": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 122.0}, "kisserek": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 107.0}, "Sobol": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 98.0}, "innocent": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "Qlocuu": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 81.0}}, "key_matchups": [{"type": "STAR_VS_STAR", "player1": "faveN", "player1_kd": 1.24, "player2": "<PERSON><PERSON><PERSON>", "player2_kd": 1.22, "impact": "VERY_HIGH", "description": "Battle of star players: <PERSON>ave<PERSON> vs <PERSON><PERSON><PERSON>"}, {"type": "IGL_BATTLE", "player1": "kraghen", "player1_kd": 0.9, "player2": "Qlocu<PERSON>", "player2_kd": 0.81, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON><PERSON>"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"faveN": {"team": "9ine", "kd_ratio": 1.24, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "MoDo": {"team": "9ine", "kd_ratio": 1.22, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}, "Flayy": {"team": "Rebels Gaming", "kd_ratio": 1.22, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Rebels Gaming to win match", "confidence": 56.08175, "reasoning": ["Team strength difference: 4.1", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "9ine +1.5 maps (avoid 0-2 loss)", "confidence": 75, "reasoning": ["Based on team strength difference: 4.1"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 73.94550000000001, "reasoning": ["Team strength analysis: 4.1 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "Rebels Gaming first map", "confidence": 66.0, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "Close matchup: faveN vs Flayy", "confidence": 55, "reasoning": ["K/D comparison: 1.24 vs 1.22"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1378188-9ine-9ine-vs-rebels-gaming-rebels-gb-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:54:06.395096"}}, "page_content": ""}, {"team1": {"name": "Ninjas IN Pyjamas NIP", "ranking": 35, "ensi_score": 1731, "winrate_10": 70.0, "winrate_30": 70.0, "current_shape": 100.0, "avg_kd": 1.04, "players": [{"name": "R1nkle", "nationality": "", "kd_ratio": 1.27}, {"name": "arrozdoce", "nationality": "", "kd_ratio": 1.06}, {"name": "ew<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.04}, {"name": "s<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.87}]}, "team2": {"name": "Iberian Soul IS", "ranking": 44, "ensi_score": 1691, "winrate_10": 80.0, "winrate_30": 60.0, "current_shape": 120.0, "avg_kd": 0.94, "players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}]}, "h2h_record": "NiP: 0 - Draws: 1 - IS: 0 (0% vs 0%)", "prediction": "Iberian Soul IS", "confidence": 54.7685, "betting_advice": "🟢 BEST BET: PLAYER_PROPS - R1nkle most kills vs sausol (80% confidence) | Alternative: MAP_HANDICAP (75%)", "key_factors": ["🆚 H2H record: Even (0-0) 🎯 (Similar rosters - high relevance)", "📈 Better recent form: Ninjas IN Pyjamas NIP (12W-6L vs 8W-10L)", "⚡ Better current shape: Iberian Soul IS (100.0% vs 120.0%)"], "additional_factors": {"h2h_data": {"previous_encounters": 1, "h2h_record": "NiP: 0 - Draws: 1 - IS: 0 (0% vs 0%)", "team1_wins": 0, "team2_wins": 0, "draws": 1, "recent_matches": [], "team1_name": "NiP", "team2_name": "IS", "team1_win_percentage": 0, "team2_win_percentage": 0, "competitive_encounters": 0, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-3", "tournament": "2025 CCT Season 2 European Series #18", "h2h_history": [{"score": ":", "context": "recent_match"}, {"score": ":", "context": "recent_match"}, {"score": ":", "context": "recent_match"}, {"score": ":", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 2}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.27, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "R1nkle", "nationality": "", "kd_ratio": 1.27}, {"name": "arrozdoce", "nationality": "", "kd_ratio": 1.06}, {"name": "ew<PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 1.04}, {"name": "s<PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.96}, {"name": "<PERSON><PERSON><PERSON>", "nationality": "Denmark", "kd_ratio": 0.87}], "team2_players": [{"name": "sausol", "nationality": "", "kd_ratio": 1.02}, {"name": "stadodo", "nationality": "", "kd_ratio": 1.0}, {"name": "mopoz", "nationality": "", "kd_ratio": 0.97}, {"name": "ALEX", "nationality": "", "kd_ratio": 0.92}, {"name": "dav1g", "nationality": "", "kd_ratio": 0.79}], "team1_avg_kd": 1.04, "team2_avg_kd": 0.94}, "recent_performance": {"team1_recent_matches": [{"score": "2:0", "result": "W", "opponent": "CYBERSHOKE Esports\nCS\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Astrum\nAstrum\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "ECSTATIC\nECSTATIC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Fnatic\nFNC\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "Metizport\nMetz\n\n\n\n\n\n\n\n\n\n\n                    All NiP Encounters\n                \n\n\n\n\nIberian Soul\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "0:2", "result": "L", "opponent": "43\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "34\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "56\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "05\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "05\n\n\n\n\n\n\nNinjas in Pyjamas\nNiP", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 12, "losses": 6}, "team2_recent_form": {"wins": 8, "losses": 10}, "team1_opponents": ["CYB", "Ast", "ECS", "Fna", "Met", "<PERSON><PERSON>", "Ast", "Mon", "Mon", "Met", "CYB", "Ast", "ECS", "Fna", "Met", "<PERSON><PERSON>", "Ast", "Mon"], "team2_opponents": ["<PERSON>n", "<PERSON>n", "<PERSON>n", "<PERSON>n", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 17, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["Ninjas in Pyjamas", "NiP", "Iberian Soul", "CYBERSHOKE Esports", "Sashi Esport", "<PERSON><PERSON>", "Astrum", "Dynamo Eclot", "Eclot", "ECSTATIC", "Fnatic", "FNC", "Metizport", "Metz", "500", "9INE", "Monte"]}, "map_statistics": {"likely_first_map": "mirage", "map_pool_analysis": {}, "veto_predictions": {}, "team_map_preferences": {}, "team1_mirage_winrate": 50, "team2_mirage_winrate": 50, "mirage_h2h_record": {"team1": 0, "team2": 0}, "team1_inferno_winrate": 50, "team2_inferno_winrate": 50, "inferno_h2h_record": {"team1": 0, "team2": 0}, "team1_dust2_winrate": 50, "team2_dust2_winrate": 50, "dust2_h2h_record": {"team1": 0, "team2": 0}, "team1_nuke_winrate": 50, "team2_nuke_winrate": 50, "nuke_h2h_record": {"team1": 0, "team2": 0}, "team1_overpass_winrate": 50, "team2_overpass_winrate": 50, "overpass_h2h_record": {"team1": 0, "team2": 0}, "team1_vertigo_winrate": 50, "team2_vertigo_winrate": 50, "vertigo_h2h_record": {"team1": 0, "team2": 0}, "team1_ancient_winrate": 50, "team2_ancient_winrate": 50, "ancient_h2h_record": {"team1": 0, "team2": 0}, "mirage_recent_form": {"team1_last_5": [], "team2_last_5": []}, "inferno_recent_form": {"team1_last_5": [], "team2_last_5": []}, "dust2_recent_form": {"team1_last_5": [], "team2_last_5": []}, "nuke_recent_form": {"team1_last_5": [], "team2_last_5": []}, "overpass_recent_form": {"team1_last_5": [], "team2_last_5": []}, "vertigo_recent_form": {"team1_last_5": [], "team2_last_5": []}, "ancient_recent_form": {"team1_last_5": [], "team2_last_5": []}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "player_analysis": {"team1_form_trends": {"R1nkle": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 127.0}, "arrozdoce": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 106.0}, "ewjerkz": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 104.0}, "sjuush": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 96.0}, "Snappi": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 87.0}}, "team2_form_trends": {"sausol": {"trend": "STABLE_AVERAGE", "confidence": 65, "recent_matches": 5, "performance_rating": 102.0}, "stadodo": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 100.0}, "mopoz": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 97.0}, "ALEX": {"trend": "STABLE_LOW", "confidence": 60, "recent_matches": 5, "performance_rating": 92.0}, "dav1g": {"trend": "DECLINING", "confidence": 55, "recent_matches": 5, "performance_rating": 79.0}}, "key_matchups": [{"type": "IGL_BATTLE", "player1": "<PERSON><PERSON><PERSON>", "player1_kd": 0.87, "player2": "dav1g", "player2_kd": 0.79, "impact": "MEDIUM", "description": "Tactical battle: <PERSON><PERSON><PERSON> vs dav1g"}], "roster_changes": {"team1_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}, "team2_changes": {"recent_changes": false, "new_players": [], "chemistry_impact": "NONE"}}, "injury_concerns": [], "star_player_analysis": {"R1nkle": {"team": "Ninjas IN Pyjamas NIP", "kd_ratio": 1.27, "impact_level": "MEDIUM", "recent_form": "STABLE_AVERAGE"}}}, "betting_markets": {"MATCH_WINNER": {"prediction": "Iberian Soul IS to win match", "confidence": 54.7685, "reasoning": ["Team strength difference: 3.2", "Primary betting market"]}, "MAP_HANDICAP": {"prediction": "Ninjas IN Pyjamas NIP +1.5 maps (avoid 0-2 loss)", "confidence": 75, "reasoning": ["Based on team strength difference: 3.2"]}, "TOTAL_MAPS": {"prediction": "OVER 2.5 maps (3 maps likely)", "confidence": 74.821, "reasoning": ["Team strength analysis: 3.2 difference"]}, "CORRECT_SCORE": {"prediction": "2-1 either team (close series)", "confidence": 55, "reasoning": ["Based on team strength and historical patterns"]}, "FIRST_MAP_WINNER": {"prediction": "First map too close to predict", "confidence": 50, "reasoning": ["First map based on individual skill and current form"]}, "FIRST_MAP_ROUNDS": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["First map not yet determined - wait for veto completion"]}, "PLAYER_PROPS": {"prediction": "R1nkle most kills vs sausol", "confidence": 80, "reasoning": ["K/D comparison: 1.27 vs 1.02"]}, "MATCH_DURATION": {"prediction": "NOT_AVAILABLE", "confidence": 0, "reasoning": ["Match duration betting not offered by bookmakers"]}}, "original_url": "https://ensigame.com/matches/cs-2/1365012-ninjas-in-pyjamas-nip-vs-iberian-soul-is-epg-20-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-20 00:54:28.978142"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 2800.0, "expected_return": 0, "risk_score": 60.0, "diversification_score": 100, "recommendations": [{"match": "500 500 vs Amkal Amkal CCT EU 20 06 25", "bet_type": "Moneyline", "recommendation": "Amkal Amkal CCT EU 20 06 25 to win match", "confidence": 84.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 23.1", "Primary betting market"]}, {"match": "Passion UA Passion UA vs Gun5 Esports Gun5 CCT EU 20 06 25", "bet_type": "Moneyline", "recommendation": "Passion UA Passion UA to win match", "confidence": 82.**************, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 21.4", "Primary betting market"]}, {"match": "500 500 vs Amkal Amkal CCT EU 20 06 25", "bet_type": "Map Handicap", "recommendation": "Amkal Amkal CCT EU 20 06 25 -1.5 maps (2-0 win)", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 23.1"]}, {"match": "Passion UA Passion UA vs Gun5 Esports Gun5 CCT EU 20 06 25", "bet_type": "Map Handicap", "recommendation": "Passion UA Passion UA -1.5 maps (2-0 win)", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 21.4"]}, {"match": "Betclic Apogee Esports vs Dynamo Eclot Eclot CCT EU 20 06 25", "bet_type": "Player Props", "recommendation": "FORSYY most kills vs jcobbb", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.13 vs 1.41"]}, {"match": "Jersa Jersa vs LP LP CCT SA 20 06 25", "bet_type": "Player Props", "recommendation": "happ most kills vs Profug", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 0.93 vs 1.22"]}, {"match": "Fire Flux Esports FF vs Bushido Wildcats BW Btct 20 06 25", "bet_type": "Player Props", "recommendation": "Soulfly most kills vs Darendeli", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.26 vs 1.00"]}, {"match": "CYBERSHOKE vs Astrum", "bet_type": "Player Props", "recommendation": "gr1ks most kills vs FenomeN", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.18 vs 1.45"]}, {"match": "Fnatic FNC vs OG Gaming", "bet_type": "Player Props", "recommendation": "blameF most kills vs spooke", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.30 vs 1.09"]}, {"match": "Ninjas IN Pyjamas NIP vs Iberian Soul IS", "bet_type": "Player Props", "recommendation": "R1nkle most kills vs sausol", "confidence": 80, "value_rating": 0.6000000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.27 vs 1.02"]}, {"match": "HEROIC Academy vs KS Esports", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 77.71572222222221, "value_rating": 0.5543144444444443, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 0.3 difference"]}, {"match": "Fnatic FNC vs OG Gaming", "bet_type": "Moneyline", "recommendation": "Fnatic FNC to win match", "confidence": 75.90536363636365, "value_rating": 0.5181072727272729, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 13.9", "Primary betting market"]}, {"match": "Fire Flux Esports FF vs Bushido Wildcats BW Btct 20 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 75.74655555555556, "value_rating": 0.5149311111111112, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 2.3 difference"]}, {"match": "500 500 vs Amkal Amkal CCT EU 20 06 25", "bet_type": "Correct Score", "recommendation": "Amkal Amkal CCT EU 20 06 25 2-0", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength and historical patterns"]}, {"match": "HEROIC Academy vs KS Esports", "bet_type": "Map Handicap", "recommendation": "HEROIC Academy +1.5 maps (avoid 0-2 loss)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 0.3"]}, {"match": "Passion UA Passion UA vs Gun5 Esports Gun5 CCT EU 20 06 25", "bet_type": "Correct Score", "recommendation": "Passion UA Passion UA 2-0", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength and historical patterns"]}, {"match": "Betclic Apogee Esports vs Dynamo Eclot Eclot CCT EU 20 06 25", "bet_type": "Map Handicap", "recommendation": "Betclic Apogee Esports +1.5 maps (avoid 0-2 loss)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 4.7"]}, {"match": "Jersa Jersa vs LP LP CCT SA 20 06 25", "bet_type": "Map Handicap", "recommendation": "Jersa Jersa +1.5 maps (avoid 0-2 loss)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 4.7"]}, {"match": "Fire Flux Esports FF vs Bushido Wildcats BW Btct 20 06 25", "bet_type": "Map Handicap", "recommendation": "Bushido Wildcats BW Btct 20 06 25 +1.5 maps (avoid 0-2 loss)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 2.3"]}, {"match": "9ine vs Rebels Gaming", "bet_type": "Map Handicap", "recommendation": "9ine +1.5 maps (avoid 0-2 loss)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 4.1"]}, {"match": "Ninjas IN Pyjamas NIP vs Iberian Soul IS", "bet_type": "Map Handicap", "recommendation": "Ninjas IN Pyjamas NIP +1.5 maps (avoid 0-2 loss)", "confidence": 75, "value_rating": 0.5, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 3.2"]}, {"match": "Ninjas IN Pyjamas NIP vs Iberian Soul IS", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 74.821, "value_rating": 0.49641999999999986, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 3.2 difference"]}, {"match": "CYBERSHOKE vs Astrum", "bet_type": "Map Handicap", "recommendation": "Astrum +1.5 maps (avoid 0-2 loss)", "confidence": 74.30877777777778, "value_rating": 0.4861755555555556, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 5.7"]}, {"match": "Genone Gone vs Volt Volt", "bet_type": "Map Handicap", "recommendation": "Volt Volt +1.5 maps (avoid 0-2 loss)", "confidence": 74.30405555555555, "value_rating": 0.48608111111111096, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 5.7"]}, {"match": "9ine vs Rebels Gaming", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 73.94550000000001, "value_rating": 0.47891000000000017, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 4.1 difference"]}, {"match": "Betclic Apogee Esports vs Copenhagen Wolves", "bet_type": "Map Handicap", "recommendation": "Betclic Apogee Esports +1.5 maps (avoid 0-2 loss)", "confidence": 73.82994444444444, "value_rating": 0.4765988888888888, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 6.2"]}, {"match": "Jersa Jersa vs LP LP CCT SA 20 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 73.33444444444444, "value_rating": 0.46668888888888893, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 4.7 difference"]}, {"match": "Betclic Apogee Esports vs Dynamo Eclot Eclot CCT EU 20 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 73.25038888888889, "value_rating": 0.4650077777777779, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 4.7 difference"]}, {"match": "Genone Gone vs Volt Volt", "bet_type": "Player Props", "recommendation": "<PERSON><PERSON> most kills vs Li<PERSON>", "confidence": 72.99999999999999, "value_rating": 0.45999999999999974, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.16 vs 1.03"]}, {"match": "CYBERSHOKE vs Astrum", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 72.30877777777778, "value_rating": 0.44617555555555555, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 5.7 difference"]}, {"match": "Genone Gone vs Volt Volt", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 72.30405555555555, "value_rating": 0.44608111111111093, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 5.7 difference"]}, {"match": "Parivision vs Roler Coaster", "bet_type": "Map Handicap", "recommendation": "<PERSON><PERSON>er +1.5 maps (avoid 0-2 loss)", "confidence": 72.20833333333334, "value_rating": 0.4441666666666668, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 7.8"]}, {"match": "HEROIC Academy vs KS Esports", "bet_type": "Player Props", "recommendation": "tripey most kills vs fnl", "confidence": 72.00000000000001, "value_rating": 0.4400000000000004, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 0.99 vs 1.11"]}, {"match": "BIG BIG vs Zero Tenacity Z10 CCT EU 20 06 25", "bet_type": "Player Props", "recommendation": "hyped most kills vs <PERSON><PERSON><PERSON><PERSON>", "confidence": 71.99999999999999, "value_rating": 0.4399999999999997, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["K/D comparison: 1.24 vs 1.12"]}, {"match": "Betclic Apogee Esports vs Copenhagen Wolves", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 71.82994444444444, "value_rating": 0.43659888888888876, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 6.2 difference"]}, {"match": "BIG BIG vs Zero Tenacity Z10 CCT EU 20 06 25", "bet_type": "Map Handicap", "recommendation": "Zero Tenacity Z10 CCT EU 20 06 25 +1.5 maps (avoid 0-2 loss)", "confidence": 71.81166666666667, "value_rating": 0.43623333333333325, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 8.2"]}, {"match": "ESC Gaming vs Prestige Prestige", "bet_type": "Moneyline", "recommendation": "ESC Gaming to win match", "confidence": 71.37325, "value_rating": 0.427465, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength difference: 14.2", "Primary betting market"]}, {"match": "Parivision vs Roler Coaster", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (3 maps likely)", "confidence": 70.20833333333334, "value_rating": 0.4041666666666668, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 7.8 difference"]}, {"match": "500 500 vs Amkal Amkal CCT EU 20 06 25", "bet_type": "Total Rounds", "recommendation": "Amkal Amkal CCT EU 20 06 25 first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Jersa Jersa vs LP LP CCT SA 20 06 25", "bet_type": "Total Rounds", "recommendation": "LP LP CCT SA 20 06 25 first map", "confidence": 70.0, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Fnatic FNC vs OG Gaming", "bet_type": "Total Rounds", "recommendation": "Fnatic FNC first map", "confidence": 70, "value_rating": 0.3999999999999999, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "500 500 vs Amkal Amkal CCT EU 20 06 25", "bet_type": "Total Maps", "recommendation": "UNDER 2.5 maps (2-0 likely)", "confidence": 68.13794444444444, "value_rating": 0.36275888888888885, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 23.1 difference"]}, {"match": "ESC Gaming vs Prestige Prestige", "bet_type": "Total Rounds", "recommendation": "ESC Gaming first map", "confidence": 67.5, "value_rating": 0.3500000000000001, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Passion UA Passion UA vs Gun5 Esports Gun5 CCT EU 20 06 25", "bet_type": "Total Maps", "recommendation": "UNDER 2.5 maps (2-0 likely)", "confidence": 66.43511111111113, "value_rating": 0.32870222222222245, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 21.4 difference"]}, {"match": "Fnatic FNC vs OG Gaming", "bet_type": "Map Handicap", "recommendation": "OG Gaming +1.5 maps (avoid 0-2 loss)", "confidence": 66.0630909090909, "value_rating": 0.3212618181818181, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 13.9"]}, {"match": "9ine vs Rebels Gaming", "bet_type": "Total Rounds", "recommendation": "Rebels Gaming first map", "confidence": 66.0, "value_rating": 0.32000000000000006, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "ESC Gaming vs Prestige Prestige", "bet_type": "Map Handicap", "recommendation": "Prestige Prestige +1.5 maps (avoid 0-2 loss)", "confidence": 65.75116666666668, "value_rating": 0.31502333333333343, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Based on team strength difference: 14.2"]}, {"match": "CYBERSHOKE vs Astrum", "bet_type": "Total Rounds", "recommendation": "CYBERSHOKE first map", "confidence": 62.5, "value_rating": 0.25, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "BIG BIG vs Zero Tenacity Z10 CCT EU 20 06 25", "bet_type": "Total Rounds", "recommendation": "BIG BIG first map", "confidence": 62.0, "value_rating": 0.24, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Fire Flux Esports FF vs Bushido Wildcats BW Btct 20 06 25", "bet_type": "Total Rounds", "recommendation": "Fire Flux Esports FF first map", "confidence": 61.5, "value_rating": 0.22999999999999998, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Betclic Apogee Esports vs Dynamo Eclot Eclot CCT EU 20 06 25", "bet_type": "Total Rounds", "recommendation": "Dynamo Eclot Eclot CCT EU 20 06 25 first map", "confidence": 61.0, "value_rating": 0.21999999999999997, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Passion UA Passion UA vs Gun5 Esports Gun5 CCT EU 20 06 25", "bet_type": "Total Rounds", "recommendation": "Passion UA Passion UA first map", "confidence": 60.5, "value_rating": 0.20999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "Betclic Apogee Esports vs Copenhagen Wolves", "bet_type": "Total Rounds", "recommendation": "Copenhagen Wolves first map", "confidence": 60.5, "value_rating": 0.20999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["First map based on individual skill and current form"]}, {"match": "ESC Gaming vs Prestige Prestige", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 14.2 difference"]}, {"match": "BIG BIG vs Zero Tenacity Z10 CCT EU 20 06 25", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 8.2 difference"]}, {"match": "Fnatic FNC vs OG Gaming", "bet_type": "Total Maps", "recommendation": "OVER 2.5 maps (competitive series)", "confidence": 60, "value_rating": 0.19999999999999996, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 0, "risk_level": "MEDIUM", "reasoning": ["Team strength analysis: 13.9 difference"]}]}, "enhancement_stats": {"total_predictions": 15, "average_confidence": 62.96399848484849, "premium_bets": 0, "strong_bets": 1, "good_bets": 4, "lean_bets": 0, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 6.666666666666667, "good_pct": 26.666666666666668, "lean_pct": 0.0}}}