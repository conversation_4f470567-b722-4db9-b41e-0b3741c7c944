{"timestamp": "20250619_232731", "total_matches": 1, "successful_scrapes": 1, "failed_urls": [], "success_rate": 100.0, "processing_time": 22.**************, "min_confidence": 70.0, "bankroll": 1000.0, "predictions": [{"team1": {"name": "<PERSON><PERSON>", "ranking": 4, "ensi_score": 1956, "winrate_10": 60.0, "winrate_30": 63.0, "current_shape": 97.0, "avg_kd": 1.08, "players": [{"name": "B1t", "nationality": "", "kd_ratio": 1.3}, {"name": "w0nderful", "nationality": "", "kd_ratio": 1.13}, {"name": "iM", "nationality": "", "kd_ratio": 1.1}, {"name": "jL", "nationality": "", "kd_ratio": 1.07}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.81}]}, "team2": {"name": "Team Vitality Vitality", "ranking": 1, "ensi_score": 2211, "winrate_10": 90.0, "winrate_30": 97.0, "current_shape": 93.0, "avg_kd": 1.14, "players": [{"name": "ZywOo", "nationality": "", "kd_ratio": 1.6}, {"name": "ropz", "nationality": "", "kd_ratio": 1.28}, {"name": "mezii", "nationality": "", "kd_ratio": 1.11}, {"name": "flameZ", "nationality": "", "kd_ratio": 0.93}, {"name": "apEX", "nationality": "", "kd_ratio": 0.77}]}, "h2h_record": "Na'Vi: 11 - Draws: 0 - Vitality: 6 (65% vs 35%)", "prediction": "Team Vitality Vitality", "confidence": 65, "betting_advice": "🟢 BEST BET: MONEYLINE - GOOD BET: Team Vitality Vitality to WIN (65% confidence) | Alternative: total_rounds (83%)", "key_factors": ["🆚 H2H advantage: <PERSON><PERSON> (11-6 record)", "📈 ENSI advantage: Team Vitality Vitality (1956 vs 2211)"], "additional_factors": {"h2h_data": {"previous_encounters": 17, "h2h_record": "Na'Vi: 11 - Draws: 0 - Vitality: 6 (65% vs 35%)", "team1_wins": 11, "team2_wins": 6, "draws": 0, "recent_matches": [": Nat<PERSON> Vince<PERSON> 1:3 Na'Vi", ": Team Vitality 2:0 Vitality", ": Team Vitality 2:0 Vitality", ": Natus Vincere 0:2 Na'Vi"], "team1_name": "<PERSON><PERSON><PERSON><PERSON>", "team2_name": "Vitality", "team1_win_percentage": 65, "team2_win_percentage": 35, "competitive_encounters": 17, "data_source": "ensigame.com", "extraction_method": "compare-bar__caption parsing"}, "additional_data": {"format": "BO3", "tier": "Tier-1", "tournament": "2024 Intel Extreme Masters Cologne", "h2h_history": [{"score": "1:3", "context": "recent_match"}, {"score": "1:3", "context": "recent_match"}, {"score": "1:3", "context": "recent_match"}, {"score": "1:3", "context": "recent_match"}], "recent_form": {}, "map_performance": {}, "streak_data": {"type": "Win", "count": 1}, "prize_money": {}, "roster_stability": {"recent_changes": 2, "stability_score": 80}, "betting_odds": {"team1_odds": 1.13, "team2_odds": 2.0, "total_maps_over": 2.5, "total_maps_under": 1.5}, "market_confidence": {}}, "tournament_context": {"tournament_name": "eSlug", "tournament_type": "", "match_format": "", "elimination_pressure": false, "qualification_stakes": false, "prize_implications": {}, "match_importance": "medium", "stage": "", "series_context": {}}, "players": {"team1_players": [{"name": "B1t", "nationality": "", "kd_ratio": 1.3}, {"name": "w0nderful", "nationality": "", "kd_ratio": 1.13}, {"name": "iM", "nationality": "", "kd_ratio": 1.1}, {"name": "jL", "nationality": "", "kd_ratio": 1.07}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "nationality": "", "kd_ratio": 0.81}], "team2_players": [{"name": "ZywOo", "nationality": "", "kd_ratio": 1.6}, {"name": "ropz", "nationality": "", "kd_ratio": 1.28}, {"name": "mezii", "nationality": "", "kd_ratio": 1.11}, {"name": "flameZ", "nationality": "", "kd_ratio": 0.93}, {"name": "apEX", "nationality": "", "kd_ratio": 0.77}], "team1_avg_kd": 1.08, "team2_avg_kd": 1.14}, "recent_performance": {"team1_recent_matches": [{"score": "1:3", "result": "L", "opponent": "Team Vitality\nVitality\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Natus <PERSON>", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "Team Vitality\nVitality\n\n\n\n\n\n\n\n\n\n\n\n            All Na", "tournament": "Recent", "date": "Recent"}, {"score": "2:1", "result": "W", "opponent": "G2 Esports\nG2\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nTier", "tournament": "Recent", "date": "Recent"}, {"score": "0:1", "result": "L", "opponent": "Natus <PERSON>", "tournament": "Recent", "date": "Recent"}], "team2_recent_matches": [{"score": "3:1", "result": "W", "opponent": "Vi", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "22\n\n\n\n\n\n\nTeam Vitality\nVitality", "tournament": "Recent", "date": "Recent"}, {"score": "2:0", "result": "W", "opponent": "Vi", "tournament": "Recent", "date": "Recent"}, {"score": "1:2", "result": "L", "opponent": "Vi", "tournament": "Recent", "date": "Recent"}, {"score": "0:2", "result": "L", "opponent": "33\n\n\n\n\n\n\nTeam Spirit\nSpirit", "tournament": "Recent", "date": "Recent"}], "team1_recent_form": {"wins": 8, "losses": 12}, "team2_recent_form": {"wins": 9, "losses": 10}, "team1_opponents": ["Tea", "Nat", "Tea", "Nat", "Nem", "Ast", "Tea", "Tea", "Leg", "MOU", "Tea", "Tea", "MOU", "Nem", "Ast", "Tea", "Leg", "MOU"], "team2_opponents": ["Pro", "Tea", "Tea", "Tea", "Tea", "Tea"]}, "common_opponents": {"has_common_opponents": true, "common_matches": [], "team1_vs_common": [], "team2_vs_common": [], "strength_of_schedule": {}, "common_opponents_count": 18, "team1_common_winrate": 0.0, "team2_common_winrate": 0.0, "common_opponents_list": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Team Vitality", "Vitality", "Team Falcons", "Falcons", "The Mongolz", "Mongolz", "Team Spirit", "Spirit", "3DMAX", "Nemiga Gaming", "Nemiga", "G2 Esports", "<PERSON><PERSON><PERSON>", "Virtus.Pro", "Legacy", "MOUZ"]}, "map_statistics": {"map_pool": [], "team1_map_preferences": {}, "team2_map_preferences": {}, "banned_maps": [], "picked_maps": [], "map_winrates": {}}, "team_form_data": {"team1_streak": {"type": "none", "count": 0}, "team2_streak": {"type": "none", "count": 0}, "team1_last_5": [], "team2_last_5": [], "momentum_indicators": {}}, "betting_context": {"market_sentiment": {"found_data": true}, "public_picks": {}, "line_movement": {}, "betting_volume": {}}, "validation_results": {"core_stats": {"valid": true, "issues": []}, "h2h_data": {"valid": true, "issues": []}, "team_data": {"valid": true, "issues": []}}, "betting_markets": {"moneyline": {"prediction": "GOOD BET: Team Vitality Vitality to WIN", "confidence": 65, "reasoning": ["Very close teams: 2.3 points apart", "Recent form advantage: 30% gap", "Identical current form: 4% gap", "Top-3 players advantage: Team Vitality Vitality (0.153 K/D gap)", "Major ENSI advantage: Team Vitality Vitality (255 points)", "30-game form advantage: Team Vitality Vitality (34% gap)"], "risk_level": "Medium", "value_rating": 8}, "total_rounds": {"prediction": "OVER 20.5 avg rounds per map", "confidence": 83, "reasoning": ["Close teams (0.060 K/D diff)", "Multiple competitive maps expected", "BO3 format allows for long matches", "Very close rankings (#4 vs #1)", "High player skill variance: 0.249 (unstable rounds)", "H2H history shows competitive matches"], "risk_level": "Low", "value_rating": 9, "value_analysis": {"has_value": true, "value_percentage": 107.**************, "implied_probability": 40.0, "our_probability": 83, "recommendation": "STRONG VALUE", "stake_suggestion": "High (3-5% bankroll)"}, "live_factors": {"momentum_shift": "stable", "pressure_level": "medium", "upset_potential": 10, "market_sentiment": "neutral", "late_roster_changes": true, "time_until_match": "unknown"}}, "prop_bets": {"prediction": "ZywOo OVER 37.5 total kills (match)", "confidence": 70, "reasoning": ["Top K/D ratio: 1.60", "Expected ~38 kills across BO3", "Consistent fragger with 0.30 K/D edge", "Multi-map format favors consistent players"], "risk_level": "Medium", "value_rating": 7}}, "original_url": "https://ensigame.com/matches/cs-2/1369180-natus-vincere-navi-vs-team-vitality-vitality-blast-19-06-25", "cs2_mechanics_validated": {"rounds_to_win": 13, "max_regulation_rounds": 24, "overtime_trigger": "12-12", "validation_passed": true, "validation_timestamp": "2025-06-19 23:27:31.186653"}}, "page_content": ""}], "portfolio_summary": {"total_bankroll": 1000.0, "allocated_amount": 150.0, "expected_return": 57.***************, "risk_score": 60.0, "diversification_score": 90, "recommendations": [{"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Total Rounds", "recommendation": "OVER 20.5 avg rounds per map", "confidence": 83, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 28.85, "risk_level": "MEDIUM", "reasoning": ["Close teams (0.060 K/D diff)", "Multiple competitive maps expected", "BO3 format allows for long matches", "Very close rankings (#4 vs #1)", "High player skill variance: 0.249 (unstable rounds)", "H2H history shows competitive matches"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Prop Bet", "recommendation": "ZywOo OVER 37.5 total kills (match)", "confidence": 70, "value_rating": 0.****************, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 16.***************, "risk_level": "MEDIUM", "reasoning": ["Top K/D ratio: 1.60", "Expected ~38 kills across BO3", "Consistent fragger with 0.30 K/D edge", "Multi-map format favors consistent players"]}, {"match": "<PERSON><PERSON> vs Team Vitality Vitality", "bet_type": "Moneyline", "recommendation": "GOOD BET: Team Vitality Vitality to WIN", "confidence": 65, "value_rating": 0.30000000000000004, "kelly_fraction": 0.05, "suggested_stake": 50.0, "expected_value": 11.75, "risk_level": "MEDIUM", "reasoning": ["Very close teams: 2.3 points apart", "Recent form advantage: 30% gap", "Identical current form: 4% gap", "Top-3 players advantage: Team Vitality Vitality (0.153 K/D gap)", "Major ENSI advantage: Team Vitality Vitality (255 points)", "30-game form advantage: Team Vitality Vitality (34% gap)"]}]}, "enhancement_stats": {"total_predictions": 1, "average_confidence": 65.0, "premium_bets": 0, "strong_bets": 0, "good_bets": 0, "lean_bets": 1, "confidence_distribution": {"premium_pct": 0.0, "strong_pct": 0.0, "good_pct": 0.0, "lean_pct": 100.0}}}